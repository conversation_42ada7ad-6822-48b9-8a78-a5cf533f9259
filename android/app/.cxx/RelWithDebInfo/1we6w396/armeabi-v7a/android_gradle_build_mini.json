{"buildFiles": ["/Users/<USER>/.shorebird/bin/cache/flutter/8c1a3ee6d9319bd5c202f73972a6d03bba335402/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/development/project/sport_mobile/android/app/.cxx/RelWithDebInfo/1we6w396/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/development/project/sport_mobile/android/app/.cxx/RelWithDebInfo/1we6w396/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}