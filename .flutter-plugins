# This is a generated file; do not edit or check into version control.
connectivity_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/
device_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/
file_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/
file_selector_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file_selector_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file_selector_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
flutter_native_splash=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.4/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.29/
image_cropper=/Users/<USER>/.pub-cache/hosted/pub.dev/image_cropper-8.1.0/
image_cropper_for_web=/Users/<USER>/.pub-cache/hosted/pub.dev/image_cropper_for_web-6.1.0/
image_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
image_picker_android=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+25/
image_picker_for_web=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/
image_picker_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
image_picker_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
image_picker_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
image_picker_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
package_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/
permission_handler_android=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/
permission_handler_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
permission_handler_html=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
permission_handler_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
quick_actions=/Users/<USER>/.pub-cache/hosted/pub.dev/quick_actions-1.1.0/
quick_actions_android=/Users/<USER>/.pub-cache/hosted/pub.dev/quick_actions_android-1.0.22/
quick_actions_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/quick_actions_ios-1.2.0/
sentry_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.11/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
sqflite=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/
sqflite_android=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/
sqflite_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/
tobias=/Users/<USER>/.pub-cache/hosted/pub.dev/tobias-4.0.0/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/
url_launcher_android=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.17/
url_launcher_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
webview_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/
webview_flutter_android=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.9.1/
webview_flutter_wkwebview=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.23.0/
