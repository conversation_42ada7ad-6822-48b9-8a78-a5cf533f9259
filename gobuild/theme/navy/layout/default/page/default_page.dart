import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/config/api_config.dart';
import 'package:sport_mobile/config/api_res_entity.dart';
import 'package:sport_mobile/default/entity/common_entity.dart';
import 'package:sport_mobile/default/model/common_model.dart';
import 'package:sport_mobile/default/widgets/my_custom_button.dart';
import 'package:sport_mobile/drawing/drawing_router.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/routers/game_router.dart';
import 'package:sport_mobile/login/model/user_model.dart';
import 'package:sport_mobile/net/network_state.dart';
import 'package:sport_mobile/promotion/promotion_router.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/default/widgets/bottom_game_tab.dart';
import 'package:sport_mobile/default/widgets/marquee.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/util/device_utils.dart';
import 'package:sport_mobile/util/games_utils.dart';
import 'package:sport_mobile/util/image_utils.dart';
import 'package:sport_mobile/widgets/avatar_widget.dart';
import 'package:sport_mobile/widgets/load_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:sport_mobile/widgets/shimmer_struct.dart';
import 'package:sport_mobile/widgets/update_dialog.dart';

import 'package:sport_mobile/winner/winner_router.dart';

class DefaultPage extends StatefulWidget {
  final PageController? pageController;

  const DefaultPage({this.pageController, super.key});

  @override
  _DefaultPageState createState() => _DefaultPageState();
}

class _DefaultPageState extends State<DefaultPage>
    with
        AutomaticKeepAliveClientMixin<DefaultPage>,
        SingleTickerProviderStateMixin {
  CommonModel provider = CommonModel();

  late Future<dynamic> commonInfoFutrue;

  Timer? _countdownTimer;

  @override
  void initState() {
    super.initState();


    PackageInfo.fromPlatform().then((PackageInfo packageInfo) {
      //更新配置
      String version = packageInfo.version;
      String versionKey = Device.isAndroid == true
          ? Constant.androidVersion
          : Constant.iosVersion;
      String? currentVersion = SpUtil.getString(versionKey);
      if (version != currentVersion) {
        checkAndShowDialog(context);
      }

      UserModel userModel = Provider.of<UserModel>(context, listen: false);
      _countdownTimer = Timer.periodic(const Duration(seconds: 10), (_) {
        if (!mounted) {
          return;
        }
        userModel.updateUserInfo();
      });
    });
  }

  List<Widget> buildGameList() {
    List<Widget> _list = [];
    if (provider.basicInfo.lotteryList != null) {
      _list = provider.basicInfo.lotteryList!.map((item) {
        return Stack(
          alignment: Alignment.center,
          children: [
            BottomGameTab(
              width: 50,
              text: item.type!,
              onPressed: () {
                String routesPath = "";
                switch (item.type) {
                  case '竞彩足球':
                    routesPath = GameRouter.jcftPage;
                    break;

                  case '北京单场':
                    routesPath = GameRouter.bjdcPage;
                    break;
                  case '竞猜冠军':
                    routesPath = GameRouter.gjjcPage;
                    break;
                  case '竞彩篮球':
                    routesPath = GameRouter.basketballPage;
                    break;

                  case '福彩3D':
                    routesPath = GameRouter.fc3dPage;
                    break;

                  case '竞猜冠亚军':
                    routesPath = GameRouter.gyjjcPage;
                    break;

                  case '排列三':
                    routesPath = GameRouter.pl3Page;
                    break;
                  case '排列五':
                    routesPath = GameRouter.pl5Page;
                    break;

                  case '足球任选9':
                    routesPath = GameRouter.ft9Page;
                    break;
                  case '足球14场':
                    routesPath = GameRouter.sfc14Page;
                    break;
                  case '双色球':
                    routesPath = GameRouter.ssqPage;
                    break;
                  case '大乐透':
                    routesPath = GameRouter.dltPage;
                    break;
                  case '七星彩':
                    routesPath = GameRouter.qxcPage;
                    break;
                  case '四场进球':
                    routesPath = GameRouter.jqcPage;
                    break;
                  case '6场半全场':
                    routesPath = GameRouter.bqcPage;
                    break;

                  case '欧亚足球':
                    routesPath = GameRouter.oyzqPage;
                    break;
                  
                  case '刮刮乐':
                    routesPath = GameRouter.gglPage;
                    break;  
                  default:
                    routesPath = GameRouter.jcftPage;
                    break;
                }
                NavigatorUtils.push(context, routesPath);
              },
              iconImg: "games/icon_${GamesUtils.getENGameName(item.type!)}",
            ),
            if (item.tag != "")
              Positioned(
                  top: 5,
                  right: 10,
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                          color: Colors.red, // 边框颜色为红色
                          width: 0.8,
                        )),
                    child: Text(
                      "${item.tag}",
                      style: const TextStyle(
                          color: Colors.red, fontSize: Dimens.font_sp10),
                    ),
                  ))
          ],
        );
      }).toList();
    }

    return _list;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ChangeNotifierProvider<CommonModel>(
        create: (_) {
          UserModel userModel = Provider.of<UserModel>(context, listen: false);
          commonInfoFutrue = provider.getCommonInfo(userModel);
          return provider;
        },
        child: Scaffold(
            backgroundColor: const Color(0xFFf2f2f2),
            body: Consumer2<CommonModel, UserModel>(
                builder: (context, model, userModel, child) {
              if (model.isLoad == false &&
                  model.networkStatus == NetworkStatus.networkConnected) {
                return SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Container(
                          decoration: BoxDecoration(color: Color(0xFF3D4681)),
                          child: SafeArea(
                            child: Container(),
                          )),
                      Container(
                        decoration: BoxDecoration(color: Color(0xFF3D4681)),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 0),
                        child: Row(
                          children: [
                            AvatarWidget(
                              defaultImageAsset:
                                  ImageUtils.getImgPath('mine/avatar_default'),
                              useOval: true,
                              imageUrl: userModel.user?.tailorUser ?? "",
                              width: 70,
                              height: 70,
                            ),
                            Expanded(
                                child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Consumer<UserModel>(
                                        builder: (context, userModel, child) {
                                      if (userModel.user!.shopName != "") {
                                        return Text(
                                            '${userModel.user!.shopName}',
                                            style: const TextStyle(
                                                fontSize: Dimens.gap_dp16,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white));
                                      } else {
                                        return Text(
                                            '${model.basicInfo.serviceData?.storeTitle ?? ""}',
                                            style: TextStyle(
                                                fontSize: Dimens.gap_dp16,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white));
                                      }
                                    }),
                                    Row(
                                      children: [
                                        Container(
                                          alignment: Alignment.center,
                                          width: 25,
                                          height: 25,
                                          child: Text(
                                            "¥",
                                            style: TextStyle(
                                                fontSize: Dimens.font_sp20,
                                                fontWeight: FontWeight.bold),
                                          ),
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: Color(0xFFD81E06),
                                          ),
                                        ),
                                        Gaps.hGap4,
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "¥${userModel.user!.lotterybalance ?? 0}",
                                              style: TextStyle(
                                                  fontSize: Dimens.font_sp12,
                                                  color: Colors.white),
                                            ),
                                            Text(
                                              "¥${userModel.user!.mosaicGold ?? 0}",
                                              style: TextStyle(
                                                  fontSize: Dimens.font_sp12,
                                                  color: Color(0xFFfe8320)),
                                            ),
                                          ],
                                        )
                                      ],
                                    )
                                  ],
                                ),
                                Gaps.vGap8,
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 2, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                      ),
                                      child: Row(
                                        children: [
                                          Container(
                                            child: Text("评分",
                                                style: TextStyles.textBold12),
                                          ),
                                          Icon(
                                            Icons.star,
                                            size: Dimens.gap_dp18,
                                            color: Color(0xFFFFD140),
                                          ),
                                          Icon(
                                            Icons.star,
                                            size: Dimens.gap_dp18,
                                            color: Color(0xFFFFD140),
                                          ),
                                          const Icon(
                                            Icons.star,
                                            size: Dimens.gap_dp18,
                                            color: Color(0xFFFFD140),
                                          ),
                                          Icon(
                                            Icons.star,
                                            size: Dimens.gap_dp18,
                                            color: Color(0xFFFFD140),
                                          ),
                                          Icon(
                                            Icons.star,
                                            size: Dimens.gap_dp18,
                                            color: Color(0xFFFFD140),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Row(
                                      children: [
                                        MyCustomButton(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 5, vertical: 5),
                                          text: "在线客服",
                                          onPressed: () {
                                            String url =
                                                'https://kfchat.lz77.cc/mchat/mchatM.aspx?siteid=856196';
                                            if (model.basicInfo.serviceData!
                                                    .cusSwitch! ==
                                                1) {
                                              url = model.basicInfo.serviceData!
                                                  .cusUrl!;
                                            }
                                            NavigatorUtils.goWebViewPage(
                                                context, '客服', url);
                                          },
                                          radius: 10,
                                          textColor: Colors.white,
                                          fontSize: Dimens.font_sp12,
                                        ),
                                        Gaps.hGap4,
                                        MyCustomButton(
                                          minWidth: 100,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 5, vertical: 5),
                                          text: "分享店铺",
                                          onPressed: () {
                                            NavigatorUtils.push(context,
                                                PromotionRouter.sharePage);
                                          },
                                          radius: 10,
                                          textColor: Colors.white,
                                          fontSize: Dimens.font_sp12,
                                        ),
                                      ],
                                    )
                                  ],
                                )
                              ],
                            ))
                          ],
                        ),
                      ),
                      Gaps.vGap8,
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 0),
                        child: FutureBuilder<dynamic>(
                            future: commonInfoFutrue,
                            builder: (context, snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.waiting) {
                                return AspectRatio(
                                  aspectRatio: 2.8,
                                  child: Container(
                                    color: Colors.transparent,
                                  ),
                                );
                              } else if (snapshot.hasError) {
                                // 异步任务出错，显示错误信息
                                return Center(
                                  child: Text('Error: ${snapshot.error}'),
                                );
                              } else {
                                return CarouselSlider(
                                  options: CarouselOptions(
                                      autoPlay: true,
                                      aspectRatio: 2.8,
                                      enlargeCenterPage: false,
                                      enableInfiniteScroll: true,
                                      viewportFraction: 1),
                                  items:
                                      model.basicInfo.bannerList!.map((item) {
                                    if (snapshot.hasError) {
                                      // 请求出错，显示错误信息
                                      return Center(
                                        child: Text('Error: ${snapshot.error}'),
                                      );
                                    } else {
                                      return Container(
                                        width:
                                            MediaQuery.of(context).size.width,
                                        margin:
                                            EdgeInsets.symmetric(horizontal: 0),
                                        //color: Colors.red,
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(0.0),
                                          child: CachedNetworkImage(
                                              imageUrl: item.imgUrl!,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              fit: BoxFit.fill,
                                              placeholder: (context, url) {
                                                return const Center(
                                                  child:
                                                      CupertinoActivityIndicator(),
                                                );
                                              }),
                                        ),
                                      );
                                    }
                                  }).toList(),
                                );
                              }
                            }),
                      ),
                      Container(
                          alignment: Alignment.center,
                          width: double.infinity,
                          margin: const EdgeInsets.symmetric(
                              horizontal: 0, vertical: 0),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 5,
                          ),
                          color: Colors.white,
                          child: Row(
                            children: [
                              const SizedBox(
                                width: 5,
                              ),
                               Icon(
                                Icons.notifications_outlined,
                                size: 14,
                                color: Color(0xFF5c5c5c),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                  flex: 1,
                                  child: Container(
                                      decoration: BoxDecoration(
                                        color: Color(0xFFEDF9FE),
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                      ),
                                      child: Row(
                                        children: [
                                          Text("店铺公告：",
                                              style: TextStyles.textSize12),
                                           if (model.basicInfo.winNews != null &&
                                  model.basicInfo.winNews!.isNotEmpty)
                                Expanded(
                                    flex: 1,
                                    child: Container(
                                        child: Row(
                                      children: [
                                        Expanded(
                                          child: Container(
                                            height: 16,
                                            child: MarqueeWidget(
                                              itemCount: model.basicInfo.winNews
                                                      ?.length ??
                                                  0,
                                              durationOffset: 14,
                                              itemBuilder: (BuildContext
                                                      context,
                                                  int index,
                                                  BoxConstraints constraints) {
                                                return Container(
                                                  child: Text(
                                                      "${model.basicInfo.winNews![index].content}",
                                                      style: TextStyle(
                                                          fontSize:
                                                              Dimens.font_sp12,
                                                          color: Color(
                                                              0xFF5c5c5c))),
                                                );
                                              },
                                              separatorBuilder: (BuildContext
                                                      context,
                                                  int index,
                                                  BoxConstraints constraints) {
                                                return Container(
                                                  width: 100,
                                                );
                                              },
                                              edgeBuilder: (BuildContext
                                                      context,
                                                  int index,
                                                  BoxConstraints constraints) {
                                                return Container(
                                                  width: constraints.maxWidth,
                                                );
                                              },
                                            ),
                                          ),
                                        )
                                      ],
                                    )))
                                        ],
                                      )))
                            ],
                          )),
                      Container(
                          margin: const EdgeInsets.symmetric(horizontal: 0),
                          padding: EdgeInsets.symmetric(horizontal: 10),
                          decoration: BoxDecoration(
                            color: Colors.white,
                          ),
                          child: Column(
                             crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              ///model.commonEntity.lotteryList.fore
                              GridView.count(
                                  crossAxisCount: 3, // 每行显示的列数
                                  childAspectRatio: 1.4, // 宽高比例
                                  padding: EdgeInsets.zero,
                                  shrinkWrap: true,
                                  physics:
                                      const NeverScrollableScrollPhysics(), // 禁止滚动
                                  children: buildGameList()),



                                  
                              if(model.basicInfo.shareList  != null && model.basicInfo.shareList!.length > 0)
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const Row(
                                    children: [
                                      LoadAssetImage(
                                        "default/winner_order_icon",
                                        width: 15,
                                        height: 15,
                                      ),
                                      Gaps.hGap4,
                                      Text("大神推单",
                                          style: TextStyles.textBold14),
                                      Gaps.hGap16,
                                    ],
                                  ),
                                  Expanded(
                                      child: Container(
                                    height: 1, // 设置横线的高度
                                    color: Colors.grey, // 设置横线的颜色
                                  )),
                                  Row(
                                    children: [
                                      Gaps.hGap4,
                                        MyCustomButton(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 5, horizontal: 12),
                                        onPressed: () {
                                          widget.pageController!.jumpToPage(2);
                                        },
                                        textColor: Colors.white,
                                        text: "更多>",
                                      )
                                    ],
                                  ),
                                ],
                              ),
                              if(model.basicInfo.shareList  != null && model.basicInfo.shareList!.length > 0)
                              Container(
                                margin: const EdgeInsets.only(top: 10),
                                height: 140,
                                child: ListView.builder(
                                  scrollDirection: Axis.horizontal,
                                  itemCount: model.basicInfo.shareList?.length,
                                  itemBuilder: (context, index) {
                                    return Container(
                                      width: 245,
                                      margin:
                                          EdgeInsets.symmetric(horizontal: 2),
                                      child: DefaultWinnerItem(
                                          winner: model
                                              .basicInfo.shareList![index]),
                                    );
                                  },
                                ),
                              ),
                              Gaps.vGap12,
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const Row(
                                    children: [
                                      LoadAssetImage(
                                        "default/drawing_icon",
                                        width: 15,
                                        height: 15,
                                      ),
                                      Gaps.hGap4,
                                      Text("开奖", style: TextStyles.textBold14),
                                      Gaps.hGap16,
                                    ],
                                  ),
                                  Expanded(
                                      child: Container(
                                    height: 1, // 设置横线的高度
                                    color: Colors.grey, // 设置横线的颜色
                                  )),
                                  Row(
                                    children: [
                                      Gaps.hGap4,

                                      MyCustomButton(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 5, horizontal: 12),
                                        onPressed: () {
                                          NavigatorUtils.push(context,
                                              DrawingRouter.drawingPage);
                                        },
                                        textColor: Colors.white,
                                        text: "更多>",
                                      )

                                      //  Text(">", style: TextStyles.textSize14),
                                    ],
                                  ),
                                ],
                              ),


            Container(
                      
                          alignment: Alignment.center,
                          width: double.infinity,
                          margin: const EdgeInsets.symmetric(
                              horizontal: 0, vertical: 5),
                          padding: const EdgeInsets.symmetric(
                            vertical: 5,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10.0), // 设置圆角大小
                          ),
                          child: Row(
                            children: [
                              const SizedBox(
                                width: 5,
                              ),
                              const LoadAssetImage(
                                "default/news",
                                height: 20,
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              Container(
                                width: 1.0,
                                height: 30,
                                color: const Color(0xFFcccccc),
                              ),
                              const SizedBox(
                                width: 5,
                              ),
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                      width: double.infinity, child: Marquee(
                                         prizeList: model.prizeList,
                                      )),
                                 
                                ],
                              )),
                            ],
                          )),                              


                              Gaps.vGap12,
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                      child: Container(
                                    height: 1, // 设置横线的高度
                                    color: Color(0xFFD9001B), // 设置横线的颜色

                                    //     child: Text("23234"),
                                  )),
                                  const MyCustomButton(
                                    minWidth: 120,
                                    text: "实体店铺",
                                    onPressed: null,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 15, vertical: 5),
                                    iconImg: LoadAssetImage(
                                      "default/instore",
                                      width: 20,
                                      height: 20,
                                    ),
                                    textColor: Colors.white,
                                    radius: 15,
                                    bgColor: Color(0xFFD9001B),
                                  ),
                                  Expanded(
                                      child: Container(
                                    height: 1, // 设置横线的高度
                                    color: Color(0xFFD9001B), // 设置横线的颜色
                                  )),
                                ],
                              ),
                              Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 10),
                                child: const Text(
                                    " 本店承诺:为便捷社区服务,我是线下实体店铺,购买有保障,每单均有实体出票照片。献出一点爱心,收获一份希望,祝您好运连连 ",
                                    style: TextStyles.textSize12),
                              ),

                   


                              GestureDetector(
                                onTap: () {
                                  NavigatorUtils.push(context,
                                      "${GameRouter.gameHelperPage}/${MatchDataType.FT}");
                                },
                                child: Container(
                                  height: 200,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: ImageUtils.getAssetImage(
                                          'default/new_strategy'), // 设置图片路径
                                      // fit: BoxFit.cover, // 设置图片的适应方式
                                    ),
                                  ),
                                ),
                              )
                            ],
                          )),
                    ],
                  ),
                );
              } else if ((model.isLoad == false &&
                      model.networkStatus == NetworkStatus.networkFailed) ||
                  (model.isLoad == false &&
                      model.networkStatus == NetworkStatus.networkTimeout)) {
                return NetworkFailedWidget(
                  onPressed: () {
                    setState(() {
                      model.getCommonInfo(userModel);
                    });
                  },
                );
              } else {
                return SingleChildScrollView(child: DefaultShimmer());
              }
            })));
  }

  @override
  bool get wantKeepAlive => true;
}

class DefaultShimmer extends StatelessWidget {
  const DefaultShimmer({super.key});

  List<Widget> buildGameList() {
    List<Widget> _list = [];

    List _itemList = List.generate(12, (index) => index);

    _list = _itemList.map((item) {
      return Stack(
        alignment: Alignment.center,
        children: [
          ShimmerStruct.circular(height: 50),
        ],
      );
    }).toList();

    return _list;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Stack(
          children: [
            const LoadAssetImage('default/bg'),
            Positioned(
              child: SafeArea(
                  child: Container(
                alignment: Alignment.center,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0), // 设置圆角大小
                ),
                margin: const EdgeInsets.only(
                    left: 4.0, right: 4.0, top: 50, bottom: 5),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Gaps.vGap24,
                        const ShimmerStruct.rectangular(height: 16),
                        const ShimmerStruct.rectangular(height: 12),
                        Gaps.vGap16,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              width: 100,
                              child: MyCustomButton(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 5, vertical: 5),
                                text: "在线客服",
                                onPressed: () {
                                  String url =
                                      'https://kfchat.lz77.cc/mchat/mchatM.aspx?siteid=856196';
                                  NavigatorUtils.goWebViewPage(
                                      context, '客服', url);
                                },
                                radius: 10,
                                textColor: Colors.white,
                                fontSize: Dimens.font_sp14,
                              ),
                            ),
                            Gaps.hGap10,
                            MyCustomButton(
                              minWidth: 100,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 5, vertical: 5),
                              text: "分享店铺",
                              onPressed: () {
                                NavigatorUtils.push(
                                    context, PromotionRouter.sharePage);
                              },
                              radius: 10,
                              iconImg: const LoadAssetImage(
                                "default/right-top-arrow",
                                width: 20,
                                height: 20,
                              ),
                              textColor: Colors.white,
                              fontSize: Dimens.font_sp14,
                            ),
                            Gaps.hGap10,
                          ],
                        )
                      ]),
                ),
              )),
            ),
            Positioned(
                left: 0,
                right: 0,
                top: 0,
                child: SafeArea(child: ShimmerStruct.circular(height: 80)))
          ],
        ),
        Container(
            alignment: Alignment.center,
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
            padding: const EdgeInsets.symmetric(
              vertical: 5,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.0), // 设置圆角大小
            ),
            child: Row(
              children: [
                const SizedBox(
                  width: 5,
                ),
                const LoadAssetImage(
                  "default/news",
                  height: 25,
                ),
                const SizedBox(
                  width: 10,
                ),
                Container(
                  width: 1.0,
                  height: 30,
                  color: const Color(0xFFcccccc),
                ),
                const SizedBox(
                  width: 5,
                ),
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(width: double.infinity, child: Marquee()),
                    Container(
                      width: double.infinity,
                      child: const Text(
                        '到店里取票请与我确定营业时间,进店请佩戴口罩 ',
                        style: TextStyles.textSize12,
                        textAlign: TextAlign.start,
                        overflow: TextOverflow.ellipsis,
                      ),
                    )
                  ],
                )),
              ],
            )),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: CarouselSlider(
              options: CarouselOptions(
                  autoPlay: true,
                  aspectRatio: 2.4,
                  enlargeCenterPage: false,
                  enableInfiniteScroll: true,
                  viewportFraction: 1),
              items: [
                Container(
                  width: MediaQuery.of(context).size.width,
                  margin: EdgeInsets.symmetric(horizontal: 5),
                  //color: Colors.red,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10.0),
                    child: Center(
                      child: Text("加载中..."),
                    ),
                  ),
                ),
              ]),
        ),
        Gaps.vGap10,
        Container(
            margin: const EdgeInsets.symmetric(horizontal: 5),
            //padding: EdgeInsets.symmetric(horizontal: 5),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(10.0)),
            child: Column(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ///model.commonEntity.lotteryList.fore

                GridView.count(
                    crossAxisCount: 3, // 每行显示的列数
                    childAspectRatio: 1.4, // 宽高比例
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(), // 禁止滚动
                    children: buildGameList()),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Container(
                      height: 1, // 设置横线的高度
                      color: Colors.red, // 设置横线的颜色

                      //     child: Text("23234"),
                    )),
                    const MyCustomButton(
                      minWidth: 120,
                      text: "实体店铺",
                      onPressed: null,
                      padding:
                          EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                      iconImg: LoadAssetImage(
                        "default/instore",
                        width: 20,
                        height: 20,
                      ),
                      textColor: Colors.white,
                      radius: 15,
                      bgColor: Color(0xFFfe5642),
                    ),
                    Expanded(
                        child: Container(
                      height: 1, // 设置横线的高度
                      color: Colors.red, // 设置横线的颜色
                    )),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: const Text(
                      " 本店承诺:为便捷社区服务,我是线下实体店铺,购买有保障,每单均有实体出票照片。献出一点爱心,收获一份希望,祝您好运连连 ",
                      style: TextStyles.textSize12),
                ),

                GestureDetector(
                  onTap: () {
                    NavigatorUtils.push(context,
                        "${GameRouter.gameHelperPage}/${MatchDataType.FT}");
                  },
                  child: Container(
                    height: 200,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: ImageUtils.getAssetImage(
                            'default/new_strategy'), // 设置图片路径
                        // fit: BoxFit.cover, // 设置图片的适应方式
                      ),
                    ),
                  ),
                )
              ],
            )),
      ],
    );
  }
}

class DefaultWinnerItem extends StatelessWidget {
  final ShareList winner;
  final String defaultImageAsset = ImageUtils.getImgPath('mine/avatar_default');
  DefaultWinnerItem({super.key, required this.winner});
  // List<Widget> generateGameTitles() {
  //   List<Widget> _list;
  //   _list = winner.touzhuStyleList!.map((e) {
  //     return Container(
  //       margin: EdgeInsets.only(right: 2),
  //       decoration: BoxDecoration(
  //         borderRadius: BorderRadius.circular(5.0),
  //         color: const Color(0xFF698ce8),
  //       ),
  //       padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
  //       child: Text(
  //         "$e",
  //         style: TextStyles.textWhite12,
  //       ),
  //     );
  //   }).toList();

  //   return _list;
  // }

  String generateGameName(String gameID) {
    int gameIDInt = int.parse(gameID);

    String gameName = OrderType.values[gameIDInt]!.cnname;
    return gameName;
  }

  String generateGameENName(String gameID) {
    int gameIDInt = int.parse(gameID);

    String gameName = OrderType.values[gameIDInt]!.enname;
    return gameName;
  }

  String getYearMonthDayDateTime(int unixTime) {
    DateTime dateTime =
        DateTime.fromMillisecondsSinceEpoch(unixTime * 1000).toLocal();

    String month = dateTime.month.toString().padLeft(2, '0');

    String day = dateTime.day.toString().padLeft(2, '0');
    String minute = dateTime.minute.toString().padLeft(2, '0');

    // 格式化为年月日
    final formattedDate = '${month}月${day}日 ${dateTime.hour}:${minute}';

    return formattedDate;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          NavigatorUtils.push(context,
              "${WinnerRouter.winnerShareOrderPage}/${winner.shareId}");
        },
        child: Container(
          margin: const EdgeInsets.only(top: 2),
          padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
          decoration: BoxDecoration(
              border: Border.all(color: Color(0xFFbbbbbb), width: 0.4),
              borderRadius: BorderRadius.circular(10),
              color: Colors.white),
          width: double.infinity,
          child: Column(
            children: [
              Stack(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            NavigatorUtils.push(context,
                                "${WinnerRouter.winnerProfilePage}/${winner.shareUserId}/7");
                          },
                          child: ClipOval(
                            //     radius: 5,
                            child: Image.network(
                              winner.headImg != null ? winner.headImg! : "",
                              width: 55,
                              height: 55,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                // 加载失败时显示默认图片
                                return Image.asset(
                                  defaultImageAsset,
                                  width: 55,
                                  height: 55,
                                  fit: BoxFit.cover,
                                );
                              },
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                // 加载中显示默认图片
                                if (loadingProgress == null) return child;
                                return Image.asset(
                                  defaultImageAsset,
                                  width: 55,
                                  height: 55,
                                  fit: BoxFit.cover,
                                );
                              },
                            ),
                          ),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Gaps.vGap4,
                            Text(
                              "${winner.petName}",
                              style: TextStyles.textSize10,
                            ),
                            Row(
                              children: [
                                Gaps.hGap4,
                                LoadAssetImage(
                                    'games/icon_${generateGameENName(winner.lotteryType!)}',
                                    width: 18.0),
                                Gaps.hGap4,
                                //   ...generateGameTitles(),

                                Container(
                                  margin: EdgeInsets.only(right: 2),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: const Color(0xFF698ce8),
                                  ),
                                  padding: EdgeInsets.symmetric(
                                      vertical: 2, horizontal: 4),
                                  child: Text(
                                    "${winner.touzhuStyle}",
                                    style: TextStyles.textWhite10,
                                  ),
                                ),
                                Gaps.hGap4,
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: Color(0xFFfd4627),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 2, horizontal: 4),
                                  child: Text(
                                    "起跟${winner.betting}元",
                                    style: TextStyles.textWhite10,
                                  ),
                                )
                              ],
                            ),
                            Gaps.vGap4,
                            Row(
                              children: [
                                winner.minForecastReward !=
                                        winner.maxForecastReward
                                    ? const Text(
                                        "奖金范围:",
                                        style: TextStyle(
                                            color: Color(0xFF999999),
                                            fontSize: Dimens.font_sp10),
                                      )
                                    : const Text(
                                        "中奖金额:",
                                        style: TextStyle(
                                            color: Color(0xFF999999),
                                            fontSize: Dimens.font_sp10),
                                      ),
                                Gaps.hGap4,
                                winner.minForecastReward !=
                                        winner.maxForecastReward
                                    ? Text(
                                        "${winner.minForecastReward} ~ ${winner.maxForecastReward}元",
                                        style: const TextStyle(
                                            color: Color(0xFF999999),
                                            fontSize: Dimens.font_sp10),
                                      )
                                    : Text(
                                        " ${winner.minForecastReward}元",
                                        style: const TextStyle(
                                            color: Color(0xFF999999),
                                            fontSize: Dimens.font_sp10),
                                      ),
                              ],
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                      top: 0,
                      right: 0,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "截止时间",
                            style: TextStyles.textSize10,
                          ),
                          Text(
                            "${getYearMonthDayDateTime(winner.endTime!)}",
                            style: TextStyles.textBoldRed10,
                          ),
                        ],
                      ))
                ],
              ),
              Container(
                padding: EdgeInsets.symmetric(vertical: 2),
                alignment: Alignment.centerLeft,
                child: Text(
                  "${winner.desc}",
                  style: TextStyles.textSize10,
                ),
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      generateGameName(winner.lotteryType!),
                      style: TextStyles.textBold12,
                    ),
                    Column(
                      children: [
                        Text(
                          "自购金额",
                          style: TextStyle(
                              fontSize: Dimens.font_sp10,
                              color: Color(0xFF999999)),
                        ),
                        Text(
                          "${winner.planMoney}",
                          style: TextStyles.textBold12,
                        ),
                      ],
                    ),
                    Container(
                      width: 1,
                      color: Color(0xFFeeeeee),
                      padding: EdgeInsets.symmetric(vertical: 2),
                    ),
                    Column(
                      children: [
                        Text(
                          "跟单人数",
                          style: TextStyle(
                              fontSize: Dimens.font_sp10,
                              color: Color(0xFF999999)),
                        ),
                        Text(
                          "${winner.copyNum}",
                          style: TextStyles.textBold12,
                        ),
                      ],
                    ),
                    MyCustomButton(
                      bgColor: Color(0xFFf16e56),
                      onPressed: null,
                      text: "我要跟单",
                      textColor: Colors.white,
                      fontSize: Dimens.font_sp10,
                      radius: 20,
                      padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    )
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
