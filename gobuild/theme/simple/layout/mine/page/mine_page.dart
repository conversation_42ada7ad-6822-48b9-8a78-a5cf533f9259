import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/certified/certified_router.dart';
import 'package:sport_mobile/coupon/coupon_router.dart';
import 'package:sport_mobile/default/widgets/my_custom_button.dart';
import 'package:sport_mobile/deposit/deposit_router.dart';
import 'package:sport_mobile/fans/fans_router.dart';
import 'package:sport_mobile/faq/faq_router.dart';
import 'package:sport_mobile/giveaway/giveaway.dart';
import 'package:sport_mobile/login/model/user_model.dart';
import 'package:sport_mobile/mine/widgets/jackpot_dialog.dart';
import 'package:sport_mobile/mine/widgets/logout_dialog.dart';
import 'package:sport_mobile/mine/widgets/mine_icon_button.dart';
import 'package:sport_mobile/order/order_router.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/transaction/order_router.dart';
import 'package:sport_mobile/promotion/promotion_router.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/store/store_router.dart';
import 'package:sport_mobile/transfer/transfer_router.dart';
import 'package:sport_mobile/util/device_utils.dart';
import 'package:sport_mobile/util/image_utils.dart';
import 'package:sport_mobile/widgets/avatar_widget.dart';
import 'package:sport_mobile/widgets/update_dialog.dart';
import 'package:sport_mobile/withdraw/withdraw_router.dart';

final shorebirdCodePush = ShorebirdUpdater();

class MinePage extends StatefulWidget {
  const MinePage({super.key});

  @override
  _MinePageState createState() => _MinePageState();
}

class _MinePageState extends State<MinePage>
    with
        AutomaticKeepAliveClientMixin<MinePage>,
        SingleTickerProviderStateMixin {
  bool isUpdateAvailable = false;

  String version = "";

  String patchNumber = "";

  void _showJackpotDialog(String? amount) {
    showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (_) => JackpotDialog(
              () {},
              money: amount,
            ));
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      version = await getVersion();

      String versionKey = Device.isAndroid == true
          ? Constant.androidVersion
          : Constant.iosVersion;
      String? currentVersion = SpUtil.getString(versionKey);
      if (version != currentVersion) {
        setState(() {
        isUpdateAvailable = true;
        });
      }

      UserModel model = Provider.of<UserModel>(context, listen: false);
      model.getFans();
      model.getJackpot().then((res) {
        if (res.status == '_0000') {
          _showJackpotDialog(res.money);
          model.getJackpot(action: 'burned');
        }
      });
  
    });
  }

  Future<void> _getPatchNumber() async {
    Patch? patch = await shorebirdCodePush.readCurrentPatch();
    if (patch != null &&  patch.number > 0) {
        patchNumber = "(${patch.number})";
    }
    setState(() {});
  }
  Future<String> getVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    version = packageInfo.version;

    await _getPatchNumber();
    

    return version;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        backgroundColor: Color(0xFFf2f2f2),
        body: Consumer<UserModel>(builder: (context, userModel, child) {
          return SingleChildScrollView(
              child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFFD9001B).withOpacity(0.8),
                      const Color(0xFFD9001B).withOpacity(0.8),
                    ], // 渐变色
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: SafeArea(
                    child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.only(
                          top: 20, left: 10, right: 10, bottom: 10),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      height: 100,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            flex: 9,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                if (isUpdateAvailable == true)
                                  Container(
                                    height: 25,
                                    padding: EdgeInsets.symmetric(),
                                    child: MyCustomButton(
                                      textColor: Colors.white,
                                      bgColor: Colors.red,
                                      onPressed: () {
                                        showDialog<void>(
                                            context: context,
                                            builder: (_) =>
                                                const UpdateDialog());
                                      },
                                      text: "更新",
                                    ),
                                  ),
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Text(userModel.user!.petName!,
                                            style: TextStyles.textSize16),
                                        Text('版本号：${version}${patchNumber}',
                                            style: TextStyles.textSize10),
                                        if (userModel.user!.shopName! != "")
                                          Text(userModel.user!.shopName!,
                                              style: TextStyles.textSize16),
                                        GestureDetector(
                                          onTap: () {
                                            NavigatorUtils.push(
                                                context, FansRouter.fansPage);
                                          },
                                          child: Text.rich(
                                            //   textAlign: TextAlign.start,
                                            // overflow: TextOverflow.ellipsis,
                                            TextSpan(
                                              text: "钻粉",
                                              style: TextStyles.textGray14,
                                              children: <TextSpan>[
                                                TextSpan(
                                                  text:
                                                      "${userModel.fans?.fansList?.length ?? 0}",
                                                  style: const TextStyle(
                                                      color: Colors.red,
                                                      fontSize:
                                                          Dimens.font_sp14,
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                const TextSpan(
                                                  text: "人",
                                                  style: TextStyles.textGray14,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                Gaps.hGap10,
                                GestureDetector(
                                  onTap: () {
                                    UserModel user = Provider.of<UserModel>(
                                        context,
                                        listen: false);
                                    if (user.hasVerifyRealName()) {
                                      NavigatorUtils.push(context,
                                          CertifiedRouter.certifiedPage);
                                    } else {
                                      NavigatorUtils.push(
                                          context,
                                          CertifiedRouter
                                              .certifiedRealNamePage);
                                    }
                                  },
                                  child: AvatarWidget(
                                    defaultImageAsset: ImageUtils.getImgPath(
                                        'mine/avatar_default'),
                                    imageUrl: userModel.user!.tailorUser!,
                                    height: 80,
                                    width: 80,
                                    useOval: false,
                                  ),
                                ),
                                Gaps.hGap10,
                              ],
                            ),
                          ),
                          Expanded(flex: 1, child: Text("")),
                        ],
                      ),
                    ),
                  ],
                )),
              ),
              Container(
                child: Stack(
                  children: [
                    Column(
                      children: [
                        Container(
                          height: 50,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFFD9001B).withOpacity(0.8),
                                const Color(0xFFD9001B).withOpacity(0.8),
                              ], // 渐变色
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                          ),
                          child: Text(""),
                        ),
                        Container(
                          height: 30,
                          width: double.infinity,
                          child: Text(""),
                        )
                      ],
                    ),
                    Positioned(
                        child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                      margin: EdgeInsets.symmetric(horizontal: 10),
                      alignment: Alignment.center,
                      width: double.infinity,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(10),
                            topRight: Radius.circular(10)), // 设置圆角大小
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Text(
                                "我的余额",
                                style: TextStyles.textSize16,
                              ),
                              Text(
                                "我的彩金",
                                style: TextStyles.textSize16,
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Text(
                                "¥${userModel.user!.lotterybalance!}",
                                style: TextStyle(
                                    color: Colors.red,
                                    fontSize: Dimens.font_sp14),
                              ),
                              Text(
                                "¥${userModel.user!.mosaicGold ?? 0}",
                                style: TextStyle(
                                    color: Colors.red,
                                    fontSize: Dimens.font_sp14),
                              ),
                            ],
                          ),
                          Gaps.vGap16,
                          Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                MyCustomButton(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 4, horizontal: 22),
                                  text: '充值',
                                  // customIcon: Icon(Icons.wallet_giftcard_outlined,color: Colors.white,),
                                  onPressed: () {
                                    NavigatorUtils.push(
                                        context, DepositRouter.depositPage);
                                  },
                                  bgColor: Color(0xFF70B603),
                                  textColor: Colors.white,
                                  fontSize: Dimens.font_sp14,
                                ),
                                MyCustomButton(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 4, horizontal: 22),
                                  text: '提现',
                                  onPressed: () {
                                    NavigatorUtils.push(
                                        context, WithdrawRouter.withdrawPage);
                                  },
                                  bgColor: Color(0xFF027DB4),
                                  textColor: Colors.white,
                                  fontSize: Dimens.font_sp14,
                                ),
                                MyCustomButton(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 4, horizontal: 22),
                                  text: '赠送',

                                  // customIcon: Icon(Icons.wallet_giftcard_outlined,color: Colors.white,),
                                  onPressed: () {
                                    NavigatorUtils.push(
                                        context, GiveawayRouter.giveawayPage);
                                  },
                                  bgColor: Color(0xFFFA919E),
                                  textColor: Colors.white,
                                  fontSize: Dimens.font_sp14,
                                ),
                                MyCustomButton(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 4, horizontal: 22),
                                  text: '转充',

                                  // customIcon: Icon(Icons.wallet_giftcard_outlined,color: Colors.white,),
                                  onPressed: () {
                                    NavigatorUtils.push(
                                        context, TransferRouter.transferPage);
                                  },
                                  bgColor: Color(0xFFD9001B).withOpacity(0.8),
                                  textColor: Colors.white,
                                  fontSize: Dimens.font_sp14,
                                )
                              ]),
                          Gaps.vGap16,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              GestureDetector(
                                onTap: () {
                                  NavigatorUtils.push(context,
                                      TransactionRouter.transactionPage);
                                },
                                child: Text(
                                  "账单明细",
                                  style: TextStyle(
                                      color: Colors.red,
                                      fontSize: Dimens.font_sp14,
                                      fontWeight: FontWeight.w300),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    )),
                  ],
                ),
              ),
              Gaps.vGap10,
              GestureDetector(
                onTap: () {
                  NavigatorUtils.push(
                      context, '${OrderRouter.orderPage}?action=all');
                },
                child: Container(
                    margin: EdgeInsets.only(bottom: 0, left: 10, right: 10),
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                            onTap: () {
                              NavigatorUtils.push(context,
                                  '${OrderRouter.orderPage}?action=all');
                            },
                            child: Text(
                              "全部订单",
                              style: TextStyle(
                                  color: Colors.red,
                                  fontSize: Dimens.font_sp14,
                                  fontWeight: FontWeight.w300),
                            )),
                        GestureDetector(
                            onTap: () {
                              NavigatorUtils.push(context,
                                  '${OrderRouter.orderPage}?action=all');
                            },
                            child: Text(
                              "投注记录",
                              style: TextStyle(
                                  fontSize: Dimens.font_sp14,
                                  fontWeight: FontWeight.w300),
                            )),
                      ],
                    )),
              ),
              Gaps.vGap10,
              Container(
                alignment: Alignment.center,
                width: double.infinity,
                margin: EdgeInsets.symmetric(vertical: 0, horizontal: 10),
                padding: const EdgeInsets.only(
                    top: 10, bottom: 15, left: 10, right: 10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0), // 设置圆角大小
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        MineIconButton(
                          text: "我的推单",
                          iconImg: "mine/icon_push_order",
                          onPressed: () {
                            NavigatorUtils.push(
                                context, OrderRouter.orderPushPage);
                          },
                        ),
                        MineIconButton(
                          text: "我的跟单",
                          iconImg: "mine/icon_pending_order",
                          onPressed: () {
                            NavigatorUtils.push(
                                context, OrderRouter.orderFollowPage);
                          },
                        ),
                        MineIconButton(
                          text: "中奖记录",
                          iconImg: "mine/icon_record",
                          onPressed: () {
                            NavigatorUtils.push(
                                context, OrderRouter.orderResultPage);
                          },
                        ),
                        MineIconButton(
                          text: "待开奖",
                          iconImg: "mine/icon_trophy",
                          onPressed: () {
                            NavigatorUtils.push(
                                context, OrderRouter.orderPendingPage);
                          },
                        ),
                      ],
                    )
                  ],
                ),
              ),
              Gaps.vGap10,
              GestureDetector(
                onTap: () {},
                child: Container(
                  height: 98,
                  margin: EdgeInsets.symmetric(vertical: 0, horizontal: 10),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    image: DecorationImage(
                      image: ImageUtils.getAssetImage('mine/mine_leagues'),
                      fit: BoxFit.cover, // 设置图片的适应方式
                    ),
                  ),
                ),
              ),
              Gaps.vGap10,
              Container(
                alignment: Alignment.center,
                width: double.infinity,
                margin: EdgeInsets.symmetric(vertical: 0, horizontal: 10),
                padding: const EdgeInsets.only(
                    top: 10, bottom: 15, left: 40, right: 40),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0), // 设置圆角大小
                ),
                child: Column(
                  children: [
                    Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            MineIconButton2(
                              text: "优惠券",
                              iconImg: "mine/icon_coupon",
                              onPressed: () {
                                NavigatorUtils.push(context,
                                    "${CouponRouter.couponPage}/normal");
                              },
                            ),
                            MineIconButton2(
                              text: "我的彩店",
                              iconImg: "mine/icon_lottery_store",
                              onPressed: () {
                                NavigatorUtils.push(
                                    context, StoreRouter.storePage);
                              },
                            ),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            MineIconButton2(
                              text: "分享好友",
                              iconImg: "mine/icon_share",
                              onPressed: () {
                                NavigatorUtils.push(
                                    context, PromotionRouter.promotionPage);
                              },
                            ),
                            MineIconButton2(
                              text: "实名认证",
                              iconImg: "mine/icon_certified",
                              onPressed: () {
                                UserModel user = Provider.of<UserModel>(context,
                                    listen: false);
                                if (user.hasVerifyRealName()) {
                                  NavigatorUtils.push(
                                      context, CertifiedRouter.certifiedPage);
                                } else {
                                  NavigatorUtils.push(context,
                                      CertifiedRouter.certifiedRealNamePage);
                                }
                              },
                            ),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            MineIconButton2(
                              text: "问题中心",
                              iconImg: "mine/icon_qa",
                              onPressed: () {
                                NavigatorUtils.push(context, FaqRouter.faqPage);
                              },
                            ),
                            MineIconButton2(
                                text: "退出登录",
                                iconImg: "mine/icon_logout",
                                onPressed: _showLogoutDialog)
                          ],
                        ),
                      ],
                    )
                  ],
                ),
              ),
              Gaps.vGap10,
              GestureDetector(
                onTap: () {
                  String? csurl = SpUtil.getString(Constant.csurl);
                  if (csurl != null) {
                    NavigatorUtils.goWebViewPage(context, '联系客服', csurl);
                  }
                },
                child: Container(
                  height: 100,
                  margin: EdgeInsets.symmetric(vertical: 0, horizontal: 10),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    image: DecorationImage(
                      image: ImageUtils.getAssetImage('mine/mine_cs'), // 设置图片路径
                      fit: BoxFit.cover, // 设置图片的适应方式
                    ),
                  ),
                ),
              )
            ],
          ));
        }));
  }

  void _showLogoutDialog() {
    showDialog<void>(context: context, builder: (_) => const LogoutDialog());
  }
}
