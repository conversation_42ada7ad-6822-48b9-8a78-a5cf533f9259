import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/default/model/common_model.dart';
import 'package:sport_mobile/default/widgets/my_custom_button.dart';
import 'package:sport_mobile/drawing/drawing_router.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/routers/game_router.dart';
import 'package:sport_mobile/login/model/user_model.dart';
import 'package:sport_mobile/net/network_state.dart';
import 'package:sport_mobile/promotion/promotion_router.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/default/widgets/bottom_game_tab.dart';
import 'package:sport_mobile/default/widgets/marquee.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/util/device_utils.dart';
import 'package:sport_mobile/util/games_utils.dart';
import 'package:sport_mobile/util/image_utils.dart';
import 'package:sport_mobile/widgets/avatar_widget.dart';
import 'package:sport_mobile/widgets/load_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:sport_mobile/widgets/shimmer_struct.dart';
import 'package:sport_mobile/widgets/update_dialog.dart';
import 'dart:convert';

class DefaultPage extends StatefulWidget {
  final PageController? pageController;

  const DefaultPage({this.pageController, super.key});

  @override
  _DefaultPageState createState() => _DefaultPageState();
}

class _DefaultPageState extends State<DefaultPage>
    with
        AutomaticKeepAliveClientMixin<DefaultPage>,
        SingleTickerProviderStateMixin {
  CommonModel provider = CommonModel();

  late Future<dynamic> commonInfoFutrue;

  Timer? _countdownTimer;


  @override
  void initState() {
    super.initState();

    PackageInfo.fromPlatform().then((PackageInfo packageInfo) {
      //更新配置
      String version = packageInfo.version;
      String versionKey = Device.isAndroid == true
          ? Constant.androidVersion
          : Constant.iosVersion;
      String? currentVersion = SpUtil.getString(versionKey);
      if (version != currentVersion) {
        checkAndShowDialog(context);
      }

      UserModel userModel = Provider.of<UserModel>(context, listen: false);
      _countdownTimer = Timer.periodic(const Duration(seconds: 10), (_) {
        if (!mounted) {
          return;
        }
        userModel.updateUserInfo();
      });
    });
  }

  List<Widget> buildGameList() {
    List<Widget> _list = [];
    if (provider.basicInfo.lotteryList != null) {
      _list = provider.basicInfo.lotteryList!.map((item) {
        return Stack(
          alignment: Alignment.center,
          children: [
            BottomGameTab(
              width: 50,
              text: item.type!,
              onPressed: () {
                String routesPath = "";
                switch (item.type) {
                  case '竞彩足球':
                    routesPath = GameRouter.jcftPage;
                    break;

                  case '北京单场':
                    routesPath = GameRouter.bjdcPage;
                    break;
                  case '竞猜冠军':
                    routesPath = GameRouter.gjjcPage;
                    break;
                  case '竞彩篮球':
                    routesPath = GameRouter.basketballPage;
                    break;

                  case '福彩3D':
                    routesPath = GameRouter.fc3dPage;
                    break;

                  case '竞猜冠亚军':
                    routesPath = GameRouter.gyjjcPage;
                    break;

                  case '排列三':
                    routesPath = GameRouter.pl3Page;
                    break;
                  case '排列五':
                    routesPath = GameRouter.pl5Page;
                    break;

                  case '足球任选9':
                    routesPath = GameRouter.ft9Page;
                    break;
                  case '足球14场':
                    routesPath = GameRouter.sfc14Page;
                    break;
                  case '双色球':
                    routesPath = GameRouter.ssqPage;
                    break;
                  case '大乐透':
                    routesPath = GameRouter.dltPage;
                    break;
                  case '七星彩':
                    routesPath = GameRouter.qxcPage;
                    break;
                  case '四场进球':
                    routesPath = GameRouter.jqcPage;
                    break;
                  case '6场半全场':
                    routesPath = GameRouter.bqcPage;
                    break;

                  case '欧亚足球':
                    routesPath = GameRouter.oyzqPage;
                    break;

                  case '刮刮乐':
                    routesPath = GameRouter.gglPage;
                    break;  
                  default:
                    routesPath = GameRouter.jcftPage;
                    break;
                }
                NavigatorUtils.push(context, routesPath);
              },
              iconImg: "games/icon_${GamesUtils.getENGameName(item.type!)}",
            ),
            if (item.tag != "")
              Positioned(
                  top: 12,
                  right: 10,
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                          color: Colors.red, // 边框颜色为红色
                          width: 0.8,
                        )),
                    child: Text(
                      "${item.tag}",
                      style: const TextStyle(
                          color: Colors.red, fontSize: Dimens.font_sp10),
                    ),
                  ))
          ],
        );
      }).toList();
    }

    return _list;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ChangeNotifierProvider<CommonModel>(
        create: (_) {
          UserModel userModel = Provider.of<UserModel>(context, listen: false);

          commonInfoFutrue = provider.getCommonInfo(userModel);

          return provider;
        },
        child: Scaffold(
            backgroundColor: const Color(0xFFf2f2f2),
            body: Consumer2<CommonModel, UserModel>(
                builder: (context, model, userModel, child) {
              if (model.isLoad == false &&
                  model.networkStatus == NetworkStatus.networkConnected) {
                return SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Stack(
                        children: [
                          const LoadAssetImage('default/bg'),
                          Positioned(
                            child: SafeArea(
                                maintainBottomViewPadding: true,
                                child: Container(
                                  alignment: Alignment.center,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius:
                                        BorderRadius.circular(10.0), // 设置圆角大小
                                  ),
                                  margin: const EdgeInsets.only(
                                      left: 4.0,
                                      right: 4.0,
                                      top: 50,
                                      bottom: 5),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 10),
                                    child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Gaps.vGap24,
                                          Consumer<UserModel>(builder:
                                              (context, userModel, child) {
                                            if (userModel.user!.shopName !=
                                                "") {
                                              return Text(
                                                  '欢迎加入${userModel.user!.shopName}',
                                                  style: TextStyles.textBold16);
                                            } else {
                                              return Text(
                                                  '欢迎加入${model.basicInfo.serviceData?.storeTitle ?? ""}',
                                                  style: TextStyles.textBold16);
                                            }
                                          }),
                                          const Text(
                                              '店铺公告：本店竭诚为各位彩民服务，欢迎大家购买，祝君中奖！谢谢您的光顾！ ',
                                              style: TextStyles.textSize12),
                                          Gaps.vGap16,
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Container(
                                                width: 100,
                                                child: MyCustomButton(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 5,
                                                      vertical: 5),
                                                  text: "在线客服",
                                                  onPressed: () {
                                                    String url =
                                                        'https://kfchat.lz77.cc/mchat/mchatM.aspx?siteid=856196';
                                                    if (model
                                                            .basicInfo
                                                            .serviceData!
                                                            .cusSwitch! ==
                                                        1) {
                                                      url = model.basicInfo
                                                          .serviceData!.cusUrl!;
                                                    }
                                                    NavigatorUtils
                                                        .goWebViewPage(
                                                            context, '客服', url);
                                                  },
                                                  radius: 10,
                                                  textColor: Colors.white,
                                                  fontSize: Dimens.font_sp14,
                                                ),
                                              ),
                                              Gaps.hGap10,
                                              MyCustomButton(
                                                minWidth: 100,
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 5,
                                                        vertical: 5),
                                                text: "分享店铺",
                                                onPressed: () {
                                                  NavigatorUtils.push(
                                                      context,
                                                      PromotionRouter
                                                          .sharePage);
                                                },
                                                radius: 10,
                                                iconImg: const LoadAssetImage(
                                                  "default/right-top-arrow",
                                                  width: 20,
                                                  height: 20,
                                                ),
                                                textColor: Colors.white,
                                                fontSize: Dimens.font_sp14,
                                              ),
                                              Gaps.hGap10,
                                              MyCustomButton(
                                                minWidth: 100,
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 25,
                                                        vertical: 5),
                                                text: "开奖",
                                                onPressed: () {
                                                  NavigatorUtils.push(
                                                      context,
                                                      DrawingRouter
                                                          .drawingPage);
                                                },
                                                radius: 10,
                                                textColor: Colors.white,
                                                fontSize: Dimens.font_sp14,
                                              ),
                                            ],
                                          )
                                        ]),
                                  ),
                                )),
                          ),
                          Consumer<UserModel>(
                              builder: (context, userModel, child) {
                            return Positioned(
                                left: 0,
                                right: 0,
                                top: 0,
                                child: SafeArea(
                                    child: AvatarWidget(
                                  defaultImageAsset: ImageUtils.getImgPath(
                                      'mine/avatar_default'),
                                  useOval: true,
                                  imageUrl: userModel.user?.tailorUser ?? "",
                                )));
                          })
                        ],
                      ),
                      Container(
                          alignment: Alignment.center,
                          width: double.infinity,
                          margin: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 10),
                          padding: const EdgeInsets.symmetric(
                            vertical: 5,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10.0), 
                          ),
                          child: Row(
                            children: [
                              const SizedBox(
                                width: 5,
                              ),
                              const LoadAssetImage(
                                "default/news",
                                height: 25,
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              Container(
                                width: 1.0,
                                height: 30,
                                color: const Color(0xFFcccccc),
                              ),
                              const SizedBox(
                                width: 5,
                              ),
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                      width: double.infinity,
                                      child: Marquee(
                                        prizeList: model.prizeList,
                                      )),
                                  Container(
                                    width: double.infinity,
                                    child: const Text(
                                      '到店里取票请与我确定营业时间,进店请佩戴口罩 ',
                                      style: TextStyles.textSize12,
                                      textAlign: TextAlign.start,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  )
                                ],
                              )),
                            ],
                          )),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: FutureBuilder<dynamic>(
                            future: commonInfoFutrue,
                            builder: (context, snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.waiting) {
                                return AspectRatio(
                                  aspectRatio: 2.4,
                                  child: Container(
                                    color: Colors.transparent,
                                  ),
                                );
                              } else if (snapshot.hasError) {
                                // 异步任务出错，显示错误信息
                                return Center(
                                  child: Text('Error: ${snapshot.error}'),
                                );
                              } else {
                                return CarouselSlider(
                                  options: CarouselOptions(
                                      autoPlay: true,
                                      aspectRatio: 2.4,
                                      enlargeCenterPage: false,
                                      enableInfiniteScroll: true,
                                      viewportFraction: 1),
                                  items:
                                      model.basicInfo.bannerList!.map((item) {
                                    if (snapshot.hasError) {
                                      // 请求出错，显示错误信息
                                      return Center(
                                        child: Text('Error: ${snapshot.error}'),
                                      );
                                    } else {
                                      return Container(
                                        width:
                                            MediaQuery.of(context).size.width,
                                        margin:
                                            EdgeInsets.symmetric(horizontal: 5),
                                        //color: Colors.red,
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(10.0),
                                          child: CachedNetworkImage(
                                              imageUrl: item.imgUrl!,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              fit: BoxFit.fill,
                                              placeholder: (context, url) {
                                                return const Center(
                                                  child:
                                                      CupertinoActivityIndicator(),
                                                );
                                              }),
                                        ),
                                      );
                                    }
                                  }).toList(),
                                );
                              }
                            }),
                      ),
                      Gaps.vGap10,
                      Container(
                          margin: const EdgeInsets.symmetric(horizontal: 5),
                          //padding: EdgeInsets.symmetric(horizontal: 5),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10.0)),
                          child: Column(
                            //   crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              ///model.commonEntity.lotteryList.fore

                              GridView.count(
                                  crossAxisCount: 3, // 每行显示的列数
                                  childAspectRatio: 1.4, // 宽高比例
                                  padding: EdgeInsets.zero,
                                  shrinkWrap: true,
                                  physics:
                                      const NeverScrollableScrollPhysics(), // 禁止滚动
                                  children: buildGameList()),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                      child: Container(
                                    height: 1, // 设置横线的高度
                                    color: Colors.red, // 设置横线的颜色

                                    //     child: Text("23234"),
                                  )),
                                  const MyCustomButton(
                                    minWidth: 120,
                                    text: "实体店铺",
                                    onPressed: null,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 15, vertical: 5),
                                    iconImg: LoadAssetImage(
                                      "default/instore",
                                      width: 20,
                                      height: 20,
                                    ),
                                    textColor: Colors.white,
                                    radius: 15,
                                    bgColor: Color(0xFFfe5642),
                                  ),
                                  Expanded(
                                      child: Container(
                                    height: 1, // 设置横线的高度
                                    color: Colors.red, // 设置横线的颜色
                                  )),
                                ],
                              ),
                              Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 10),
                                child: const Text(
                                    " 本店承诺:为便捷社区服务,我是线下实体店铺,购买有保障,每单均有实体出票照片。献出一点爱心,收获一份希望,祝您好运连连 ",
                                    style: TextStyles.textSize12),
                              ),

                              GestureDetector(
                                onTap: () {
                                  NavigatorUtils.push(context,
                                      "${GameRouter.gameHelperPage}/${MatchDataType.FT}");
                                },
                                child: Container(
                                  height: 200,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: ImageUtils.getAssetImage(
                                          'default/new_strategy'), // 设置图片路径
                                      // fit: BoxFit.cover, // 设置图片的适应方式
                                    ),
                                  ),
                                ),
                              )
                            ],
                          )),
                    ],
                  ),
                );
              } else if ((model.isLoad == false &&
                      model.networkStatus == NetworkStatus.networkFailed) ||
                  (model.isLoad == false &&
                      model.networkStatus == NetworkStatus.networkTimeout)) {
                return NetworkFailedWidget(
                  onPressed: () {
                    setState(() {
                      model.getCommonInfo(userModel);
                    });
                  },
                );
              } else {
                return SingleChildScrollView(child: DefaultShimmer());
              }
            })));
  }

  @override
  bool get wantKeepAlive => true;
}

class DefaultShimmer extends StatelessWidget {
  const DefaultShimmer({super.key});

  List<Widget> buildGameList() {
    List<Widget> _list = [];

    List _itemList = List.generate(12, (index) => index);

    _list = _itemList.map((item) {
      return Stack(
        alignment: Alignment.center,
        children: [
          ShimmerStruct.circular(height: 50),
        ],
      );
    }).toList();

    return _list;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Stack(
          children: [
            const LoadAssetImage('default/bg'),
            Positioned(
              child: SafeArea(
                  child: Container(
                alignment: Alignment.center,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0), // 设置圆角大小
                ),
                margin: const EdgeInsets.only(
                    left: 4.0, right: 4.0, top: 50, bottom: 5),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Gaps.vGap24,
                        const ShimmerStruct.rectangular(height: 16),
                        const ShimmerStruct.rectangular(height: 12),
                        Gaps.vGap16,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              width: 100,
                              child: MyCustomButton(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 5, vertical: 5),
                                text: "在线客服",
                                onPressed: () {
                                  String url =
                                      'https://kfchat.lz77.cc/mchat/mchatM.aspx?siteid=856196';
                                  NavigatorUtils.goWebViewPage(
                                      context, '客服', url);
                                },
                                radius: 10,
                                textColor: Colors.white,
                                fontSize: Dimens.font_sp14,
                              ),
                            ),
                            Gaps.hGap10,
                            MyCustomButton(
                              minWidth: 100,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 5, vertical: 5),
                              text: "分享店铺",
                              onPressed: () {
                                NavigatorUtils.push(
                                    context, PromotionRouter.sharePage);
                              },
                              radius: 10,
                              iconImg: const LoadAssetImage(
                                "default/right-top-arrow",
                                width: 20,
                                height: 20,
                              ),
                              textColor: Colors.white,
                              fontSize: Dimens.font_sp14,
                            ),
                            Gaps.hGap10,
                          ],
                        )
                      ]),
                ),
              )),
            ),
            Positioned(
                left: 0,
                right: 0,
                top: 0,
                child: SafeArea(child: ShimmerStruct.circular(height: 80)))
          ],
        ),
        Container(
            alignment: Alignment.center,
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
            padding: const EdgeInsets.symmetric(
              vertical: 5,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.0), // 设置圆角大小
            ),
            child: Row(
              children: [
                const SizedBox(
                  width: 5,
                ),
                const LoadAssetImage(
                  "default/news",
                  height: 25,
                ),
                const SizedBox(
                  width: 10,
                ),
                Container(
                  width: 1.0,
                  height: 30,
                  color: const Color(0xFFcccccc),
                ),
                const SizedBox(
                  width: 5,
                ),
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(width: double.infinity, child: Marquee()),
                    Container(
                      width: double.infinity,
                      child: const Text(
                        '到店里取票请与我确定营业时间,进店请佩戴口罩 ',
                        style: TextStyles.textSize12,
                        textAlign: TextAlign.start,
                        overflow: TextOverflow.ellipsis,
                      ),
                    )
                  ],
                )),
              ],
            )),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: CarouselSlider(
              options: CarouselOptions(
                  autoPlay: true,
                  aspectRatio: 2.4,
                  enlargeCenterPage: false,
                  enableInfiniteScroll: true,
                  viewportFraction: 1),
              items: [
                Container(
                  width: MediaQuery.of(context).size.width,
                  margin: EdgeInsets.symmetric(horizontal: 5),
                  //color: Colors.red,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10.0),
                    child: Center(
                      child: Text("加载中..."),
                    ),
                  ),
                ),
              ]),
        ),
        Gaps.vGap10,
        Container(
            margin: const EdgeInsets.symmetric(horizontal: 5),
            //padding: EdgeInsets.symmetric(horizontal: 5),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(10.0)),
            child: Column(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ///model.commonEntity.lotteryList.fore

                GridView.count(
                    crossAxisCount: 3, // 每行显示的列数
                    childAspectRatio: 1.4, // 宽高比例
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(), // 禁止滚动
                    children: buildGameList()),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Container(
                      height: 1, // 设置横线的高度
                      color: Colors.red, // 设置横线的颜色

                      //     child: Text("23234"),
                    )),
                    const MyCustomButton(
                      minWidth: 120,
                      text: "实体店铺",
                      onPressed: null,
                      padding:
                          EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                      iconImg: LoadAssetImage(
                        "default/instore",
                        width: 20,
                        height: 20,
                      ),
                      textColor: Colors.white,
                      radius: 15,
                      bgColor: Color(0xFFfe5642),
                    ),
                    Expanded(
                        child: Container(
                      height: 1, // 设置横线的高度
                      color: Colors.red, // 设置横线的颜色
                    )),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: const Text(
                      " 本店承诺:为便捷社区服务,我是线下实体店铺,购买有保障,每单均有实体出票照片。献出一点爱心,收获一份希望,祝您好运连连 ",
                      style: TextStyles.textSize12),
                ),

                GestureDetector(
                  onTap: () {
                    NavigatorUtils.push(context,
                        "${GameRouter.gameHelperPage}/${MatchDataType.FT}");
                  },
                  child: Container(
                    height: 200,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: ImageUtils.getAssetImage(
                            'default/new_strategy'), // 设置图片路径
                        // fit: BoxFit.cover, // 设置图片的适应方式
                      ),
                    ),
                  ),
                )
              ],
            )),
      ],
    );
  }
}
