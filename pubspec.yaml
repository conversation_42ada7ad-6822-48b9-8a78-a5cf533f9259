name: sport_mobile
description: sport.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

#以下两项是本次打包时所使用的平台和bundleid
latest_game_name: hm

latest_bundle_id: com.fysport.hm134

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.3.4
environment:
  sdk: '>=3.0.5 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.0.5
  dio: ^5.8.0+1
  common_utils: 2.1.0
  sprintf: ^7.0.0
  url_launcher: ^6.3.0
  oktoast: ^3.3.1
  keyboard_actions: ^4.2.0
  intl: ^0.19.0 
  rxdart: ^0.28.0
  cached_network_image: ^3.4.1
  fluro: ^2.0.5
  #flutter_swiper_null_safety_flutter3: 3.0.1
  quick_actions: ^1.0.5
  webview_flutter: ^4.7.0
  #carousel_slider: ^4.2.1
  carousel_slider: ^5.0.0
  web_socket_channel: ^3.0.0
  socket_io_client: ^2.0.2
  encrypt: ^5.0.1
  sp_util: ^2.0.3
  flutter_html: ^3.0.0-alpha.3
  easy_refresh: ^3.3.2+1
  extended_nested_scroll_view: ^6.1.2
  get: ^4.6.5
  flutter_easyloading: ^3.0.5
  qr_flutter: ^4.1.0
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2

  pointycastle: ^3.8.0
  tobias: ^4.0.0 

  flutter_screenutil: ^5.9.3
  flutter_slidable: ^3.0.0
  #open_file: ^3.3.2 #请求MANAGE_EXTERNAL_STORAGE出现拒绝的bug，所以使用open_file_plus
  #open_file_plus: ^3.4.1
  path_provider: ^2.1.1
  image_cropper: ^8.0.2
  image_picker: ^1.0.4
  device_info_plus: ^10.1.2  
  permission_handler: ^11.0.1
  http: ^1.1.0
  package_info_plus: ^8.0.2 
  logger: ^2.0.2+1
  connectivity_plus: ^6.0.3
  file_picker: ^8.0.3
  shorebird_code_push: ^2.0.3
  shimmer: ^3.0.0
  sentry_flutter: ^8.14.1
  scratcher: ^2.5.0
  
dev_dependencies:
  flutter_launcher_icons: "^0.13.1"
  flutter_native_splash: ^2.4.0
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  remove_alpha_ios: true
 
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:



  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  assets:
    - assets/images/
    - assets/images/home/
    - assets/images/login/
    - assets/images/default/
    - assets/images/carousel/
    - assets/images/games/
    - assets/images/mine/
    - assets/images/mine/deposit/
    - assets/images/mine/faq/
    - assets/images/share/
    - assets/images/order/
    - assets/images/bank/
    - assets/images/bet/
    - assets/images/winner/
    - assets/images/lottery/
    - assets/images/expert/
    - assets/config/
    - shorebird.yaml

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages


  fonts:
    - family: BankIcons
      fonts:
        - asset: assets/fonts/BankIcons.ttf
    - family: NewBankIcons
      fonts:
        - asset: assets/fonts/banks.ttf
    - family: BankFonts
      fonts:
        - asset: assets/fonts/bankfonts.ttf
    
        
tobias:
  url_scheme: sportmobile
  ios:
    ignore_security: true
  #  no_utdid: true
