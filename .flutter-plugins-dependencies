{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": true, "dependencies": []}, {"name": "flutter_native_splash", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.4/", "native_build": true, "dependencies": []}, {"name": "image_cropper", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_cropper-8.1.0/", "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": []}, {"name": "quick_actions_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quick_actions_ios-1.2.0/", "native_build": true, "dependencies": []}, {"name": "sentry_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "to<PERSON>s", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/tobias-4.0.0/", "native_build": true, "dependencies": []}, {"name": "url_launcher_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": []}, {"name": "webview_flutter_wkwebview", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.23.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "android": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "flutter_native_splash", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.4/", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.29/", "native_build": true, "dependencies": []}, {"name": "image_cropper", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_cropper-8.1.0/", "native_build": true, "dependencies": []}, {"name": "image_picker_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+25/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": []}, {"name": "quick_actions_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quick_actions_android-1.0.22/", "native_build": true, "dependencies": []}, {"name": "sentry_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.11/", "native_build": true, "dependencies": []}, {"name": "sqflite_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/", "native_build": true, "dependencies": []}, {"name": "to<PERSON>s", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/tobias-4.0.0/", "native_build": true, "dependencies": []}, {"name": "url_launcher_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.17/", "native_build": true, "dependencies": []}, {"name": "webview_flutter_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.9.1/", "native_build": true, "dependencies": []}], "macos": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": true, "dependencies": []}, {"name": "file_selector_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/", "native_build": true, "dependencies": []}, {"name": "image_picker_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sentry_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": []}, {"name": "webview_flutter_wkwebview", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.23.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "linux": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": false, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": false, "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": false, "dependencies": []}, {"name": "file_selector_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/", "native_build": true, "dependencies": []}, {"name": "image_picker_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_linux"]}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": false, "dependencies": []}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "sentry_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": []}], "windows": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": false, "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": false, "dependencies": []}, {"name": "file_selector_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/", "native_build": true, "dependencies": []}, {"name": "image_picker_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": false, "dependencies": []}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": []}, {"name": "sentry_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": []}], "web": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "dependencies": []}, {"name": "flutter_native_splash", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.4/", "dependencies": []}, {"name": "image_cropper_for_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_cropper_for_web-6.1.0/", "dependencies": []}, {"name": "image_picker_for_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/", "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "dependencies": []}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": []}, {"name": "sentry_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/", "dependencies": ["package_info_plus"]}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": []}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/", "dependencies": []}]}, "dependencyGraph": [{"name": "connectivity_plus", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "file_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "flutter_native_splash", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "image_cropper", "dependencies": ["image_cropper_for_web"]}, {"name": "image_cropper_for_web", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "quick_actions", "dependencies": ["quick_actions_android", "quick_actions_ios"]}, {"name": "quick_actions_android", "dependencies": []}, {"name": "quick_actions_ios", "dependencies": []}, {"name": "sentry_flutter", "dependencies": ["package_info_plus"]}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "to<PERSON>s", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}], "date_created": "2025-08-11 21:52:13.294572", "version": "3.27.3", "swift_package_manager_enabled": false}