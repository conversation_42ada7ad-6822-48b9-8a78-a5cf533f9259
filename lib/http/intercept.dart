///  intercept.dart
///
///  Created by iotjin on 2020/07/08.
///  description:  拦截器

import 'package:dio/dio.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/config/api_config.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/util/log_utils.dart';
import 'dio_utils.dart';
import 'exception_handle.dart';

// default token
const String defaultToken = '';
const String kRefreshTokenUrl = "";

String getToken() {
  var token = SpUtil.getString(Constant.token) ?? defaultToken;
  return token;
}

void setToken(accessToken) {
  SpUtil.putString(Constant.token, accessToken);
}

String getRefreshToken() {
  var refreshToken = SpUtil.getString('refreshToken') ?? '';
  return refreshToken;
}

void setRefreshToken(refreshToken) {
  SpUtil.putString(Constant.refreshToken, refreshToken);
}

/// 统一添加身份验证请求头（根据项目自行处理）
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // if (options.path != APIs.login) {
    //   final String accessToken = getToken();
    //   if (accessToken.isNotEmpty) {
    //     options.headers['Authorization'] = 'Bearer $accessToken';
    //   }
    // }
    super.onRequest(options, handler);
  }
}

/// 刷新Token（根据项目自行处理）
class TokenInterceptor extends QueuedInterceptor {
  Dio? _tokenDio;

  Future<Map<String, dynamic>?> refreshTokenRequest() async {
    var params = {'accessToken': getToken(), 'refreshToken': getRefreshToken()};
    try {
      _tokenDio ??= Dio();
      _tokenDio!.options = DioUtils.instance.dio.options;
      _tokenDio!.options.headers['Authorization'] = 'Bearer ${getToken()}';
      final Response<dynamic> response = await _tokenDio!.post<dynamic>(kRefreshTokenUrl, data: params);
      var res = response.data as dynamic;
      if (res['code'] == ExceptionHandle.success) {
        return response.data;
      }
      // if (response.statusCode == ExceptionHandle.success) {
      //   return response.data;
      // }
    } catch (e) {
      LogUtils.logger.e('---------- 刷新Token失败！----------');
    }
    return null;
  }

  @override
  Future<void> onResponse(Response<dynamic> response, ResponseInterceptorHandler handler) async {
    // 401代表token过期
    if (response.statusCode == ExceptionHandle.unauthorized) {
      LogUtils.logger.d('---------- 自动刷新Token ----------');
      var res = await refreshTokenRequest(); // 获取新的accessToken
      if (res != null) {
        var accessToken = res['accessToken'];
        LogUtils.logger.e('---------- NewToken: $accessToken ----------');

        // 保存token
        setToken(accessToken);
        setRefreshToken(res['refreshToken']);

        // 重新请求失败接口
        final RequestOptions request = response.requestOptions;
        request.headers['Authorization'] = 'Bearer $accessToken';

        final Options options = Options(
          headers: request.headers,
          method: request.method,
        );

        try {
          LogUtils.logger.e('---------- 重新请求接口 ----------');

          /// 避免重复执行拦截器，使用tokenDio
          final Response<dynamic> response = await _tokenDio!.request<dynamic>(
            request.path,
            data: request.data,
            queryParameters: request.queryParameters,
            cancelToken: request.cancelToken,
            options: options,
            onReceiveProgress: request.onReceiveProgress,
          );
          return handler.next(response);
        } on DioException catch (e) {
          return handler.reject(e);
        }
      }
    }
    super.onResponse(response, handler);
  }
}

/// 打印日志
class LoggingInterceptor extends Interceptor {
  late DateTime _startTime;
  late DateTime _endTime;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    _startTime = DateTime.now();
    LogUtils.logger.d('-------------------- Start --------------------');
    if (options.queryParameters.isEmpty) {
      LogUtils.logger.d('RequestUrl: ${options.baseUrl}${options.path}');
    } else {
      LogUtils.logger.d('RequestUrl: ${options.baseUrl}${options.path}?${Transformer.urlEncodeMap(options.queryParameters)}');
    }
    LogUtils.logger.d('RequestMethod: ${options.method}');
    LogUtils.logger.d('RequestHeaders:${options.headers}');
    LogUtils.logger.d('RequestContentType: ${options.contentType}');
    LogUtils.logger.d('RequestData: ${options.data.toString()}');
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response<dynamic> response, ResponseInterceptorHandler handler) {
    _endTime = DateTime.now();
    final int duration = _endTime.difference(_startTime).inMilliseconds;
    if (response.statusCode == ExceptionHandle.success) {
      LogUtils.logger.d('ResponseCode: ${response.statusCode}');
    } else {
      LogUtils.logger.e('ResponseCode: ${response.statusCode}');
    }
    // 输出结果
    LogUtils.logger.d('返回数据：${response.data}');
    LogUtils.logger.d('-------------------- End: $duration 毫秒 --------------------');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    LogUtils.logger.d('-------------------- Error --------------------');
    super.onError(err, handler);
  }
}




/// 放入平台名称
class PlatformInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
             options.headers['Platform'] = ApiConfig.latestGameName;

    super.onRequest(options, handler);
  }

}