import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/bank_card/entity/bank_list_entity.dart';
import 'package:sport_mobile/bank_card/model/bank_card_model.dart';
import 'package:sport_mobile/bank_card/utils/bank_utils.dart';
import 'package:sport_mobile/login/model/user_action.dart';
import 'package:sport_mobile/login/model/user_model.dart';
import 'package:sport_mobile/login/widgets/my_text_field.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/util/toast_utils.dart';
import 'package:sport_mobile/widgets/address_picker.dart/address_picker.dart';

import 'package:sport_mobile/widgets/my_app_bar.dart';
import 'package:sport_mobile/widgets/my_button.dart';

class AlipayAddPage extends StatefulWidget {
  const AlipayAddPage({super.key});

  @override
  _AlipayAddPageState createState() => _AlipayAddPageState();
}

class _AlipayAddPageState extends State<AlipayAddPage> {
  BankCardModel provider = BankCardModel();
  final TextEditingController _phonecontroller = TextEditingController();
  final TextEditingController _cardcontroller = TextEditingController();
  final TextEditingController _captchacontroller = TextEditingController();

  String bankName = "";

  String province = "";

  String city = "";

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _phonecontroller.dispose();
    _cardcontroller.dispose();
    _captchacontroller.dispose();
    super.dispose();
  }


  String joinAddress() {
    String joinStr = "";

    if (province != "" && city != "") {
      joinStr = "$province - $city";
    }

    return joinStr;
  }

  commit() async {
    if (_cardcontroller.text == "") {
      EasyLoading.showToast('请输入支付宝账号');
      return;
    }

  

    if (_phonecontroller.text == "") {
      EasyLoading.showToast('请输入银行预留手机号');
      return;
    }

    if (_captchacontroller.text == "") {
      EasyLoading.showToast('请输入短信验证码');
      return;
    }

    Map<String, String> params = {
      'banktype': '2',
      'bankNumber': '${_cardcontroller.text}',
      'bankMobile': '${_phonecontroller.text}',
      'code': '${_captchacontroller.text}',
    };

    BankBindResEntity res = await provider.addBank(params);
  

    EasyLoading.showToast(res.message!);
    if (res.status == '_0000') {
        if (context.mounted) {
              Navigator.pop(context,true);
            return;
        }
    }

    return;
  }

  void showAddressPicker(BuildContext context) {
    showModalBottomSheet(
        context: context,
        builder: (context) {
          return BottomSheet(
              enableDrag: false,
              onClosing: () {},
              builder: (context) {
                return   Container(
                  //  color: EMOColors.root_bg,
                  height: 250.0,

                  child: AddressPicker(
                    style: TextStyle(color: Colors.black, fontSize: 17),
                    mode: AddressPickerMode.provinceAndCity,
                    onTapEnter: (address) {
                      setState(() {
                        province = address.currentProvince!.province!;
                        city = address.currentCity!.city!;
                      });
                    },

                    onSelectedAddressChanged: (address) {
                      setState(() {
                        province = address.currentProvince!.province!;
                        city = address.currentCity!.city!;
                      });

                      print('${address.currentProvince!.province}');
                    },
                    // onTap: setArea,
                  ),
                );
              });
        });
  }

  void showPicker(BuildContext context, List<Bank> bankList) {
    List<Widget> _list;

    _list = bankList.map((e) {
     
      return Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              BankUtils.generateBankIcon(e.url!),
              color: BankUtils.generateBankColor(e.color!),
              size: 20,
            ),
            Gaps.hGap12,
            Text("${e.bank}")
          ],
        ),
      );
    }).toList();

    showModalBottomSheet(
        context: context,
        builder: (context) {
          return BottomSheet(
              enableDrag: false,
              onClosing: () {},
              builder: (context) {
                return Container(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(vertical: 10),
                        width: double.infinity,
                        color: Color(0xFFf5f5f5),
                        child:const  Text(
                          '选择银行',
                          style: TextStyles.textSize14,
                        ),
                      ),
                      Row(
                        //    mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              height: 250,
                              child: CupertinoPicker(
                                itemExtent: 40,
                                onSelectedItemChanged: (int index) {
                                  setState(() {
                                    bankName = bankList[index].bank!;
                                  });
                                },
                                children: _list,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              });
        });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFf2f2f2),
        appBar: const MyAppBar(
          centerTitle: '添加支付宝账号',
          backImgColor: Colors.white,
        ),
        body: ChangeNotifierProvider<BankCardModel>(create: (_) {
          return provider..getBankList();
        }, child: Consumer<UserModel>(builder: (context, user, child) {
          return SingleChildScrollView(
              child: Column(
            children: [
              Container(
                decoration: const BoxDecoration(
                    gradient: LinearGradient(
                  colors: [Color(0xFF4268cb), Color(0xFF8c7eec)], // 渐变色数组
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                )),
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(15),
                    topRight: Radius.circular(15),
                  ),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      alignment: Alignment.center,
                      color: Colors.white,
                      child: Consumer<BankCardModel>(
                          builder: (context, bankCardModel, child) {
                        return Column(
                          children: [
                            Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                decoration: const BoxDecoration(
                                  border: Border(
                                    bottom: BorderSide(
                                      color: Color(0xFFe3e3e3),
                                      width: 1,
                                    ),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      flex: 1,
                                      // child: Text(
                                      //   "请添加的银行卡（请绑定本人银行卡）",
                                      //   style: TextStyles.textSize14,
                                      // ),

                                      child: Text.rich(
                                        textAlign: TextAlign.start,
                                        overflow: TextOverflow.ellipsis,
                                        TextSpan(
                                          text: "请添加",
                                          style: TextStyles.textGray14,
                                          children: <TextSpan>[
                                            TextSpan(
                                              text: "${user.user!.realName}",
                                              style: const TextStyle(
                                                  color: Colors.black,
                                                  fontSize: Dimens.font_sp14,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            const TextSpan(
                                              text: "的支付宝（请绑定本人支付宝）",
                                              style: TextStyles.textGray14,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                )),
                         
                          
                            Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: Color(0xFFe3e3e3),
                                    width: 1,
                                  ),
                                ),
                              ),
                              child: Row(
                                children: [
                                  const Expanded(
                                      flex: 2,
                                      child: Text(
                                        "账号",
                                        style: TextStyles.textSize14,
                                      )),
                                  Expanded(
                                      flex: 8,
                                      child: MyTextField(
                                        key: const Key('alipayaccount'),
                                        controller: _cardcontroller,
                                        maxLength: 30,
                                       // keyboardType: TextInputType.phone,
                                        hintText: "请输入支付宝账号",
                                        onChanged: (String text) {},
                                      )),
                                ],
                              ),
                            ),
                           
                          ],
                        );
                      })),
                ),
              ),
              Gaps.vGap18,
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                alignment: Alignment.center,
                color: Colors.white,
                child: Column(
                  children: [
                    Container(
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Color(0xFFe3e3e3),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Expanded(
                              flex: 2,
                              child: Text(
                                "手机号",
                                style: TextStyles.textSize14,
                              )),
                          Expanded(
                              flex: 8,
                              child: MyTextField(
                                key: const Key('phone'),
                                controller: _phonecontroller,
                                maxLength: 11,
                                keyboardType: TextInputType.phone,
                                hintText: "请输入银行预留手机号",
                                withDecimal: true,
                                onChanged: (String text) {},
                              )),
                        ],
                      ),
                    ),
                    Container(
                      decoration: const BoxDecoration(
                          // border: Border(
                          //   bottom: BorderSide(
                          //     color: Color(0xFFe3e3e3),
                          //     width: 1,
                          //   ),
                          // ),
                          ),
                      child: Row(
                        children: [
                          const Expanded(
                              flex: 2,
                              child: Text(
                                "验证码",
                                style: TextStyles.textSize14,
                              )),
                          Expanded(
                              flex: 8,
                              child: MyTextField(
                                key: const Key('captcha'),
                                controller: _captchacontroller,
                                maxLength: 4,
                                //  keyboardType: TextInputType.phone,
                                hintText: "请输入4位验证码",
                                withDecimal: true,
                                onChanged: (String text) {},
                                getVCode: () async {
                                  final String phone = _phonecontroller.text;
                                  if (_phonecontroller.text.length == 11) {
                                    BaseEntity response =
                                        await UserAction.sendCaptcha(phone,'3');
                                    if (response.status != "_0000") {
                                      Toast.show(response.message);
                                      return false;
                                    }
                                    Toast.show(response.message);
                                    return true;
                                  } else {
                                    Toast.show("请输入有效的手机号");
                                    return false;
                                  }
                                },
                              )),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Gaps.vGap18,
              Container(
                height: 45,
                margin: EdgeInsets.symmetric(horizontal: 10),
                width: double.infinity,
                child: MyButton(
                  key: Key('push'),
                  onPressed: () {
                    commit();
                  },
                  text: "完成",
                  radius: 20,
                  fontSize: Dimens.font_sp14,
                  gradient: true,
                ),
              )
            ],
          ));
        })));
  }
}
