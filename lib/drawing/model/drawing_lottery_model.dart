import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:sport_mobile/drawing/entity/lottery_newest_detail_entity.dart';
import 'package:sport_mobile/game/data/match_data.dart';

import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/net/ws_utils.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/util/other_utils.dart';

class DrawingLotteryModel extends ChangeNotifier {
  bool isLoading = false;
  DrawingLotteryData drawingLotteryData = DrawingLotteryData();

  int typeID = 0;

//  String type = 'jczq';

  Future getDrawingLotteryNewestList(String type, String boutIndex) async {
    OrderType.values.forEach((key, orderType) {
      if (orderType.enname == type) {
        typeID = key;
      }
    });

    var parameters = {
      'version': Constant.version,
      'action': "ajaxKaiJiangForPhone",
      'type': "$typeID",
      'boutindex': "$boutIndex",
      'channel': Utils.getDeviceName()
    };

    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest =
        BaseRequestEntity(Api.getDrawingLotteryNewest, form);

    Map<String, dynamic> requestDataMap = baseRequest.toJson();
    isLoading = true;

    var respStr = await WsUtils.instance
        .sendMessageAndWaitForResponse(jsonEncode(requestDataMap));

    Map<String, dynamic> dataList = jsonDecode(respStr);

    LotteryNewestDetailEntity drawingLotteryEntity =
        LotteryNewestDetailEntity.fromJson(dataList);

    isLoading = false;

    drawingLotteryData = drawingLotteryEntity.data!;

    if (drawingLotteryData.redresult != null &&
        drawingLotteryData.blueresult != null) {
      List<String> redResultList = [];
      List<String> blueResultList = [];

      redResultList = drawingLotteryData.redresult!.split(',');
      blueResultList = drawingLotteryData.blueresult!.split(',');
      drawingLotteryData.awardNumber = [...redResultList, ...blueResultList];
    } else {
      List<String> resultList = [];
      resultList = drawingLotteryData.result!.split(',');
      drawingLotteryData.awardNumber = resultList;
    }

    switch (type) {
      case "dlt":
        drawingLotteryData.salesAmount = drawingLotteryData.dltSalesAmount;
        break;
      case "qxc":
        drawingLotteryData.salesAmount = drawingLotteryData.qxcSalesAmount;
        break;

      case "ssq":
        drawingLotteryData.salesAmount = drawingLotteryData.ssqSalesAmount;
        break;
      default:
    }

    notifyListeners();
  }
}
