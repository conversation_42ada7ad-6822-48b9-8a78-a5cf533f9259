import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:sport_mobile/drawing/entity/drawing_ft_lottery_history_entity.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/net/ws_utils.dart';
import 'package:sport_mobile/order/entity/order_entity.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/res/game_types.dart';
import 'package:sport_mobile/util/other_utils.dart';

class DrawingFTLotteryHistoryModel extends ChangeNotifier {
  bool isLoading = false;
  bool hasMoreData = true;
  int typeID = 0;

  List<FTBout> boutList = [];

  Future loadMore(int page, String action) async {
    isLoading = true;

    var tempList = await getDrawingFTLotteryHistoryList(page, action);

    if (tempList.isEmpty) {
      hasMoreData = false;
    } else {
      boutList.addAll(tempList);
    }

    isLoading = false;
    notifyListeners();
  }

  Future refreshData(int page, String type) async {
    isLoading = true;

    var tempList = await getDrawingFTLotteryHistoryList(page, type);

    if (tempList.isEmpty) {
      hasMoreData = false;
    } else {
      boutList = tempList;
      hasMoreData = true;
    }
    isLoading = false;

    notifyListeners();
  }

  Future<List<FTBout>> getDrawingFTLotteryHistoryList(
      int page, String type) async {
    OrderType.values.forEach((key, orderType) {
      if (orderType.enname == type) {
        typeID = key;
      }
    });

    var parameters = {
      'version': Constant.version,
      'action': "ajaxKaiJiangForPhone",
      'type': "$typeID",
      'page': "$page",
      'channel': Utils.getDeviceName()
    };
    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest =
        BaseRequestEntity(Api.getDrawingLotteryHistory, form);
    Map<String, dynamic> requestDataMap = baseRequest.toJson();

    var respStr = await WsUtils.instance
        .sendMessageAndWaitForResponse(jsonEncode(requestDataMap));

    Map<String, dynamic> dataList = jsonDecode(respStr);

    DrawingFTLotteryHistoryEntity drawingFTLotteryHistoryEntity =
        DrawingFTLotteryHistoryEntity.fromJson(dataList);

    drawingFTLotteryHistoryEntity.boutList!.forEach((FTBout bout) {
      switch (typeID) {
        case 13:

          ///四场进球
          bout.awardNumber = bout.lotteryDrawscResult!.split(' ');
          break;
        case 11: //任选9
        case 10: //足球14场
           bout.awardNumber = bout.lotteryDrawResult!.split(' ');
          break;
     
        case 14:
           bout.awardNumber = bout.lotteryDrawlcResult!.split(' ');
          break;
        default:
      }
    });

    

    return drawingFTLotteryHistoryEntity.boutList!;
  }



  String getActionType(OrderEntity item) {
    if (item.isCopy == "1") {
      return '跟单';
    } else if (item.isOptimization == "1" && item.isCopy != "1") {
      return '代购';
    } else if (item.isZhuihao == "0" &&
        item.isDaigou == "1" &&
        item.isAdd == "0" &&
        item.isOptimization == "0") {
      return '代购';
    } else if (item.isZhuihao == "1") {
      return '追号';
    } else if (item.isAdd == "1" && item.isCommunity == "1") {
      return '追号';
    } else if (item.isAdd == "0") {
      return '追加';
    } else if (item.isCommunity == "1") {
      return '合买';
    } else {
      return ' ';
    }
  }



}
