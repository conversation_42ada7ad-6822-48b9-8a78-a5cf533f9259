import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/drawing/entity/drawing_lottery_history_entity.dart';
import 'package:sport_mobile/drawing/entity/lottery_newest_detail_entity.dart';
import 'package:sport_mobile/drawing/model/drawing_lottery_history_model.dart';
import 'package:sport_mobile/drawing/model/drawing_lottery_model.dart';
import 'package:sport_mobile/drawing/page/drawing_page.dart';
import 'package:sport_mobile/game/routers/game_router.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/util/date_utils.dart';
import 'package:sport_mobile/util/image_utils.dart';
import 'package:sport_mobile/widgets/my_app_bar.dart';
import 'package:sport_mobile/widgets/state_layout.dart';
import 'package:sport_mobile/winner/entity/follow_winner_list_entity.dart';

class DrawingLotteryPage extends StatefulWidget {
  final String type;
  final String boutIndex;

  const DrawingLotteryPage(this.type, this.boutIndex, {super.key});

  @override
  // ignore: library_private_types_in_public_api
  _DrawingLotteryState createState() => _DrawingLotteryState();
}

class _DrawingLotteryState extends State<DrawingLotteryPage>
    with SingleTickerProviderStateMixin {
// class _WinnerPageState extends State<WinnerPage> {

// class _WinnerPageState extends State<WinnerPage>
//     with SingleTickerProviderStateMixin {
  // WinnerModel provider = WinnerModel();
  TabController? _tabController;
  final PageController _pageController = PageController();

  String? name;

  @override
  void initState() {
    super.initState();

    switch (widget.type) {
      case "dlt": //大乐透
        
        name = '大乐透';
        break;

      case "ssq":
        name = '双色球';
        break;
      case "qxc": //七星彩
        name = '七星彩';
        break;
      default:
    }



    _tabController = TabController(
      vsync: this,
      length: 2,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _tabController?.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Color(0xFFf2f2f2),
        appBar:  MyAppBar(
          title: "${name}",
        ),
        body: Container(
          //  height: 100.4,
          decoration: const BoxDecoration(
            border: Border(
              top: BorderSide(
                color: Color(0xFFe3e3e3),
                width: 0.2,
              ),
              bottom: BorderSide(
                color: Color(0xFFe3e3e3),
                width: 0.2,
              ),
            ),
          ),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Color(0xFFe3e5e4),
                      width: 2,
                    ),
                  ),
                ),
                child: TabBar(
                  controller: _tabController,
                  indicatorColor: Colors.red,
                  indicatorPadding:
                      EdgeInsets.symmetric(horizontal: 60, vertical: 0),

                  labelPadding: EdgeInsets.symmetric(vertical: 0),
                  padding: EdgeInsets.symmetric(vertical: 0),
                  labelColor: Colors.red,
                  unselectedLabelColor: Colors.black,
                  //  indicator: BoxDecoration(color: Colors.red),
                  tabs: [
                    Tab(
                      child: Container(
                        child: Align(
                          alignment: Alignment.center,
                          child: Text('最新开奖',
                              style: TextStyle(
                                  fontSize: Dimens.font_sp14,
                                  fontWeight: FontWeight.bold)),
                        ),
                      ),
                    ),
                    Tab(
                      child: Container(
                        child: Align(
                          alignment: Alignment.center,
                          child: Text('历史开奖',
                              style: TextStyle(
                                  fontSize: Dimens.font_sp14,
                                  fontWeight: FontWeight.bold)),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              MultiProvider(
                providers: [
                  ChangeNotifierProvider(create: (_) => DrawingLotteryModel()),

                  //历史开奖
                  ChangeNotifierProvider(
                      create: (_) => DrawingLotteryHistoryModel()),
                ],
                child: Expanded(
                    child: ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(15),
                          topRight: Radius.circular(15),
                        ),
                        child: Container(
                            alignment: Alignment.center,
                            color: Color(0xFFf2f2f2),
                            child: TabBarView(
                              controller: _tabController,
                              children: [
                                DrawingLotteryNewest(
                                    widget.type, widget.boutIndex, name!),
                                DrawingLotteryHistoryRefresh(widget.type), //跟单
                              ],
                            )))),
              ),
            ],
          ),
        ));
  }
}

class DrawingLotteryNewest extends StatefulWidget {
  final String type;

  final String name;
  final String boutIndex;

  const DrawingLotteryNewest(this.type, this.boutIndex, this.name, {super.key});

  @override
  _DrawingLotteryNewestState createState() => _DrawingLotteryNewestState();
}

class _DrawingLotteryNewestState extends State<DrawingLotteryNewest> {
  @override
  void initState() {
    super.initState();

    Provider.of<DrawingLotteryModel>(context, listen: false)
        .getDrawingLotteryNewestList(widget.type, widget.boutIndex);
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget generateTopLayout(drawingLotteryData) {
    Widget _layout;

    switch (widget.type) {
      case "dlt": //大乐透
        _layout = DLTTop9Layout(drawingLotteryData);

        break;

      case "ssq":
      case "qxc": //七星彩
        _layout = LotteryTop6Layout(drawingLotteryData);

        break;
      default:
        _layout = LotteryTop6Layout(drawingLotteryData);
    }

    return _layout;
  }

  List<Widget> buildDigitalItem(List<String> awardNumber) {
    List<Widget> numbers = [];

    int pos = widget.type == 'dlt' ? 5 : 6;

    for (int i = 0; i < awardNumber.length; i++) {
      var _tmpWidget;
      if (i < pos) {
        _tmpWidget = LotteryDigtalItem(
          name: awardNumber[i],
        );
      } else {
        _tmpWidget = LotteryDigtalItem(
          name: awardNumber[i],
          itemColor: const Color(0xFF3480ff),
        );
      }

      numbers.add(_tmpWidget);
    }

    return numbers;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DrawingLotteryModel>(builder: (context, model, child) {
      return Container(
        child: Column(
          children: [
            Expanded(
                child: ListView(
              children: [
                model.isLoading == false
                    ? Container(
                        child: Column(
                          children: [
                            Gaps.vGap16,
                            Container(
                              child: Text.rich(
                                textAlign: TextAlign.start,
                                overflow: TextOverflow.ellipsis,
                                TextSpan(
                                  text:
                                      "第${model.drawingLotteryData.boutIndex}期 ",
                                  style: TextStyles.textSize14,
                                  children: <TextSpan>[
                                    TextSpan(
                                      text: "开奖时间：",
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontSize: Dimens.font_sp14,
                                      ),
                                    ),
                                    TextSpan(
                                      text: DateFormatUtils.getYearMonthDay(
                                          model.drawingLotteryData.stopTime!),
                                      style: TextStyles.textSize14,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Gaps.vGap16,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: buildDigitalItem(
                                  model.drawingLotteryData.awardNumber!),
                            ),
                            Gaps.vGap16,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                      border:
                                          Border.all(color: Color(0xFFbbbbbb))),
                                  child: Column(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 40, vertical: 4),
                                        decoration: BoxDecoration(
                                            color: Color(0xFFdee6e9)),
                                        child: Text(
                                          "本期销售(元)",
                                          style: TextStyles.textSize14,
                                        ),
                                      ),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 0, vertical: 4),
                                        decoration: BoxDecoration(),
                                        child: Text(
                                          "${model.drawingLotteryData.salesAmount}",
                                          style: TextStyles.textSize14,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                Gaps.hGap18,
                                Container(
                                  decoration: BoxDecoration(
                                      border:
                                          Border.all(color: Color(0xFFbbbbbb))),
                                  child: Column(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 40, vertical: 4),
                                        decoration: BoxDecoration(
                                            color: Color(0xFFdee6e9)),
                                        child: Text(
                                          "奖池滚存(元)",
                                          style: TextStyles.textSize14,
                                        ),
                                      ),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 0, vertical: 4),
                                        decoration: BoxDecoration(),
                                        child: Text(
                                          "${model.drawingLotteryData.bonusPool}",
                                          style: TextStyles.textSize14,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Gaps.vGap18,
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 28, vertical: 4),
                              child: Text(
                                "本期中奖详情",
                                style: TextStyles.textBold16,
                              ),
                            ),
                            generateTopLayout(model.drawingLotteryData)
                          ],
                        ),
                      )
                    : StateLayout(
                        type: StateType.loading,
                      )
              ],
            )),
            GestureDetector(
              onTap: () {


                String routePath;

                switch (widget.type) {
                  case "dlt":
                    routePath = GameRouter.dltPage;
                    break;
                  case "ssq":
                    routePath = GameRouter.ssqPage;
                    break;

                  case "qxc":
                    routePath = GameRouter.qxcPage;
                    break;

                  default:
                    routePath = GameRouter.dltPage;
                }



                NavigatorUtils.push(context, routePath, replace: true);

              },
              child: Container(
                height: 65,
                alignment: Alignment.center,
                color: Colors.red,
                child: Text(
                  "${widget.name}下单",
                  style: TextStyles.textWhite16,
                ),
              ),
            )
          ],
        ),
      );
    });
  }
}

///
class WinnerFollowItem extends StatelessWidget {
  final FollowWinner winner;

  final VoidCallback? onPressed;

  final String defaultImageAsset = ImageUtils.getImgPath('mine/avatar_default');

  WinnerFollowItem(this.winner, {this.onPressed, super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed ?? onPressed,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ClipOval(
              child: Image.network(
                winner.headImg != null ? winner.headImg! : "",
                width: 45,
                height: 45,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // 加载失败时显示默认图片
                  return Image.asset(
                    defaultImageAsset,
                    width: 45,
                    height: 45,
                    fit: BoxFit.cover,
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  // 加载中显示默认图片
                  if (loadingProgress == null) return child;
                  return Image.asset(
                    defaultImageAsset,
                    width: 45,
                    height: 45,
                    fit: BoxFit.cover,
                  );
                },
              ),
            ),
            Gaps.hGap10,
            Expanded(
                child: Container(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFe3e3e3),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("${winner.petName}"),
                      Gaps.vGap4,
                      Row(
                        children: [
                          Text.rich(
                            textAlign: TextAlign.start,
                            overflow: TextOverflow.ellipsis,
                            TextSpan(
                              text: "七日回报率",
                              style: TextStyle(fontSize: Dimens.font_sp12),
                              children: <TextSpan>[
                                TextSpan(
                                  text: "${winner.rate}",
                                  style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: Dimens.font_sp12,
                                      fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                          ),
                          Gaps.hGap10,
                          const Text(
                            "近7日 ",
                            style: TextStyle(fontSize: Dimens.font_sp12),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 4, vertical: 1),
                            decoration: BoxDecoration(
                                border: Border.all(
                                  color: Colors.red, // 边框颜色为红色
                                  width: 1, // 边框宽度为1像素
                                ),
                                borderRadius: BorderRadius.circular(5.0)),
                            child: Text(
                              "${winner.zhong}",
                              style: TextStyle(
                                  color: Colors.red,
                                  fontSize: Dimens.font_sp12),
                            ),
                          )
                        ],
                      ),
                      Gaps.vGap4,
                      Row(
                        children: [
                          Text(
                            "钻粉:${winner.fansNum}  ",
                            style: TextStyle(fontSize: Dimens.font_sp12),
                          ),
                          Text(
                            "关注:${winner.attenNum}  ",
                            style: TextStyle(fontSize: Dimens.font_sp12),
                          ),
                          Text(
                            "推单数:${winner.shareNum}  ",
                            style: TextStyle(fontSize: Dimens.font_sp12),
                          ),
                        ],
                      )
                    ],
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                    decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.grey, // 边框颜色为红色
                          width: 1, // 边框宽度为1像素
                        ),
                        borderRadius: BorderRadius.circular(5.0)),
                    child: Text(
                      "已关注",
                      style: TextStyle(
                          color: Colors.grey, fontSize: Dimens.font_sp12),
                    ),
                  )
                ],
              ),
            ))
          ],
        ),
      ),
    );
  }
}

class WinnerPushOrderItem extends StatelessWidget {
  final FollowWinner winner;
  final String defaultImageAsset = ImageUtils.getImgPath('mine/avatar_default');

  WinnerPushOrderItem(this.winner, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ClipOval(
            child: Image.network(
              winner.headImg != null ? winner.headImg! : "",
              width: 45,
              height: 45,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                // 加载失败时显示默认图片
                return Image.asset(
                  defaultImageAsset,
                  width: 45,
                  height: 45,
                  fit: BoxFit.cover,
                );
              },
              loadingBuilder: (context, child, loadingProgress) {
                // 加载中显示默认图片
                if (loadingProgress == null) return child;
                return Image.asset(
                  defaultImageAsset,
                  width: 45,
                  height: 45,
                  fit: BoxFit.cover,
                );
              },
            ),
          ),
          Gaps.hGap10,
          Expanded(
              child: Container(
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Color(0xFFe3e3e3),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("${winner.petName}"),
                    Gaps.vGap4,
                    Row(
                      children: [
                        Text.rich(
                          textAlign: TextAlign.start,
                          overflow: TextOverflow.ellipsis,
                          TextSpan(
                            text: "七日回报率",
                            style: TextStyle(fontSize: Dimens.font_sp12),
                            children: <TextSpan>[
                              TextSpan(
                                text: "${winner.rate}",
                                style: const TextStyle(
                                    color: Colors.red,
                                    fontSize: Dimens.font_sp12,
                                    fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                        Gaps.hGap10,
                        const Text(
                          "近7日 ",
                          style: TextStyle(fontSize: Dimens.font_sp12),
                        ),
                        Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                          decoration: BoxDecoration(
                              border: Border.all(
                                color: Colors.red, // 边框颜色为红色
                                width: 1, // 边框宽度为1像素
                              ),
                              borderRadius: BorderRadius.circular(5.0)),
                          child: Text(
                            "${winner.zhong}",
                            style: TextStyle(
                                color: Colors.red, fontSize: Dimens.font_sp12),
                          ),
                        )
                      ],
                    ),
                    Gaps.vGap4,
                    Row(
                      children: [
                        Text(
                          "钻粉:${winner.fansNum}  ",
                          style: TextStyle(fontSize: Dimens.font_sp12),
                        ),

                        Text(
                          "关注:${winner.attenNum}  ",
                          style: TextStyle(fontSize: Dimens.font_sp12),
                        ),
                        Text(
                          "推单数:${winner.shareNum}  ",
                          style: TextStyle(fontSize: Dimens.font_sp12),
                        ),
                      ],
                    )
                  ],
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                  decoration: BoxDecoration(
                      border: Border.all(
                        color: Colors.grey, // 边框颜色为红色
                        width: 1, // 边框宽度为1像素
                      ),
                      borderRadius: BorderRadius.circular(5.0)),
                  child: Text(
                    "已关注",
                    style: TextStyle(
                        color: Colors.grey, fontSize: Dimens.font_sp12),
                  ),
                )
              ],
            ),
          ))
        ],
      ),
    );
  }
}

class DrawingLotteryHistoryRefresh extends StatefulWidget {
  final String type;

  const DrawingLotteryHistoryRefresh(this.type, {super.key});

  @override
  _DrawingLotteryHistoryRefreshState createState() =>
      _DrawingLotteryHistoryRefreshState();
}

class _DrawingLotteryHistoryRefreshState
    extends State<DrawingLotteryHistoryRefresh> {
  int page = 0;

  @override
  void initState() {
    super.initState();
    Provider.of<DrawingLotteryHistoryModel>(context, listen: false)
        .refreshData(page, widget.type);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DrawingLotteryHistoryModel>(
        builder: (context, model, child) {
      if (model.boutList.isEmpty && model.isLoading) {
        //加载数据
        return const StateLayout(
          type: StateType.loading,
        );
      }

      if (model.boutList.isEmpty && model.isLoading == false) {
        //加载完毕，没有数据
        return const StateLayout(
          type: StateType.empty,
        );
      }

      return NotificationListener(
          onNotification: (ScrollNotification note) {
            if (note.metrics.pixels == note.metrics.maxScrollExtent) {
              page = page + 1;
              Provider.of<DrawingLotteryHistoryModel>(context, listen: false)
                  .loadMore(page, widget.type);
            }
            return true;
          },
          child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10)),
              ),
              child: RefreshIndicator(
                onRefresh: () {
                  page = 1;
                  return model.refreshData(page, widget.type);
                },
                child: Consumer<DrawingLotteryHistoryModel>(
                  builder: (_, provider, child) {
                    return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10.0),
                        child: model.boutList.isEmpty
                            ? const StateLayout(type: StateType.loading)
                            : ListView.builder(
                                itemCount: model.boutList.length + 1,
                                itemBuilder: (context, index) {
                                  return index < model.boutList.length
                                      ? DrawingLotteryHistoryItem(
                                          widget.type,
                                          bout: model.boutList[index],
                                        )
                                      : MoreWidget(model.boutList.length,
                                          model.hasMoreData, 10);
                                }));
                  },
                ),
              )));
    });
  }
}

class MoreWidget extends StatelessWidget {
  const MoreWidget(this.itemCount, this.hasMore, this.pageSize, {super.key});

  final int itemCount;
  final bool hasMore;
  final int pageSize;

  @override
  Widget build(BuildContext context) {
    const TextStyle style = TextStyle(color: Color(0x8A000000));
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          if (hasMore) const CupertinoActivityIndicator(),
          if (hasMore) Gaps.hGap5,

          /// 只有一页的时候，就不显示FooterView了
          Text(hasMore ? '正在加载中...' : (itemCount < pageSize ? '' : '没有了呦~'),
              style: style),
        ],
      ),
    );
  }
}

class DrawingLotteryHistoryItem extends StatelessWidget {
  final Bout bout;
  final String type;

  const DrawingLotteryHistoryItem(this.type, {super.key, required this.bout});

  List<Widget> buildDigitalItem(List<String> awardNumber) {
    List<Widget> numbers = [];

    int pos = type == 'dlt' ? 5 : 6;

    for (int i = 0; i < awardNumber.length; i++) {
      var _tmpWidget;
      if (i < pos) {
        _tmpWidget = LotteryDigtalItem(
          name: awardNumber[i],
        );
      } else {
        _tmpWidget = LotteryDigtalItem(
          name: awardNumber[i],
          itemColor: const Color(0xFF3480ff),
        );
      }

      numbers.add(_tmpWidget);
    }

    return numbers;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // NavigatorUtils.push(context, OrderRouter.orderDetailPage,
        //     arguments: {"id": bout.orderId, "from": '0'});
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFe3e3e3),
              width: 1,
            ),
          ),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "开奖时间:${DateFormatUtils.getYearMonthDay(bout.stopTime!)}",
                  style: TextStyles.textGray14,
                ),
                Text(
                  "第${bout.boutIndex}期",
                  style: TextStyles.textGray14,
                ),
              ],
            ),
            Gaps.vGap12,
            Row(
              children: [
                ...buildDigitalItem(
                  bout.awardNumber!,
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}

class DLTTop9Layout extends StatelessWidget {
  final DrawingLotteryData drawingLotteryData;

  DLTTop9Layout(this.drawingLotteryData, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 15),
            decoration: BoxDecoration(color: Color(0xFFdee6e9)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    "奖项",
                    style: TextStyles.textBold16,
                  ),
                )),
                Expanded(
                    child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    "中奖注数",
                    style: TextStyles.textBold16,
                  ),
                )),
                Expanded(
                    child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    "每注奖金(元)",
                    style: TextStyles.textBold16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "一等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num1}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money1}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "二等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num2}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money2}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "三等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num3}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money3}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "四等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num4}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money4}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "五等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num5}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money5}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "六等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num6}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money6}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "七等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num7}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money7}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "八等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num8}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money8}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "九等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num9}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money9}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class LotteryTop6Layout extends StatelessWidget {
  final DrawingLotteryData drawingLotteryData;

  LotteryTop6Layout(this.drawingLotteryData, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 15),
            decoration: BoxDecoration(color: Color(0xFFdee6e9)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    "奖项",
                    style: TextStyles.textBold16,
                  ),
                )),
                Expanded(
                    child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    "中奖注数",
                    style: TextStyles.textBold16,
                  ),
                )),
                Expanded(
                    child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    "每注奖金(元)",
                    style: TextStyles.textBold16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "一等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num1}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money1}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "二等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num2}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money2}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "三等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num3}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money3}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "四等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num4}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money4}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "五等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num5}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money5}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 0),
            decoration: BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "六等奖",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.num6}",
                    style: TextStyles.textSize16,
                  ),
                )),
                Expanded(
                    child: Container(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                      bottom: BorderSide(
                        color: Color(0xFFe3e3e3),
                        width: 1,
                      ),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "${drawingLotteryData.money6}",
                    style: TextStyles.textSize16,
                  ),
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
