class DrawingFTLotteryHistoryEntity {
  List<FTBout>? boutList;
  String? status;

  DrawingFTLotteryHistoryEntity({this.boutList, this.status});

  DrawingFTLotteryHistoryEntity.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      boutList = <FTBout>[];
      json['data'].forEach((v) {
        boutList!.add(FTBout.fromJson(v));
      });
    }
    status = json['status'];
  }

}

class FTBout {
  int? id;
  String? lotteryDrawNum;
  String? lotteryDrawlcResult;
  String? lotteryDrawResult;
  String? lotteryDrawscResult;
  String? lotteryDrawTime;
  String? lcPrize;
  String? lcBets;
  int? totalSaleAmountlc;
  List<String>? awardNumber;

  FTBout(
      {this.id,
      this.lotteryDrawNum,
      this.awardNumber,
      this.lotteryDrawlcResult,
      this.lotteryDrawResult,
      this.lotteryDrawscResult,
      this.lotteryDrawTime,
      this.lcPrize,
      this.lcBets,
      this.totalSaleAmountlc});

  FTBout.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    lotteryDrawNum = json['lotteryDrawNum'];
    lotteryDrawlcResult = json['lotteryDrawlcResult'];
    lotteryDrawResult = json['lotteryDrawResult'];
    lotteryDrawscResult = json['lotteryDrawscResult'];
    lotteryDrawTime = json['lotteryDrawTime'];
    lcPrize = json['lc_prize'];
    lcBets = json['lc_bets'];
    totalSaleAmountlc = json['totalSaleAmountlc'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['lotteryDrawNum'] = this.lotteryDrawNum;
    data['lotteryDrawlcResult'] = this.lotteryDrawlcResult;
    data['lotteryDrawTime'] = this.lotteryDrawTime;
    data['lc_prize'] = this.lcPrize;
    data['lc_bets'] = this.lcBets;
    data['totalSaleAmountlc'] = this.totalSaleAmountlc;
    return data;
  }
}
