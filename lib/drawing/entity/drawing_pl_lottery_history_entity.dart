import 'package:sport_mobile/drawing/entity/drawing_pl_lottery_newest_entity.dart';

class DrawingPLLotteryHistoryEntity {
  List<LotteryPLBout>? data;
  String? status;

  DrawingPLLotteryHistoryEntity({this.data, this.status});

  DrawingPLLotteryHistoryEntity.fromJson(Map<String, dynamic> json) {

    if (json['data'] != null) {
      data = <LotteryPLBout>[];
      json['data'].forEach((v) {
        data!.add(LotteryPLBout.fromJson(v));
      });
    }
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['status'] = this.status;
    return data;
  }
}

class Data {
  int? id;
  String? boutIndex;
  String? lotteryNumberFive;
  String? stopTime;
  String? plwSalesAmount;
  String? firstBets;

  Data(
      {this.id,
      this.boutIndex,
      this.lotteryNumberFive,
      this.stopTime,
      this.plwSalesAmount,
      this.firstBets});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    boutIndex = json['bout_index'];
    lotteryNumberFive = json['lottery_number_five'];
    stopTime = json['stop_time'];
    plwSalesAmount = json['plw_sales_amount'];
    firstBets = json['first_bets'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['bout_index'] = this.boutIndex;
    data['lottery_number_five'] = this.lotteryNumberFive;
    data['stop_time'] = this.stopTime;
    data['plw_sales_amount'] = this.plwSalesAmount;
    data['first_bets'] = this.firstBets;
    return data;
  }
}
