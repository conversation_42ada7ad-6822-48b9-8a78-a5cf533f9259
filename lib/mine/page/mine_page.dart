import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/certified/certified_router.dart';
import 'package:sport_mobile/coupon/coupon_router.dart';
import 'package:sport_mobile/default/widgets/my_custom_button.dart';
import 'package:sport_mobile/deposit/deposit_router.dart';
import 'package:sport_mobile/fans/fans_router.dart';
import 'package:sport_mobile/faq/faq_router.dart';
import 'package:sport_mobile/giveaway/giveaway.dart';
import 'package:sport_mobile/login/model/user_model.dart';
import 'package:sport_mobile/mine/widgets/jackpot_dialog.dart';
import 'package:sport_mobile/mine/widgets/logout_dialog.dart';
import 'package:sport_mobile/mine/widgets/mine_icon_button.dart';
import 'package:sport_mobile/order/order_router.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/transaction/order_router.dart';
import 'package:sport_mobile/promotion/promotion_router.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/store/store_router.dart';
import 'package:sport_mobile/transfer/transfer_router.dart';
import 'package:sport_mobile/util/device_utils.dart';
import 'package:sport_mobile/util/image_utils.dart';
import 'package:sport_mobile/widgets/avatar_widget.dart';
import 'package:sport_mobile/widgets/load_image.dart';
import 'package:sport_mobile/widgets/update_dialog.dart';
import 'package:sport_mobile/withdraw/withdraw_router.dart';

final shorebirdCodePush = ShorebirdUpdater();

class MinePage extends StatefulWidget {
  const MinePage({super.key});

  @override
  _MinePageState createState() => _MinePageState();
}

class _MinePageState extends State<MinePage>
    with
        AutomaticKeepAliveClientMixin<MinePage>,
        SingleTickerProviderStateMixin {
  String version = "";

  String patchNumber = "";
  bool isUpdateAvailable = false;

  void _showJackpotDialog(String? amount) {
    showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (_) => JackpotDialog(
              () {},
              money: amount,
            ));
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      version = await getVersion();
      String versionKey = Device.isAndroid == true
          ? Constant.androidVersion
          : Constant.iosVersion;
      String? currentVersion = SpUtil.getString(versionKey);
      if (version != currentVersion) {
        isUpdateAvailable = true;
      }
      UserModel model = Provider.of<UserModel>(context, listen: false);
      model.getFans();
      model.getJackpot().then((res) {
        if (res.status == '_0000') {
          _showJackpotDialog(res.money);
          model.getJackpot(action: 'burned');
        }
      });
    });
  }

  Future<void> _getPatchNumber() async {
    Patch? patch = await shorebirdCodePush.readCurrentPatch();
    if (patch != null &&  patch.number > 0) {
        patchNumber = "(${patch.number})";
    }
    setState(() {});
  }

  Future<String> getVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    version = packageInfo.version;


    await _getPatchNumber();

    return version;
  }

  @override
  Widget build(BuildContext context) {
    print(MediaQuery.of(context).size.width);
    super.build(context);
    return Scaffold(
        backgroundColor: const Color(0xFFFFFFFF),
        body: Consumer<UserModel>(builder: (context, userModel, child) {
          return SingleChildScrollView(
              child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                // height: 204.w,
                width: double.infinity,
                decoration: BoxDecoration(
                    image: DecorationImage(
                  image: ImageUtils.getAssetImage('mine/bg'),
                  fit: BoxFit.cover, // 设置图片的适应方式
                )),
                child: SafeArea(
                    child: Column(
                  children: [
                    Container(
                      height: 58.w,
                      child: Row(
                        children: [
                          GestureDetector(
                            onTap: () {
                              UserModel user = Provider.of<UserModel>(context,
                                  listen: false);
                              if (user.hasVerifyRealName()) {
                                NavigatorUtils.push(
                                    context, CertifiedRouter.certifiedPage);
                              } else {
                                NavigatorUtils.push(context,
                                    CertifiedRouter.certifiedRealNamePage);
                              }
                            },
                            child: AvatarWidget(
                              defaultImageAsset:
                                  ImageUtils.getImgPath('mine/avatar_default'),
                              imageUrl: userModel.user!.tailorUser!,
                              height: 60.w,
                              width: 60.w,
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                child: Text(userModel.user!.petName!,
                                    style: TextStyle(
                                        height: 1,
                                        fontSize: Dimens.font_sp14,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF5C5C5C))),
                              ),
                              if (userModel.user!.shopName! != "")
                                Text(userModel.user!.shopName!,
                                    style: TextStyle(
                                        fontSize: Dimens.font_sp12,
                                        color: Color(0xFF5C5C5C))),

                              Container(

                                  //   color: Colors.red,
                                  child: GestureDetector(
                                onTap: () {
                                  NavigatorUtils.push(
                                      context, FansRouter.fansPage);
                                },
                                child: Text.rich(
                                  //   textAlign: TextAlign.start,
                                  // overflow: TextOverflow.ellipsis,
                                  TextSpan(
                                    text: "钻粉 ",
                                    style: TextStyle(
                                      height: 1,
                                      fontSize: Dimens.gap_dp12,
                                    ),
                                    children: <TextSpan>[
                                      TextSpan(
                                        text:
                                            "${userModel.fans?.fansList?.length ?? 0}",
                                        style: const TextStyle(
                                          color: Colors.red,
                                          height: 1,
                                          fontSize: Dimens.font_sp12,
                                        ),
                                      ),
                                      const TextSpan(
                                        text: " 人",
                                        style: TextStyle(
                                            height: 1,
                                            fontSize: Dimens.font_sp12),
                                      ),
                                    ],
                                  ),
                                ),
                              )),
                              // MyCustomButton(
                              //   padding: const EdgeInsets.symmetric(
                              //       horizontal: 15, vertical: 0),
                              //   text:
                              //       '钻粉 ${userModel.fans?.fansList?.length ?? 0} 人',
                              //   onPressed: () {
                              //     NavigatorUtils.push(
                              //         context, FansRouter.fansPage);
                              //   },
                              //   bgColor: Colors.black.withOpacity(0.1),
                              //   minHeight: 20,
                              //   textColor: Colors.white,
                              //   fontSize: Dimens.font_sp12,
                              // ),
                            ],
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Container(
                      //   height: 90.w,
                      margin: EdgeInsets.symmetric(horizontal: 14),
                      padding:
                          EdgeInsets.symmetric(horizontal: 14, vertical: 14),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.4),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  LoadAssetImage(
                                    "mine/icon_balance",
                                    width: 18,
                                    height: 18,
                                  ),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("我的余额",
                                          style: TextStyle(
                                              height: 1.5,
                                              fontSize: Dimens.font_sp14,
                                              color: Color(0xFF5C5C5C))),
                                      Text(
                                          "¥${userModel.user!.lotterybalance ?? 0}",
                                          style: TextStyle(
                                              height: 1,
                                              fontWeight: FontWeight.bold,
                                              fontSize: Dimens.font_sp12,
                                              color: Color(0xFF5C5C5C))),
                                    ],
                                  )
                                ],
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  LoadAssetImage(
                                    "mine/icon_bonus",
                                    width: 18,
                                    height: 18,
                                  ),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("我的彩金",
                                          style: TextStyle(
                                              height: 1.5,
                                              fontSize: Dimens.font_sp14,
                                              color: Color(0xFF5C5C5C))),
                                      Text(
                                          "¥${userModel.user!.mosaicGold ?? 0}",
                                          style: TextStyle(
                                              height: 1,
                                              fontWeight: FontWeight.bold,
                                              fontSize: Dimens.font_sp12,
                                              color: Color(0xFF5C5C5C))),
                                    ],
                                  )
                                ],
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 16.w,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              FinanceButton(
                                color: Color(0xFFFA8B8B),
                                iconImg: "mine/icon_deposit",
                                text: "充值",
                                onPressed: () {
                                  NavigatorUtils.push(
                                      context, DepositRouter.depositPage);
                                },
                              ),
                              FinanceButton(
                                color: Color(0xFF74F587),
                                iconImg: "mine/icon_withdraw",
                                text: "提现",
                                onPressed: () {
                                  NavigatorUtils.push(
                                      context, WithdrawRouter.withdrawPage);
                                },
                              ),
                              FinanceButton(
                                color: Color(0xFF6aa8f9),
                                iconImg: "mine/icon_giveaway",
                                text: "赠送",
                                onPressed: () {
                                  NavigatorUtils.push(
                                      context, GiveawayRouter.giveawayPage);
                                },
                              ),
                              FinanceButton(
                                color: Color(0xFFe38bf4),
                                iconImg: "mine/icon_transfer",
                                text: "转充",
                                onPressed: () {
                                  NavigatorUtils.push(
                                      context, TransferRouter.transferPage);
                                },
                              ),
                            ],
                          )
                        ],
                      ),
                    )
                  ],
                )),
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 14),
                decoration: const BoxDecoration(
                  color: Color(0xFFffffff),
                ),
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 14),
                      child: Column(
                        children: [
                          SizedBox(
                            height: 16.w,
                          ),
                          Container(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('我的订单',
                                    style: TextStyle(
                                        fontSize: Dimens.gap_dp12,
                                        fontWeight: FontWeight.w600)),
                                GestureDetector(
                                  onTap: () {
                                    NavigatorUtils.push(context,
                                        '${OrderRouter.orderPage}?action=all');
                                  },
                                  child: const Text('全部订单',
                                      style: TextStyle(
                                        fontSize: Dimens.font_sp12,
                                        fontWeight: FontWeight.w400,
                                      )),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 16.w,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              OrderButton(
                                  onPressed: () {
                                    NavigatorUtils.push(
                                        context, OrderRouter.orderPushPage);
                                  },
                                  iconImg: "mine/icon_push_order",
                                  text: '我的推单',
                                  colors: [
                                    Color(0xFFFFBFBF),
                                    Color(0xFFFF6464),
                                  ]),
                              OrderButton(
                                  onPressed: () {
                                    NavigatorUtils.push(
                                        context, OrderRouter.orderFollowPage);
                                  },
                                  iconImg: "mine/icon_pending_order",
                                  text: '我的跟单',
                                  colors: [
                                    Color(0xFF59FE72),
                                    Color(0xFF02CB20),
                                  ]),
                              OrderButton(
                                  onPressed: () {
                                    NavigatorUtils.push(
                                        context, OrderRouter.orderResultPage);
                                  },
                                  iconImg: "mine/icon_record",
                                  text: '中奖记录',
                                  colors: [
                                    Color(0xFFC4C4FF),
                                    Color(0xFF8C8CF0),
                                  ]),
                              OrderButton(
                                  onPressed: () {
                                    NavigatorUtils.push(
                                        context, OrderRouter.orderPendingPage);
                                  },
                                  iconImg: "mine/icon_trophy",
                                  text: '待开奖',
                                  colors: [
                                    Color(0xFFED96FE),
                                    Color(0xFFBE30DB),
                                  ]),
                            ],
                          ),
                          SizedBox(
                            height: 16.w,
                          ),
                          LoadAssetImage(
                            "mine/mine_cs",
                            fit: BoxFit.cover,
                          ),
                          SizedBox(
                            height: 16.w,
                          ),
                          Container(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('更多服务',
                                    style: TextStyle(
                                        fontSize: Dimens.gap_dp12,
                                        fontWeight: FontWeight.w600)),
                                Row(
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        NavigatorUtils.push(context,
                                            '${OrderRouter.orderPage}?action=all');
                                      },
                                      child: Text.rich(
                                        TextSpan(
                                          text: "版本号：",
                                          style: TextStyle(
                                              fontSize: Dimens.font_sp12,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFF5c5c5c)),
                                          children: <TextSpan>[
                                            TextSpan(
                                              text: "${version}${patchNumber}",
                                              style: TextStyle(
                                                  fontSize: Dimens.font_sp12,
                                                  fontWeight: FontWeight.w400,
                                                  color: Color(0xFF5c5c5c)),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    if (isUpdateAvailable == true)
                                      Container(
                                        height: 25,
                                        margin: EdgeInsets.only(left: 4),
                                        child: MyCustomButton(
                                          textColor: Colors.white,
                                          bgColor: Colors.red,
                                          onPressed: () {
                                            showDialog<void>(
                                                context: context,
                                                builder: (_) =>
                                                    const UpdateDialog());
                                          },
                                          text: "更新",
                                        ),
                                      )
                                  ],
                                )
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 16.w,
                          ),
                          Container(
                            child: Column(
                              children: [
                                Container(
                                  height: 18.w,
                                  child: ServiceHorionzalItem(
                                      text: "账单明细",
                                      iconImg: "mine/icon_order",
                                      onPressed: () {
                                        NavigatorUtils.push(context,
                                            TransactionRouter.transactionPage);
                                      }),
                                ),
                                SizedBox(
                                  height: 15.w,
                                ),
                                Container(
                                  height: 18,
                                  child: ServiceHorionzalItem(
                                    text: "实名认证",
                                    iconImg: "mine/icon_certified",
                                    onPressed: () {
                                      UserModel user = Provider.of<UserModel>(
                                          context,
                                          listen: false);
                                      if (user.hasVerifyRealName()) {
                                        NavigatorUtils.push(context,
                                            CertifiedRouter.certifiedPage);
                                      } else {
                                        NavigatorUtils.push(
                                            context,
                                            CertifiedRouter
                                                .certifiedRealNamePage);
                                      }
                                    },
                                  ),
                                ),
                                SizedBox(
                                  height: 15.w,
                                ),
                                Container(
                                  height: 18,
                                  child: ServiceHorionzalItem(
                                    text: "优惠券",
                                    iconImg: "mine/icon_coupon",
                                    onPressed: () {
                                      NavigatorUtils.push(context,
                                          "${CouponRouter.couponPage}/normal");
                                    },
                                  ),
                                ),
                                SizedBox(
                                  height: 15.w,
                                ),

                                Container(
                                  height: 18,
                                  child: ServiceHorionzalItem(
                                    text: "我的推广",
                                    iconImg: "mine/icon_share",
                                    onPressed: () {
                                      NavigatorUtils.push(context,
                                          PromotionRouter.promotionPage);
                                    },
                                  ),
                                ),
                                SizedBox(
                                  height: 15.w,
                                ),
                                Container(
                                  height: 18,
                                  child: ServiceHorionzalItem(
                                    text: "在线客服",
                                    iconImg: "mine/icon_contact",
                                    onPressed: () {
                                      String? csurl =
                                          SpUtil.getString(Constant.csurl);
                                      if (csurl != null) {
                                        NavigatorUtils.goWebViewPage(
                                            context, '联系客服', csurl);
                                      }
                                    },
                                  ),
                                ),
                                SizedBox(
                                  height: 15.w,
                                ),
                                Container(
                                  height: 18,
                                  child: ServiceHorionzalItem(
                                    text: "问题中心",
                                    iconImg: "mine/icon_qa",
                                    onPressed: () {
                                      NavigatorUtils.push(
                                          context, FaqRouter.faqPage);
                                    },
                                  ),
                                ),
                                SizedBox(
                                  height: 15.w,
                                ),
                                Container(
                                  height: 18,
                                  child: ServiceHorionzalItem(
                                    text: "我的彩店",
                                    iconImg: "mine/icon_lottery_store",
                                    onPressed: () {
                                      NavigatorUtils.push(
                                          context, StoreRouter.storePage);
                                    },
                                  ),
                                ),
                                SizedBox(
                                  height: 15.w,
                                ),
                                Container(
                                  height: 18,
                                  child: ServiceHorionzalItem(
                                      text: "退出登录",
                                      iconImg: "mine/icon_logout",
                                      onPressed: _showLogoutDialog),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Gaps.vGap10,
                  ],
                ),
              ),
              Gaps.vGap12,
            ],
          ));
        }));
  }

  void _showLogoutDialog() {
    showDialog<void>(context: context, builder: (_) => const LogoutDialog());
  }
}

class ServiceHorionzalItem extends StatelessWidget {
  final String iconImg;

  const ServiceHorionzalItem(
      {required this.text,
      required this.iconImg,
      required this.onPressed,
      super.key});

  final GestureTapCallback? onPressed;

  final String text;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: onPressed,
        behavior: HitTestBehavior.opaque,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                LoadAssetImage(
                  iconImg,
                  width: 18,
                  height: 18,
                  fit: BoxFit.contain,
                ),
                SizedBox(
                  width: 5.w,
                ),
                Text(text,
                    style: TextStyle(
                        color: Color(0xFF5c5c5c),
                        fontSize: Dimens.font_sp14,
                        fontWeight: FontWeight.w400)),
              ],
            ),

            LoadAssetImage(
              "mine/icon_arrow_right",
              height: 18,
              width: 18,
            )

            // Text(
            //   ">",

            //   style: TextStyle(
            //     height: 1,
            //     color: Color(0xFF5c5c5c), fontSize: Dimens.font_sp18,fontWeight: FontWeight.w400),
            // )
          ],
        ));
  }
}

class FinanceButton extends StatelessWidget {
  final String iconImg;

  final GestureTapCallback? onPressed;

  final String text;

  final Color color;

  const FinanceButton(
      {required this.text,
      required this.iconImg,
      required this.onPressed,
      required this.color,
      super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 5, vertical: 2),
        decoration:
            BoxDecoration(borderRadius: BorderRadius.circular(8), color: color),
        child: Row(
          children: [
            LoadAssetImage(
              iconImg,
              width: 18,
              height: 18,
            ),
            SizedBox(
              width: 1.5.w,
            ),
            Text("$text",
                style: TextStyle(
                    height: 1.5,
                    fontSize: Dimens.font_sp12,
                    color: Color(0xFFFFFFFF))),
          ],
        ),
      ),
    );
  }
}

class OrderButton extends StatelessWidget {
  final String iconImg;

  final GestureTapCallback? onPressed;

  final String text;

  final List<Color> colors;

  const OrderButton(
      {required this.text,
      required this.iconImg,
      required this.onPressed,
      required this.colors,
      super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8), // 设置圆角大小

          gradient: LinearGradient(
            begin: Alignment(-1.0, -1.0),
            end: Alignment(1.0, 1.0),
            colors: colors,
          ),
        ),
        child: Column(
          children: [
            LoadAssetImage(
              "$iconImg",
              width: 24,
              height: 24,
            ),
            SizedBox(
              height: 10.w,
            ),
            Text('$text',
                style: TextStyle(
                  fontSize: Dimens.font_sp12,
                  color: Color(0xFFFFFFFF),
                  fontWeight: FontWeight.w400,
                ))
          ],
        ),
      ),
    );
  }
}
