import 'package:flutter/material.dart';
import 'package:sport_mobile/widgets/my_app_bar.dart';
import 'package:sport_mobile/widgets/state_layout.dart';

class NotFoundPage extends StatelessWidget {

  const NotFoundPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      appBar: MyAppBar(
        centerTitle: '页面不存在',
        backImgColor: Colors.white,


      ),
      body: StateLayout(
        hintText: '页面不存在',
        type: StateType.empty,
      ),
    );
  }
}
