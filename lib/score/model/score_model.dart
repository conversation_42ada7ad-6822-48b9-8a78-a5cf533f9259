import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/score/entity/collect_match_list.dart';
import 'package:sport_mobile/score/entity/score_list_entity.dart';
import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/net/ws_utils.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/util/other_utils.dart';
import 'package:sport_mobile/net/network_state.dart';

class ScoreModel extends ChangeNotifier {
  bool isLoad = true;
  NetworkStatus networkStatus = NetworkStatus.networkConnected;
  bool hasMoreData = false;
  List<Score> scoreList = [];

  List<String> collectList = [];

  int _matchType = MatchDataType.FT;
  int get matchType => _matchType;

  int _type = 0;

  int get type => _type;

  void setMatchType(int id) {
    _matchType = id;

    refreshData();
    notifyListeners();
  }

  void setType(int id) {
    _type = id;
  }

  Future refreshData() async {
    isLoad = true;

    try {
      List<Score> tempList = await getScoreList(type, matchType);
      isLoad = false;
      networkStatus = NetworkStatus.networkConnected;

      if (tempList.isEmpty) {
        hasMoreData = false;
        scoreList = tempList;
      } else {
        scoreList = tempList;
        getCollectMatchList();
      }
    } on TimeoutException catch (_) {
      isLoad = false;
      networkStatus = NetworkStatus.networkTimeout;
    } catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkFailed;
    }

    notifyListeners();
  }

  Future loadMore() async {
    if (isLoad) {
      return;
    }

    if (hasMoreData == false) {
      return;
    }

    isLoad = true;
    try {
      var tempList = await getScoreList(type, matchType);
      isLoad = false;
      networkStatus = NetworkStatus.networkConnected;

      if (tempList.isEmpty) {
        hasMoreData = false;
      } else {
        scoreList.addAll(tempList);
      }
    } on TimeoutException catch (_) {
      isLoad = false;
      networkStatus = NetworkStatus.networkTimeout;
    } catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkFailed;
    }

    notifyListeners();
  }

  Future<List<Score>> getScoreList(int type, int matchType) async {
    String? token = SpUtil.getString(Constant.token);

    var parameters = {
      'token': "$token",
      'version': Constant.version,
      'type': "$type",
      'match_type': "$matchType",
    };

    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest = BaseRequestEntity(Api.getMatchList, form);

    Map<String, dynamic> requestDataMap = baseRequest.toJson();

    var respStr = await WsUtils.instance.sendMessageAndWaitForResponse(
        jsonEncode(requestDataMap),
        showProgress: false);

    Map<String, dynamic> dataList = jsonDecode(respStr);

    ScoreListEntity scoreListEntity = ScoreListEntity.fromJson(dataList);

    return scoreListEntity.data!;
  }

  getCollectMatchList() async {
    String? token = SpUtil.getString(Constant.token);

    var parameters = {
      'token': "$token",
      'version': Constant.version,
      'match_type': "$matchType",
      'limit': "999",
    };

    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest =
        BaseRequestEntity(Api.getCollectMatchList, form);

    Map<String, dynamic> requestDataMap = baseRequest.toJson();

    var respStr = await WsUtils.instance.sendMessageAndWaitForResponse(
        jsonEncode(requestDataMap),
        showProgress: false);

    Map<String, dynamic> dataList = jsonDecode(respStr);

    CollectMatchListEntity collectMatchListEntity =
        CollectMatchListEntity.fromJson(dataList);

    if (collectMatchListEntity.status == '_0000') {
      collectList = collectMatchListEntity.data!;
    }

    // notifyListeners();
  }

  Future collectMatch(Score score) async {
    int actionFlag = 0;

    if (collectList.contains(score.boutIndex)) {
      actionFlag = 1;
    }

    String? token = SpUtil.getString(Constant.token);

    var parameters = {
      'token': "$token",
      'version': Constant.version,
      'bout_index': "${score.boutIndex}",
      'is_del': "$actionFlag",
      'match_type': "$matchType",
    };

    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest = BaseRequestEntity(Api.collectMatch, form);

    Map<String, dynamic> requestDataMap = baseRequest.toJson();

    var respStr = await WsUtils.instance
        .sendMessageAndWaitForResponse(jsonEncode(requestDataMap));

    Map<String, dynamic> dataList = jsonDecode(respStr);

    var baseRes = BaseEntity.fromJson(dataList);

    if (baseRes.status == '_0000') {
      EasyLoading.showToast('${baseRes.message}');
      await getCollectMatchList();
    }

    notifyListeners();
  }
}
