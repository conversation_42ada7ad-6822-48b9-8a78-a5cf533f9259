class ScoreListEntity {
  String? status;
  String? message;
  List<Score>? data;

  ScoreListEntity({this.status, this.message, this.data});

  ScoreListEntity.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <Score>[];
      json['data'].forEach((v) {
        data!.add(Score.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Score {
  int? id;
  String? boutIndex;
  int? homeYellow;
  int? awayYellow;
  int? homeRed;
  int? awayRed;
  int? homeScore;
  int? awayScore;
  int? homeHalfScore;
  int? awayHalfScore;
  String? homeRank;
  String? awayRank;
  int? homeCorner;
  int? awayCorner;
  int? status;
  String? time;
  String? date;
  String? turn;
  int? type;
  int? timestamp;
  String? matchName;
  int? jhMatchid;
  String? homeTeam;
  String? awayTeam;
  int? homeId;
  int? awayId;
  int? lotteryStatus;

  Score(
      {this.id,
      this.boutIndex,
      this.homeYellow,
      this.awayYellow,
      this.homeRed,
      this.awayRed,
      this.homeScore,
      this.awayScore,
      this.homeHalfScore,
      this.awayHalfScore,
      this.homeRank,
      this.awayRank,
      this.homeCorner,
      this.awayCorner,
      this.status,
      this.time,
      this.date,
      this.turn,
      this.type,
      this.timestamp,
      this.matchName,
      this.jhMatchid,
      this.homeTeam,
      this.awayTeam,
      this.homeId,
      this.awayId,
      this.lotteryStatus});

  Score.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    boutIndex = json['bout_index'];
    homeYellow = json['home_yellow'];
    awayYellow = json['away_yellow'];
    homeRed = json['home_red'];
    awayRed = json['away_red'];
    //

    if (json['home_score'] != null && json['home_score'] is String) {
      homeScore = int.parse(json['home_score']);
    } else {
      homeScore = json['home_score'];
    }

    //

    if (json['away_score'] != null && json['away_score'] is String) {
      awayScore = int.parse(json['away_score']);
    } else {
      awayScore = json['away_score'];
    }

    homeHalfScore = json['home_half_score'];
    awayHalfScore = json['away_half_score'];
    homeRank = json['home_rank'];
    awayRank = json['away_rank'];
    homeCorner = json['home_corner'];



    awayCorner = json['away_corner'];
    //

    if (json['status'] != null && json['status'] is String) {
      status = int.parse(json['status']);
    } else {
      status = json['status'];
    }

    time = json['time'];
    date = json['date'];
    turn = json['turn'];
    type = json['type'];
    timestamp = json['timestamp'];
    matchName = json['match_name'];
    jhMatchid = json['jh_matchid'];
    homeTeam = json['home_team'];
    awayTeam = json['away_team'];
    homeId = json['home_id'];
    awayId = json['away_id'];
    lotteryStatus = json['lottery_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['bout_index'] = this.boutIndex;
    data['home_yellow'] = this.homeYellow;
    data['away_yellow'] = this.awayYellow;
    data['home_red'] = this.homeRed;
    data['away_red'] = this.awayRed;
    data['home_score'] = this.homeScore;
    data['away_score'] = this.awayScore;
    data['home_half_score'] = this.homeHalfScore;
    data['away_half_score'] = this.awayHalfScore;
    data['home_rank'] = this.homeRank;
    data['away_rank'] = this.awayRank;
    data['home_corner'] = this.homeCorner;
    data['away_corner'] = this.awayCorner;
    data['status'] = this.status;
    data['time'] = this.time;
    data['date'] = this.date;
    data['turn'] = this.turn;
    data['type'] = this.type;
    data['timestamp'] = this.timestamp;
    data['match_name'] = this.matchName;
    data['jh_matchid'] = this.jhMatchid;
    data['home_team'] = this.homeTeam;
    data['away_team'] = this.awayTeam;
    data['home_id'] = this.homeId;
    data['away_id'] = this.awayId;
    data['lottery_status'] = this.lotteryStatus;
    return data;
  }
}
