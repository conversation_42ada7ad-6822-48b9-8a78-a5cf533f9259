import 'dart:convert';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/login/entity/reset_password_entity.dart';
import 'package:sport_mobile/login/entity/user_entity.dart';
import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/net/ws_utils.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/util/log_utils.dart';
import 'package:sport_mobile/util/other_utils.dart';

class UserAction extends ChangeNotifier {
  logout() {
    LogUtils.logger.i("用户登出");
    SpUtil.remove(Constant.token);
    SpUtil.remove(Constant.loginName);
    SpUtil.remove(Constant.nickName);
  }

  Future<UserEntity> register(regReq) async {
    var regResp = await WsUtils.instance
        .sendMessageAndWaitForResponse(json.encode(regReq));

    Map<String, dynamic> dataMap = jsonDecode(regResp);

    UserEntity resp = UserEntity.fromJson(dataMap);

    if (resp.status == '_0000') {
      SpUtil.putString(Constant.token, resp.token!);
      SpUtil.putString(Constant.loginName, resp.petName!);
      SpUtil.putString(Constant.nickName, resp.nickName!);
      resp.message = "登陆成功";
    }
    return resp;
  }

  static Future<BaseEntity> sendCaptcha(phone, action) async {

    Map<String, String> parameters = {
      'version': Constant.version,
      //   'token': "$token",
      'channel': Utils.getDeviceName(),
      'action': '$action',
      'name': '$phone',
    };

    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest =
        BaseRequestEntity(Api.sendValidateCode, form);

    Map<String, dynamic> requestDataMap = baseRequest.toJson();

    var respStr = await WsUtils.instance
        .sendMessageAndWaitForResponse(jsonEncode(requestDataMap));

    var dataList = jsonDecode(respStr);

    var baseRes = BaseEntity.fromJson(dataList);

    if (baseRes.status == '_0000') {
      baseRes.message = "验证码已发送";
    }

    return baseRes;
  }

  static Future<BaseEntity> resetPassword(
      String phone, String password, String captcha) async {
    String form =
        "version=${Constant.version}&name=$phone&new_password=$password&code=$captcha&channel=${Utils.getDeviceName()}";
    var resetPasswordEntity = ResetPasswordEntity(form);
    Map<String, dynamic> jsonForm = resetPasswordEntity.toJson();

    var regResp = await WsUtils.instance
        .sendMessageAndWaitForResponse(json.encode(jsonForm));

    Map<String, dynamic> dataMap = jsonDecode(regResp);

    BaseEntity resp = BaseEntity.fromJson(dataMap);

    if (resp.status == '_0000') {
      resp.message = "密码修改成功";
    }
    return resp;
  }
}
