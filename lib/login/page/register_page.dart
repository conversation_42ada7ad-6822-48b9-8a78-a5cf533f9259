import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/config/secrets.dart';
import 'package:sport_mobile/login/entity/user_entity.dart';
import 'package:sport_mobile/login/model/user_action.dart';
import 'package:sport_mobile/login/model/user_model.dart';
import 'package:sport_mobile/login/widgets/contact_owner.dart';
import 'package:sport_mobile/login/widgets/my_text_field.dart';
import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/routers/routers.dart';
import 'package:sport_mobile/util/change_notifier_manage.dart';
import 'package:sport_mobile/util/encrypt_utils.dart';
import 'package:sport_mobile/util/other_utils.dart';
import 'package:sport_mobile/util/toast_utils.dart';
import 'package:sport_mobile/widgets/my_app_bar.dart';
import 'package:sport_mobile/widgets/my_button.dart';
import 'package:sport_mobile/widgets/my_scroll_view.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  _RegisterPageState createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage>
    with ChangeNotifierMixin<RegisterPage> {
  //定义一个controller
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _vCodeController = TextEditingController();
  final TextEditingController _nickNameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _inviteCodeController = TextEditingController();
  final FocusNode _nodeText1 = FocusNode();
  final FocusNode _nodeText2 = FocusNode();
  final FocusNode _nodeText3 = FocusNode();
  final FocusNode _nodeText4 = FocusNode();
  final FocusNode _nodeText5 = FocusNode();
  bool _clickable = false;

  @override
  Map<ChangeNotifier, List<VoidCallback>?>? changeNotifier() {
    final List<VoidCallback> callbacks = <VoidCallback>[_verify];
    return <ChangeNotifier, List<VoidCallback>?>{
      _phoneController: callbacks,
      _vCodeController: callbacks,
      _nickNameController: callbacks,
      _passwordController: callbacks,
      _inviteCodeController: callbacks,
    };
  }

  void _verify() {
    final String name = _phoneController.text;
    final String vCode = _vCodeController.text;
    final String password = _passwordController.text;
    final String inviteCode = _inviteCodeController.text;
    final String nickName = _nickNameController.text;
    bool clickable = true;
    if (name.isEmpty || name.length < 11) {
      clickable = false;
    }
    if (vCode.isEmpty) {
      clickable = false;
    }

    if (inviteCode.isEmpty) {
      clickable = false;
    }

    if (nickName.isEmpty) {
      clickable = false;
    }

    if (password.isEmpty || password.length < 6 || password.length > 18) {
      clickable = false;
    }
    if (clickable != _clickable) {
      setState(() {
        _clickable = clickable;
      });
    }
  }

  void _register() {
    UserAction userAction = UserAction();

    final String name = EncryptionUtil.aesEncrypt(_phoneController.text,loginKey,loginIV);
    final String password = EncryptionUtil.aesEncrypt(_passwordController.text,loginKey,loginIV);

    final String code = _vCodeController.text;
    final String nickName = _nickNameController.text;
    final String inviteCode = _inviteCodeController.text;

    var parameters = {
      'name': name,
      'pet_name': nickName,
      'code': code,
      'source_type': "1",
      'recommend': inviteCode,
      'password': password,
      'source': Constant.generalsource,
      'version': Constant.version,
      'other_from': "klc",
      "other_version": "game2",
      "channel": Utils.getDeviceName()
    };

    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest = BaseRequestEntity(Api.register, form);
    Map<String, dynamic> requestDataMap = baseRequest.toJson();

    userAction.register(requestDataMap).then((UserEntity res) {
      if (res.status == '_0000') {
        // 获取UserProvider并设置用户信息
        UserModel userModel = Provider.of<UserModel>(context, listen: false);
        userModel.setUser(res);
        NavigatorUtils.push(context, Routes.home);
      } else {
        Toast.show(res.message);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MyAppBar(
        centerTitle: "注册",
        backImgColor: Colors.white,
      ),
      body: MyScrollView(
        keyboardConfig: Utils.getKeyboardActionsConfig(
            context, <FocusNode>[_nodeText1, _nodeText2, _nodeText3]),
        crossAxisAlignment: CrossAxisAlignment.center,
        padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 20.0),
        children: _buildBody(),
      ),
    );
  }

  List<Widget> _buildBody() {
    return <Widget>[
      Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFe3e3e3),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            const Expanded(
                flex: 2,
                child: Text(
                  "手机号",
                  style: TextStyles.textSize14,
                )),
            Expanded(
                flex: 8,
                child: MyTextField(
                  key: const Key('phone'),
                  focusNode: _nodeText1,
                  controller: _phoneController,
                  maxLength: 11,
                  keyboardType: TextInputType.phone,
                  hintText: "请输入您的手机号",
                  getVCode: () async {
                    final String phone = _phoneController.text;
                    if (_phoneController.text.length == 11) {
                      BaseEntity response =
                          await UserAction.sendCaptcha(phone, '1');
                      if (response.status != "_0000") {
                        Toast.show(response.message);
                        return false;
                      }
                      Toast.show(response.message);
                      return true;
                    } else {
                      Toast.show("请输入有效的手机号");
                      return false;
                    }
                  },
                )),
          ],
        ),
      ),
      Gaps.vGap8,
      Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFe3e3e3),
              width: 0.5,
            ),
          ),
        ),
        child: Row(children: [
          const Expanded(
              flex: 2,
              child: Text(
                "验证码",
                style: TextStyles.textSize14,
              )),
          Expanded(
              flex: 8,
              child: MyTextField(
                key: const Key('vcode'),
                focusNode: _nodeText2,
                controller: _vCodeController,
                keyboardType: TextInputType.number,
                maxLength: 6,
                hintText: "请输入验证码",
              )),
        ]),
      ),
      Gaps.vGap8,
      Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Color(0xFFe3e3e3),
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            children: [
              const Expanded(
                  flex: 2,
                  child: Text(
                    "昵称",
                    style: TextStyles.textSize14,
                  )),
              Expanded(
                flex: 8,
                child: MyTextField(
                  key: const Key('nickname'),
                  focusNode: _nodeText3,
                  controller: _nickNameController,
                  keyboardType: TextInputType.text,
                  maxLength: 20,
                  hintText: "请设置昵称",
                             enableChineseCharset: true,

                ),
              )
            ],
          )),
      Gaps.vGap8,
      Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Color(0xFFe3e3e3),
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            children: [
              const Expanded(
                  flex: 2,
                  child: Text(
                    "密码",
                    style: TextStyles.textSize14,
                  )),
              Expanded(
                flex: 8,
                child: MyTextField(
                  key: const Key('password'),
                  keyName: 'password',
                  focusNode: _nodeText4,
                  isInputPwd: true,
                  controller: _passwordController,
                  maxLength: 18,
                  keyboardType: TextInputType.visiblePassword,
                  hintText: "6-18位数字与字母",
                ),
              )
            ],
          )),
      Gaps.vGap8,
      Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Color(0xFFe3e3e3),
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            children: [
              const Expanded(
                  flex: 2,
                  child: Text(
                    "邀请码",
                    style: TextStyles.textSize14,
                  )),
              Expanded(
                flex: 8,
                child: MyTextField(
                  key: const Key('invitecode'),
                  focusNode: _nodeText5,
                  controller: _inviteCodeController,
                  keyboardType: TextInputType.text,
                  maxLength: 6,
                  hintText: "请输入邀请码",
                ),
              )
            ],
          )),
      Gaps.vGap24,
      MyButton(
        key: const Key('register'),
        onPressed: _clickable ? _register : null,
        gradient: true,
        radius: 30,
        text: "确认",
      ),
      Gaps.vGap16,
      const ContactOwner()
    ];
  }
}
