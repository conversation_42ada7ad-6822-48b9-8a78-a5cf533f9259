// class UserBankListEntity {
//   String? status;
//   String? message;
//   List<BankCard>? bankCardList;
//   List<Ailarr>? ailarr;
//   PayData? payData;

//   UserBankListEntity(
//       {this.status, this.message, this.bankCardList, this.ailarr, this.payData});

//   UserBankListEntity.fromJson(Map<String, dynamic> json) {
//     status = json['status'];
//     message = json['message'];
//     if (json['data'] != null) {
//       bankCardList = <BankCard>[];
//       json['data'].forEach((v) {
//         bankCardList!.add(new BankCard.fromJson(v));
//       });
//     }
//     if (json['ailarr'] != null) {
//       ailarr = <Ailarr>[];
//       json['ailarr'].forEach((v) {
//         ailarr!.add(new Ailarr.fromJson(v));
//       });
//     }
//     payData = json['pay_data'] != null
//         ? new PayData.fromJson(json['pay_data'])
//         : null;
//   }


// }

// class BankCard {
//   int? id;
//   int? uid;
//   String? color;
//   String? url;
//   String? bank;
//   String? bankNo;
//   String? bankWei;
//   int? bankType;
//   int? channel;

//   BankCard(
//       {this.id,
//       this.uid,
//       this.color,
//       this.url,
//       this.bank,
//       this.bankNo,
//       this.bankWei,
//       this.bankType,
//       this.channel});

//   BankCard.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     uid = json['uid'];
//     color = json['color'];
//     url = json['url'];
//     bank = json['bank'];
//     bankNo = json['bank_no'];
//     bankWei = json['bank_wei'];
//     bankType = json['bank_type'];
//     channel = json['channel'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['id'] = this.id;
//     data['uid'] = this.uid;
//     data['color'] = this.color;
//     data['url'] = this.url;
//     data['bank'] = this.bank;
//     data['bank_no'] = this.bankNo;
//     data['bank_wei'] = this.bankWei;
//     data['bank_type'] = this.bankType;
//     data['channel'] = this.channel;
//     return data;
//   }
// }

class Ailarr {
  int? id;
  int? uid;
  String? color;
  String? bank;
  String? bankNo;
  String? bankWei;
  int? bankType;
  int? isBind;
  int? isAuth;
  int? channel;

  Ailarr(
      {this.id,
      this.uid,
      this.color,
      this.bank,
      this.bankNo,
      this.bankWei,
      this.bankType,
      this.isBind,
      this.isAuth,
      this.channel});

  Ailarr.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    uid = json['uid'];
    color = json['color'];
    bank = json['bank'];
    bankNo = json['bank_no'];
    bankWei = json['bank_wei'];
    bankType = json['bank_type'];
    isBind = json['is_bind'];
    isAuth = json['is_auth'];
    channel = json['channel'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['uid'] = this.uid;
    data['color'] = this.color;
    data['bank'] = this.bank;
    data['bank_no'] = this.bankNo;
    data['bank_wei'] = this.bankWei;
    data['bank_type'] = this.bankType;
    data['is_bind'] = this.isBind;
    data['is_auth'] = this.isAuth;
    data['channel'] = this.channel;
    return data;
  }
}

class PayData {
  int? alipayType;
  int? happyType;
  int? isHappyChat;

  PayData({this.alipayType, this.happyType, this.isHappyChat});

  PayData.fromJson(Map<String, dynamic> json) {
    alipayType = json['alipay_type'];
    happyType = json['happy_type'];
    isHappyChat = json['is_happy_chat'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['alipay_type'] = this.alipayType;
    data['happy_type'] = this.happyType;
    data['is_happy_chat'] = this.isHappyChat;
    return data;
  }
}
