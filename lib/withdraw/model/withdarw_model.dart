import 'dart:async';

import 'package:flutter/material.dart';

import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/bank_card/entity/user_bank_list_entity.dart';
import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/net/network_state.dart';
import 'package:sport_mobile/net/ws_utils.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/util/other_utils.dart';

class WithdrawModel extends ChangeNotifier {
  UserBankListEntity? userBankList;

  bool isLoad = true;
  NetworkStatus networkStatus = NetworkStatus.networkConnected;

  BankCard currentUserBankCard = BankCard();
  Ailarr currentAlipayCard = Ailarr();

  getUserBankList() async {
    String? token = SpUtil.getString(Constant.token);

    Map<String, String> parameters = {
      'version': Constant.version,
      'token': "$token",
      'channel': Utils.getDeviceName()
    };

    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest =
        BaseRequestEntity(Api.getUserbanklist, form);

    Map<String, dynamic> requestDataMap = baseRequest.toJson();

    try {

      isLoad = true;

      var respStr = await WsUtils.instance
          .sendMessageAndWaitForResponse(jsonEncode(requestDataMap),showProgress: false);

      networkStatus = NetworkStatus.networkConnected;
isLoad = false;
      var dataList = jsonDecode(respStr);

      userBankList = UserBankListEntity.fromJson(dataList);

      if (userBankList?.status == '_0000') {
        if (userBankList!.bankCardList!.isNotEmpty) {
          currentUserBankCard = userBankList!.bankCardList!.first;
        } else {
          currentUserBankCard = BankCard();
        }

        if (userBankList!.ailarr!.isNotEmpty) {
          currentAlipayCard = userBankList!.ailarr!.first;
        } else {
          currentAlipayCard = Ailarr();
        }
      }
    } on TimeoutException catch (_) {
      isLoad = false;
      networkStatus = NetworkStatus.networkTimeout;
    } catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkFailed;
    }

    notifyListeners();
  }

  changeSelectBank(dynamic bank) {
    if (bank is Ailarr) {
      currentAlipayCard = bank;
    } else {
      currentUserBankCard = bank;
    }

    notifyListeners();
  }

  Future<BaseEntity> withdraw(
      double amount, int withdrawType, int bankID) async {
    String? token = SpUtil.getString(Constant.token);

    Map<String, String> parameters = {
      'banktype': "$withdrawType",
      'money': '$amount',
      'type': '0',
      'withtype': '0',
      'txgz': 'http://127.0.0.1:8080/help/txts.html',
      'bankId': "${bankID}",
      'version': Constant.version,
      'token': "$token",
      'channel': Utils.getDeviceName()
    };

    BaseEntity baseRes;

    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest = BaseRequestEntity(Api.withdraw, form);

    Map<String, dynamic> requestDataMap = baseRequest.toJson();

    try {
      var respStr = await WsUtils.instance
          .sendMessageAndWaitForResponse(jsonEncode(requestDataMap));

      var dataList = jsonDecode(respStr);

      baseRes = BaseEntity.fromJson(dataList);
      return baseRes;
    } catch (e) {
      baseRes = BaseEntity('-1002', '${e.toString()}');
      return baseRes;
    }
  }
}
