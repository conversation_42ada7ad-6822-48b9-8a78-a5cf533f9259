import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/coupon/entity/coupon_entity.dart';
import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/net/ws_utils.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/util/other_utils.dart';
import 'package:sport_mobile/net/network_state.dart';

class LiveModel extends ChangeNotifier {
  bool isLoad = true;
  NetworkStatus networkStatus = NetworkStatus.networkConnected;
}

class ChatEntity {
  String? liveRoomUrl;
  String? data;
  String? sign;

  ChatEntity({this.liveRoomUrl, this.data, this.sign});

  ChatEntity.fromJson(Map<String, dynamic> json) {
    liveRoomUrl = json['liveRoomUrl'];
    data = json['data'];
    sign = json['sign'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['liveRoomUrl'] = this.liveRoomUrl;
    data['data'] = this.data;
    data['sign'] = this.sign;
    return data;
  }
}
