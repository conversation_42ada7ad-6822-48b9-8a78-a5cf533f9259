import 'package:fluro/fluro.dart';
import 'package:sport_mobile/transaction/page/transaction_page.dart';
import 'package:sport_mobile/routers/i_router.dart';



class TransactionRouter implements IRouterProvider{

  static String transactionPage = '/transaction';
  
  @override
  void initRouter(FluroRouter router) {
    router.define(transactionPage, handler: Handler(handlerFunc: (_, __) => const TransactionPage()));
  }
  
}
