import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/default/widgets/my_custom_button.dart';
import 'package:sport_mobile/game/routers/game_router.dart';
import 'package:sport_mobile/order/entity/order_detail.dart';
import 'package:sport_mobile/order/model/order_datail_model.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:flutter/cupertino.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/util/image_utils.dart';
import 'package:sport_mobile/util/other_utils.dart';
import 'package:sport_mobile/util/toast_utils.dart';
import 'package:sport_mobile/widgets/custom_button.dart';
import 'package:sport_mobile/widgets/load_image.dart';

class ProjectDetailPage extends StatefulWidget {
  final String id;

  ProjectDetailPage({required this.id, super.key});

  @override
  _ProjectDetailPageState createState() => _ProjectDetailPageState();
}

class _ProjectDetailPageState extends State<ProjectDetailPage> {
  late String orderID;

  bool show = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      orderID = widget.id;

      Provider.of<OrderDetailModel>(context, listen: false)
          .getOrderDetail(orderID);
    });
  }

  Widget getbetMethodName(OrderDetailModel model) {
    if (!(model.commonData!.type == '七星彩' ||
        model.commonData!.type == '大乐透' ||
        model.commonData!.type == '排列三' ||
        model.commonData!.type == '排列五' ||
        model.commonData!.type == '福彩3d' ||
        model.commonData!.type == '足球任选9' ||
        model.commonData!.type == '足球14场' ||
        model.commonData!.type == '四场进球' ||
        model.commonData!.type == '竞猜冠军' ||
        model.commonData!.type == '竞猜冠亚军' ||
        model.commonData!.type == '6场半全场' ||
        model.commonData!.type == '双色球')) {
      return const Text(
        '过关方式',
        style: TextStyles.textSize12,
      );
    } else {
      return Container();
    }
  }

  List<Widget> getTicketMatchName(OrderDetailModel model, DrawInfo item) {
    List<Widget> matchList = [];

    if (model.commonData!.type == '排列三') {
      var temp = Text(
        item.boutIndex!,
      );

      matchList.add(temp);
    } else if (model.commonData!.type == '排列五') {
      Widget temp = Text(
        item.boutIndex!,
      );
      matchList.add(temp);
    } else if (model.commonData!.type == '福彩3d') {
      Widget temp = Text(
        item.boutIndex!,
      );
      matchList.add(temp);
    } else if (model.commonData!.type == '足球任选9') {
      Widget temp = Text(
        item.boutIndex!,
      );
      matchList.add(temp);
    } else if (model.commonData!.type == '四场进球') {
      Widget temp = Text(
        item.boutIndex!,
      );
      matchList.add(temp);
    } else if (model.commonData!.type == '足球14场') {
      Widget temp = Text(
        item.boutIndex!,
      );
      matchList.add(temp);
    } else if (model.commonData!.type == '6场半全场') {
      Widget temp = Text(
        item.boutIndex!,
      );
      matchList.add(temp);
    } else if (model.commonData!.type == '竞猜冠军') {
      Widget temp = Text(model.matchList!.first.termNo!);

      matchList.add(temp);
    } else if (model.commonData!.type == '竞猜冠亚军') {
      Widget temp = Text(model.matchList!.first.termNo!);

      matchList.add(temp);
    } else if (model.commonData!.type == '6场半全场') {
      Widget temp = Text(
        item.boutIndex!,
      );
      matchList.add(temp);
    } else if (model.commonData!.type == '双色球') {
      Widget temp = Text(
        item.boutIndex!,
      );
      matchList.add(temp);
    } else if (model.commonData!.type == '大乐透') {
      Widget temp = Text(
        item.boutIndex!,
      );
      matchList.add(temp);
    } else if (model.commonData!.type == '七星彩') {
      Widget temp = Text(
        item.boutIndex!,
      );
      matchList.add(temp);
    } else {
      item.chuanList!.forEach((_item) {
        matchList.add(
          Row(
            children: [
              Text(
                '${_item["home"]}',
              ),
              const Text(
                'VS',
                style: TextStyles.textGray12,
              ),
              Text(
                '${_item["away"]}',
              ),
            ],
          ),
        );

        matchList.add(Row(
          children: [
            Text(
              "${_item["sp"]}",
              style: TextStyles.textGray12,
            )
          ],
        ));
      });
    }

    return matchList;
  }

  Widget getBetTicketType(model, item) {
    if (!(model.commonData!.type == '七星彩' ||
        model.commonData!.type == '大乐透' ||
        model.commonData!.type == '排列三' ||
        model.commonData!.type == '排列五' ||
        model.commonData!.type == '福彩3d' ||
        model.commonData!.type == '足球任选9' ||
        model.commonData!.type == '足球14场' ||
        model.commonData!.type == '四场进球' ||
        model.commonData!.type == '竞猜冠军' ||
        model.commonData!.type == '竞猜冠亚军' ||
        model.commonData!.type == '6场半全场' ||
        model.commonData!.type == '双色球')) {
      return Container(
        width: 80,
        height: 80,
        alignment: Alignment.center,
        child: Text(
          "${item.type}",
          style: TextStyles.textSize12,
        ),
      );
    } else {
      return Container();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<OrderDetailModel>(builder: (context, model, child) {
      if (model.isLoading == true) {
        return const Center(child: CupertinoActivityIndicator(radius: 16.0));
      }

      String gameType = model.orderDetail.commonInfo!.type!;

      return Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      margin: EdgeInsets.only(right: 5),
                      width: 30,
                      child: LoadAssetImage(
                          model.getMatchImageByChineseName(gameType)),
                    ),
                    model.commonData!.type == "福彩3d"
                        ? Text("福彩3d")
                        : Text(
                            "$gameType  ",
                            style: TextStyles.textBold16,
                          ),
                    Text(
                      "${model.chuanTip}  ",
                      style: TextStyles.textBold16,
                    ),
                  ],
                ),
                Row(
                  children: [
                    const Text(
                      "状态 ",
                      style: TextStyles.textGray12,
                    ),
                    Text(
                      "${model.commonData!.status}",
                      style: TextStyles.textSize12,
                    ),
                  ],
                )
              ],
            ),
          ),
          const Column(
            children: [
              Divider(
                height: 0.2,
                color: Color(0xFFdedede),
                thickness: 1.0,
                indent: 16.0,
                endIndent: 16.0,
              ),
            ],
          ),
          Expanded(
              child: Container(
                  color: Color(0xFFf2f2f2),
                  child: ListView(
                    children: [
                      Container(
                        color: Colors.white,
                        margin: EdgeInsets.only(bottom: 10),
                        padding:
                            EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(
                                  //  width: 160,
                                  child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            Container(
                                              width: 100,
                                              child: const Text(
                                                "投注金额：",
                                                style: TextStyle(
                                                    fontSize: Dimens.font_sp14,
                                                    height: 2,
                                                    color: Color(0xFF9f9f9f)),
                                              ),
                                            ),
                                            Row(
                                              children: [
                                                Text(
                                                  "¥${model.commonData!.amount}",
                                                  style: const TextStyle(
                                                      fontSize:
                                                          Dimens.font_sp14,
                                                      height: 2,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Color(0xFFfd4627)),
                                                ),
                                                Text(
                                                  "(${model.commonData!.multiple}倍)",
                                                  style: const TextStyle(
                                                      fontSize:
                                                          Dimens.font_sp14,
                                                      height: 2,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Color(0xFF9f9f9f)),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Container(
                                              width: 100,
                                              child: const Text(
                                                "余额支付：",
                                                style: TextStyle(
                                                    fontSize: Dimens.font_sp14,
                                                    height: 2.0,
                                                    color: Color(0xFF9f9f9f)),
                                              ),
                                            ),
                                            Text(
                                              "¥${Utils.formatPrice(model.commonData!.balanceMoney!)}",
                                              style: TextStyle(
                                                fontSize: Dimens.font_sp14,
                                                height: 2,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Container(
                                              width: 100,
                                              child: const Text(
                                                "彩金支付：",
                                                style: TextStyle(
                                                    fontSize: Dimens.font_sp14,
                                                    height: 2,
                                                    color: Color(0xFF9f9f9f)),
                                              ),
                                            ),
                                            Text(
                                              "¥${Utils.formatPrice(model.commonData!.mosaicMoney!)}",
                                              //   "¥${model.commonData!.mosaicMoney}",
                                              style: const TextStyle(
                                                fontSize: Dimens.font_sp14,
                                                height: 2,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Container(
                                              width: 100,
                                              child: const Text(
                                                "优惠券支付：",
                                                style: TextStyle(
                                                    fontSize: Dimens.font_sp14,
                                                    height: 2,
                                                    color: Color(0xFF9f9f9f)),
                                              ),
                                            ),
                                            Text(
                                              "¥${Utils.formatPrice(model.commonData!.couponMoney!)}",
                                              style: const TextStyle(
                                                fontSize: Dimens.font_sp14,
                                                height: 2,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Container(
                                              width: 100,
                                              child: const Text(
                                                "中奖金额：",
                                                style: TextStyle(
                                                    fontSize: Dimens.font_sp14,
                                                    height: 2,
                                                    color: Color(0xFF9f9f9f)),
                                              ),
                                            ),
                                            model.commonData!.result == "未开奖"
                                                ? const Text(
                                                    "暂无",
                                                    style: TextStyle(
                                                      fontSize:
                                                          Dimens.font_sp14,
                                                      height: 2,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  )
                                                : Text(
                                                    "¥${Utils.formatPrice(model.commonData!.winMoney!)}",
                                                    style: const TextStyle(
                                                      fontSize:
                                                          Dimens.font_sp14,
                                                      height: 2,
                                                      color: Colors.red,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                          ],
                                        ),
                                        model.commonData!.result != "未开奖" &&
                                                model.commonData!
                                                        .mosaicWinMoney !=
                                                    "0.00"
                                            ? Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Container(
                                                    width: 100,
                                                    child: const Text(
                                                      "活动加奖：",
                                                      style: TextStyle(
                                                          fontSize:
                                                              Dimens.font_sp14,
                                                          height: 2,
                                                          color: Color(
                                                              0xFF9f9f9f)),
                                                    ),
                                                  ),
                                                  Text(
                                                    "¥${Utils.formatPrice(model.commonData!.mosaicWinMoney!)}",
                                                    style: const TextStyle(
                                                      fontSize:
                                                          Dimens.font_sp14,
                                                      height: 2,
                                                      color: Color(0xFFfd4627),
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ],
                                              )
                                            : Container(),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Container(
                                              width: 100,
                                              child: const Text(
                                                "奖金优化方案：",
                                                style: TextStyle(
                                                    fontSize: Dimens.font_sp14,
                                                    height: 2,
                                                    color: Color(0xFF9f9f9f)),
                                              ),
                                            ),
                                            Text(
                                              "${model.commonData!.jjyh}",
                                              style: const TextStyle(
                                                fontSize: Dimens.font_sp14,
                                                height: 2,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),

                                         Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Container(
                                              width: 100,
                                              child: const Text(
                                                "备注：",
                                                style: TextStyle(
                                                    fontSize: Dimens.font_sp14,
                                                    height: 2,
                                                    color: Colors.red),
                                                  
                                              ),
                                            ),
                                            Text(
                                              "${model.commonData!.remark}",
                                              style: const TextStyle(
                                                fontSize: Dimens.font_sp14,
                                                height: 2,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.red
                                              ),
                                            ),
                                          ],
                                        ),
                                      ]),
                                ),
                                Container(
                                    alignment: Alignment.centerRight,
                                    width: 60,
                                    child: model.commonData?.imgPath! != ''
                                        ? LoadAssetImage(
                                            model.commonData!.imgPath!,
                                          )
                                        : Container()),
                              ],
                            ),
                            Container(
                              margin: EdgeInsets.symmetric(vertical: 15),
                              width: double.infinity,
                              height: 1,
                              color: Color(0xFFdedede),
                            ),
                            Container(
                              child: Column(children: [
                                Container(
                                  padding: EdgeInsets.only(bottom: 10),
                                  alignment: Alignment.centerLeft,
                                  child: const Text(
                                    "订单详情",
                                    style: TextStyles.textBold16,
                                  ),
                                )
                              ]),
                            ),
                            Container(
                              padding: EdgeInsets.only(left: 10),
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      const Text(
                                        "投注时间：",
                                        style: TextStyles.textDarkGray12,
                                      ),
                                      Text(
                                        "${model.commonData!.buyTime}",
                                        style: const TextStyle(
                                            fontSize: Dimens.font_sp12),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Row(
                                        children: [
                                          const Text("订单编号：",
                                              style: TextStyles.textDarkGray12),
                                          Text("${model.commonData!.orderNo}",
                                              style: const TextStyle(
                                                  fontSize: Dimens.font_sp12)),
                                        ],
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          Clipboard.setData(ClipboardData(
                                              text:
                                                  '${model.commonData!.orderNo}'));
                                          Toast.show("复制成功");
                                        },
                                        child: Container(
                                          //   height: 50,
                                          alignment: Alignment.center,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 8),
                                          decoration: BoxDecoration(
                                            color: Color(0xFFf6f6f6),
                                            borderRadius:
                                                BorderRadius.circular(5.0),
                                            border: Border.all(
                                              color: Color(0xFFd0d0d0),
                                              width: 1.0,
                                            ),
                                          ),

                                          child: const Text(
                                            "复制编号",
                                            textAlign: TextAlign.center,
                                            style: TextStyles.textDarkGray12,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),

                      if (model.commonData!.type == '竞彩足球' ||
                          model.commonData!.type == '竞彩篮球' ||
                          model.commonData!.type == '北京单场' ||
                          model.commonData!.type == '欧亚足球' ||
                          model.commonData!.type == '排列三' ||
                          model.commonData!.type == '排列五' ||
                          model.commonData!.type == '福彩3d' ||
                          model.commonData!.type == '双色球' ||
                          model.commonData!.type == '大乐透' ||
                          model.commonData!.type == '七星彩' ||
                          model.commonData!.type == '刮刮乐')
                        FirstOrderContentLayout(model, widget.id),

                      //	<!-- 足球14场/足球任选9/四场进球/6场半全场/竞猜冠军/竞猜冠亚军 -->
                      if (model.commonData!.type == '足球任选9' ||
                          model.commonData!.type == '足球14场' ||
                          model.commonData!.type == '四场进球' ||
                          model.commonData!.type == '6场半全场' ||
                          model.commonData!.type == '竞猜冠军' ||
                          model.commonData!.type == '竞猜冠亚军')
                        SecondOrderContentLayout(model),

                      if (model.commonData!.isPic != null &&
                          model.commonData!.isPic == 1)
                        Container(
                          margin: EdgeInsets.only(top: 10),
                          child: Column(
                            children: [
                              Container(
                                color: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 10),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          child: const Text(
                                            "实体店出票",
                                            style: TextStyles.textBold16,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              if (show == true &&
                                  model.commonData!.cpPic != null &&
                                  model.commonData!.cpPic != "")
                                Container(
                                  alignment: Alignment.center,
                                  height: 400,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 30),

                                  width: double.infinity,
                                  // padding: const EdgeInsets.symmetric(
                                  //     horizontal: 30),
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: NetworkImage(
                                        model.commonData!.cpPic!,
                                      ), // 设置图片路径
                                      fit: BoxFit.cover, // 设置图片的适应方式
                                    ),
                                  ),
                                ),
                              if ((show == false ||
                                  model.commonData!.cpPic == null ||
                                  (model.commonData!.cpPic != null &&
                                      model.commonData!.cpPic!.isEmpty)))
                                Container(
                                  alignment: Alignment.center,
                                  height: 400,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 30),
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: ImageUtils.getAssetImage(
                                          'order/Lottery_pic'), // 设置图片路径
                                      fit: BoxFit.cover, // 设置图片的适应方式
                                    ),
                                  ),
                                  child: Container(
                                      alignment: Alignment.center,
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            " 告知 ",
                                            style: TextStyles.textBold14,
                                          ),
                                          Text(
                                            " 请务必仔细核对彩票，如有错误请在出票截止前联系店家申请，否则开奖后因错误票引起的纠纷由您自行承担。 ",
                                            style: TextStyles.textSize12,
                                          ),
                                          Gaps.vGap24,
                                          Container(
                                            width: 100,
                                            child: AcknowledgeButton(
                                              '我知道了',
                                              onPressed: () {
                                                if (model.commonData!.cpPic !=
                                                    null) {
                                                  show = true;
                                                  setState(() {});
                                                } else {
                                                  EasyLoading.showToast(
                                                      "请您耐心等待，您的彩票正在接单或出票中");
                                                }
                                              },
                                              color: Colors.transparent,
                                              textColor: Color(0xFFef8b09),
                                              // fontSize: Dimens.font_sp12,
                                            ),
                                          ),
                                        ],
                                      )),
                                )
                            ],
                          ),
                        ),
                      if (model.drawList?.isNotEmpty != null &&
                          model.drawList!.length > 0)
                        Container(
                          margin: EdgeInsets.only(top: 10),
                          child: Column(
                            children: [
                              Container(
                                color: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 10),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          child: const Text(
                                            "出票明细",
                                            style: TextStyles.textBold16,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                width: double.infinity,
                                color: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 5),
                                child: Column(
                                  children: [
                                    Column(
                                      children: [
                                        Container(
                                          padding:
                                              EdgeInsets.symmetric(vertical: 4),
                                          color: Color(0xFFf6f6f6),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                  flex: 4,
                                                  child: Text(
                                                    model.commonData!.type == '七星彩' ||
                                                            model.commonData!.type ==
                                                                '大乐透' ||
                                                            model.commonData!.type ==
                                                                '排列三' ||
                                                            model.commonData!
                                                                    .type ==
                                                                '排列五' ||
                                                            model.commonData!
                                                                    .type ==
                                                                '福彩3d' ||
                                                            model.commonData!
                                                                    .type ==
                                                                '足球任选9' ||
                                                            model.commonData!
                                                                    .type ==
                                                                '足球14场' ||
                                                            model.commonData!
                                                                    .type ==
                                                                '四场进球' ||
                                                            model.commonData!
                                                                    .type ==
                                                                '竞猜冠军' ||
                                                            model.commonData!
                                                                    .type ==
                                                                '竞猜冠亚军' ||
                                                            model.commonData!
                                                                    .type ==
                                                                '6场半全场' ||
                                                            model.commonData!
                                                                    .type ==
                                                                '双色球'
                                                        ? '期号'
                                                        : '场次',
                                                    style:
                                                        TextStyles.textSize12,
                                                  )),
                                              Expanded(
                                                  flex: 1,
                                                  child: Text(
                                                    '倍数',
                                                    style:
                                                        TextStyles.textSize12,
                                                  )),
                                              Expanded(
                                                  flex: 1,
                                                  child:
                                                      getbetMethodName(model)),
                                              Expanded(
                                                  flex: 1,
                                                  child: Container(
                                                      alignment:
                                                          Alignment.centerRight,
                                                      child: Text(
                                                        '状态',
                                                        style: TextStyles
                                                            .textSize12,
                                                      ))),
                                            ],
                                          ),
                                        ),
                                        for (var item in model.drawList!)
                                          Column(
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Expanded(
                                                      flex: 4,
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children:
                                                            getTicketMatchName(
                                                                model, item),
                                                      )),
                                                  Expanded(
                                                      flex: 1,
                                                      child: Text(
                                                          "${item.multiple}")),
                                                  Expanded(
                                                      flex: 1,
                                                      child: getBetTicketType(
                                                          model, item)),
                                                  Expanded(
                                                      flex: 1,
                                                      child: Container(
                                                          alignment: Alignment
                                                              .centerRight,
                                                          child: Text(
                                                            "${item.ticketStatus!}",
                                                            style: item.ticketStatus ==
                                                                    '已中奖'
                                                                ? TextStyle(
                                                                    fontSize: Dimens
                                                                        .font_sp12,
                                                                    color: Colors
                                                                        .red)
                                                                : TextStyle(
                                                                    fontSize: Dimens
                                                                        .font_sp12),
                                                          ))),
                                                ],
                                              ),
                                            ],
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                    ],
                  )))
        ],
      );
    });
  }
}

class AcknowledgeButton extends StatelessWidget {
  final String title;

  final VoidCallback? onPressed;

  final Color? color;

  final Color? textColor;

  final double minHeight;

  final double minWidth;

  final double textSize;

  const AcknowledgeButton(this.title,
      {this.onPressed,
      this.color,
      this.textColor = const Color(0xFFFFFFFF),
      this.textSize = Dimens.font_sp12,
      this.minWidth = 60,
      this.minHeight = 30,
      super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: Colors.red, // 边框颜色为红色
            width: 0.4, // 边框宽度为1像素
          ),
        ),
        width: minWidth,
        height: minHeight,
        alignment: Alignment.center,
        child: Text(
          "$title",
          style: TextStyle(color: textColor, fontSize: textSize),
        ),
      ),
    );
  }
}

//竞彩足球 竞彩篮球 北京单场 欧亚足球 排列三 排列五 福彩3D 双色球 大乐透 七星彩
class FirstOrderContentLayout extends StatefulWidget {
  final OrderDetailModel model;

  final String orderID;

  const FirstOrderContentLayout(this.model, this.orderID, {super.key});

  @override
  FirstOrderContentLayoutState createState() {
    return FirstOrderContentLayoutState();
  }
}

class FirstOrderContentLayoutState extends State<FirstOrderContentLayout> {
  Widget getMatchName(model, item) {
    Widget matchWidget = const Text("未知赛事");

    if (model.commonData!.type == '竞彩篮球') {
      matchWidget = Text(
        '${item.awayTeam}VS(主)${item.homeTeam}',
        softWrap: true,
        style: TextStyles.textSize12,
      );
    }

    if (model.commonData!.type == '竞彩足球') {
      matchWidget = Text(
        '${item.homeTeam}(主)VS${item.awayTeam}',
        softWrap: true,
      );
    }

    if (model.commonData!.type == '北京单场') {
      matchWidget = Text(
        '${item.homeTeam}(主)VS${item.awayTeam}',
        softWrap: true,
      );
    }

    if (model.commonData!.type == '欧亚足球') {
      matchWidget = Text(
        '${item.homeTeam}(主)VS${item.awayTeam}',
      );
    }

    if (model.commonData!.type == '排列三') {
      matchWidget = Text(
        item.boutIndex!,
      );
    }
    if (model.commonData!.type == '排列五') {
      matchWidget = Text(
        item.boutIndex!,
      );
    }
    if (model.commonData!.type == '福彩3d') {
      matchWidget = Text(
        item.boutIndex!,
      );
    }

    return matchWidget;
  }

  List<Widget> buildOrderItemList(
      OrderDetailModel model, List<CollectInfo> matchList) {
    List<Widget> _list;

    _list = matchList.map((item) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 15),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFe3e3e3),
              width: 0.4,
            ),
          ),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    flex: 4,
                    child: Container(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            children: [
                              Text(
                                item.week!,
                                style: TextStyles.textGray12,
                              ),
                              Text(
                                " ${item.league!}",
                                style: const TextStyle(
                                    color: Color(0xFF658ce7),
                                    fontSize: Dimens.font_sp12),
                              ),
                            ],
                          ),
                          getMatchName(model, item)
                        ],
                      ),
                    )),
                Expanded(
                    flex: 3,
                    child: Container(
                      alignment: Alignment.center,
                      child: getBetType(model, item),
                    )),
                if (model.commonData!.type == '北京单场')
                  Expanded(
                      flex: 1,
                      child: Container(
                        alignment: Alignment.center,
                        child: Text(
                          "${item.concede!}",
                          style: TextStyles.textSize12,
                        ),
                      )),
                Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        if (item.autoFlag == 2 || item.autoFlag == 3)
                          const Text(
                            '取消',
                            style: TextStyles.textSize12,
                          ),
                        if (item.autoFlag == 0)
                          Text(
                            item.scoreInfo!.first,
                            style: TextStyles.textSize12,
                          ),
                        if (item.autoFlag == 1)
                          for (var score in item.scoreInfo!)
                            Text(
                              score,
                              style: TextStyles.textSize12,
                            ),
                        if (item.lotteryNumber != null)
                          Text(
                            item.lotteryNumber!,
                            style: TextStyles.textSize12,
                          ),
                      ],
                    )),
              ],
            ),
          ],
        ),
      );
    }).toList();

    return _list;
  }

  Widget getBetType(OrderDetailModel model, CollectInfo item) {
    List<Widget> typeWidgetList = [];

    if (model.commonData!.type == '北京单场') {
      item.tips!.asMap().forEach((index, tip) {
        Widget temp = tip.win
            ? Text(
                '${tip.name}(${item.spResult?[index]})',
                style: TextStyle(
                  fontSize: Dimens.font_sp12,
                  color: Colors.red,
                ),
              )
            : Text(
                tip.name,
                style: TextStyles.textDarkGray12,
              );

        typeWidgetList.add(temp);
      });
    } else if (model.commonData!.type == '排列三' ||
        model.commonData!.type == '排列五' ||
        model.commonData!.type == '福彩3d') {
      Widget temp = Text(
        item.content!,
        style: TextStyles.textDarkGray12,
      );
      typeWidgetList.add(temp);
    } else {
      item.tips!.asMap().forEach((index, tip) {
        Widget temp = tip.win
            ? Text(
                '${tip.name} ${tip.sp}',
                style: const TextStyle(
                  fontSize: Dimens.font_sp12,
                  color: Colors.red,
                ),
              )
            : Text(
                '${tip.name} ${tip.sp}',
                style: TextStyles.textDarkGray12,
              );

        typeWidgetList.add(temp);
      });
    }

    // if (model.commonData!.type == '竞彩足球') {
    //   for (var tip in item.tips!) {
    //     Widget temp = tip.win
    //         ? Text(
    //             '${tip.name} ${tip.sp}',
    //             style: TextStyle(
    //               fontSize: Dimens.font_sp12,
    //               color: Colors.red,
    //             ),
    //           )
    //         : Text(
    //             '${tip.name} ${tip.sp}',
    //             style: TextStyles.textDarkGray12,
    //           );

    //     typeWidgetList.add(temp);
    //   }
    // }

    // if (model.commonData!.type == '竞彩篮球') {
    //   for (var tip in item.tips!) {
    //     Widget temp = Text(
    //       '${tip.name} ${tip.sp}',
    //       style: TextStyles.textDarkGray12,
    //     );
    //     typeWidgetList.add(temp);
    //   }
    // }

    // if (model.commonData!.type != '北京单场' &&
    //     model.commonData!.type != '排列三' &&
    //     model.commonData!.type != '排列五' &&
    //     model.commonData!.type != '福彩3d' &&
    //     model.commonData!.type != '竞彩足球' &&
    //     model.commonData!.type != '竞彩篮球') {

    // if (model.commonData!.type != '北京单场' &&
    //     model.commonData!.type != '排列三' &&
    //     model.commonData!.type != '排列五' &&
    //     model.commonData!.type != '福彩3d' &&
    //     model.commonData!.type != '竞彩足球' &&
    //     model.commonData!.type != '竞彩篮球') {
    //   for (var tip in item.tips!) {
    //     Widget temp = Text(
    //       '${tip.name} ${tip.sp}',
    //       style: TextStyles.textDarkGray12,
    //     );
    //     typeWidgetList.add(temp);
    //   }
    // }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [...typeWidgetList],
    );
  }

  Widget gglDrawingStatusLabel(
      String labelText, CommonInfo? order, String orderID) {
    Widget _widget;
    if (order == null) {
      throw Exception("Order not found");
    }

    switch (labelText) {
      case "出票成功":
        _widget = GestureDetector(
          onTap: () {
            String filterAmount;
            if (order.amount == null) {
              return;
            }

            if (order.amount!.contains('.')) {
              filterAmount = order.amount!.split('.').first;
            } else {
              filterAmount = order.amount!;
            }

            NavigatorUtils.push(
              context,
              "${GameRouter.gglDetailPage}/${orderID}/${filterAmount}",
            );
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 2),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.0),
                color: Color(0xFFd14444)),
            child: Text(
              "去刮开",
              style: TextStyles.textWhite12,
            ),
          ),
        );

        break;
      case "已撤单":
        _widget = Text(
          "已撤单",
          style: TextStyles.textSize12,
        );
        break;
      default:
        _widget = Row(
          children: [
            Text(
              "${labelText}",
              style: TextStyles.textSize12,
            ),
            Gaps.hGap4,
            GestureDetector(
              onTap: () {
                String filterAmount;
                if (order.amount == null) {
                  return;
                }

                if (order.amount!.contains('.')) {
                  filterAmount = order.amount!.split('.').first;
                } else {
                  filterAmount = order.amount!;
                }
                NavigatorUtils.push(
                  context,
                  "${GameRouter.gglDetailPage}/${orderID}/${filterAmount}",
                );
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.0),
                    color: Color(0xFFd14444)),
                child: Text(
                  "查详情",
                  style: TextStyles.textWhite12,
                ),
              ),
            )
          ],
        );
    }

    return _widget;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      //     margin: EdgeInsets.only(top: 5),
      child: Column(
        children: [
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      child: const Text(
                        "订单内容",
                        style: TextStyles.textBold16,
                      ),
                    ),
                  ],
                ),
                if (widget.model.commonData!.type == '竞彩足球' ||
                    widget.model.commonData!.type == '竞彩篮球' ||
                    widget.model.commonData!.type == '北京单场' ||
                    widget.model.commonData!.type == '欧亚足球')
                  Row(
                    children: [
                      const Text(
                        "过关方式 ",
                        style: TextStyles.textGray12,
                      ),
                      Text(
                        "${widget.model.chuanTip}",
                        style: TextStyles.textGray12,
                      ),
                    ],
                  ),
              ],
            ),
          ),
          if (widget.model.commonData!.type == '竞彩足球' ||
              widget.model.commonData!.type == '竞彩篮球' ||
              widget.model.commonData!.type == '北京单场' ||
              widget.model.commonData!.type == '欧亚足球' ||
              widget.model.commonData!.type == '排列三' ||
              widget.model.commonData!.type == '排列五' ||
              widget.model.commonData!.type == '福彩3d')
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Column(
                children: [
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(vertical: 4),
                        color: Color(0xFFf6f6f6),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                                flex: 4,
                                child: Container(
                                  child: Text(
                                    widget.model.commonData!.type == '排列三' ||
                                            widget.model.commonData!.type ==
                                                '排列五' ||
                                            widget.model.commonData!.type ==
                                                '福彩3d'
                                        ? '期号'
                                        : '场次',
                                    style: TextStyles.textSize12,
                                  ),
                                )),
                            Expanded(
                                flex: 3,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    '投注项',
                                    style: TextStyles.textSize12,
                                  ),
                                )),
                            if (widget.model.commonData!.type == '北京单场')
                              Expanded(
                                  flex: 1,
                                  child: Container(
                                    alignment: Alignment.center,
                                    child: const Text(
                                      '让',
                                      style: TextStyles.textSize12,
                                    ),
                                  )),
                            Expanded(
                              flex: 1,
                              child: Container(
                                alignment: Alignment.centerRight,
                                child: Text(
                                  widget.model.commonData!.type == '排列三' ||
                                          widget.model.commonData!.type ==
                                              '排列五' ||
                                          widget.model.commonData!.type ==
                                              '福彩3d' ||
                                          widget.model.commonData!.type == '双色球'
                                      ? '中奖号'
                                      : '赛果',
                                  style: TextStyles.textSize12,
                                ),
                              ),
                            ),
                            // if (widget.model.commonData!.type == '竞彩足球' ||
                            //     widget.model.commonData!.type == '竞彩篮球')
                            //   Expanded(
                            //       flex: 1,
                            //       child: Container(
                            //         alignment: Alignment.center,
                            //         child: const Text(
                            //           '胆',
                            //           style: TextStyles.textSize12,
                            //         ),
                            //       )),
                          ],
                        ),
                      ),
                      ...buildOrderItemList(
                          widget.model, widget.model.matchList!),
                      if (widget.model.commonData!.type == '北京单场')
                        Container(
                            margin: const EdgeInsets.only(top: 15),
                            child: const Text(
                                '北京单场中奖奖金 = 2元 x 所选场次的单场开奖SP值连乘（第一场为SP1，第二场为SP2，……第n场为SPn）x 倍数x 65%。（注：开奖SP = 每场总投注金额/中奖彩票的总投金额；65%为官方返奖率）',
                                style: TextStyle(
                                    color: Color(0xFFf68174),
                                    fontSize: Dimens.font_sp12)))
                    ],
                  ),
                ],
              ),
            ),
          if (widget.model.commonData!.type == '刮刮乐')
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        width: 60,
                        child: Text(
                          "名称",
                          style: TextStyles.textGray12,
                        ),
                      ),
                      Text(
                        "中国红-${widget.model.commonData!.type}",
                        style: TextStyles.textSize12,
                      ),
                    ],
                  ),
                  Gaps.vGap4,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        width: 60,
                        child: Text(
                          "购买面值:",
                          style: TextStyles.textGray12,
                        ),
                      ),
                      Text(
                        "${widget.model.commonData!.amount}",
                        style: TextStyles.textSize12,
                      ),
                    ],
                  ),
                  Gaps.vGap4,
                  if (widget.model.commonData!.status == '出票成功')
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Container(
                          width: 60,
                          child: Text(
                            "存放到期:",
                            style: TextStyles.textGray12,
                          ),
                        ),
                        Text(
                          "${gglExpTime(widget.model.commonData!.buyTime!)}小时",
                          style: TextStyles.textSize12,
                        ),
                      ],
                    ),
                  Gaps.vGap4,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        width: 60,
                        child: Text(
                          "刮状态:",
                          style: TextStyles.textGray12,
                        ),
                      ),
                      gglDrawingStatusLabel(widget.model.commonData!.status!,
                          widget.model.commonData, widget.orderID),
                    ],
                  ),
                ],
              ),
            ),
          if (widget.model.commonData!.type == '双色球' ||
              widget.model.commonData!.type == '大乐透' ||
              widget.model.commonData!.type == '七星彩')
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: Column(
                children: [
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(vertical: 4),
                        color: Color(0xFFf6f6f6),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                                flex: 1,
                                child: Container(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    '期号',
                                    style: TextStyles.textSize12,
                                  ),
                                )),
                            Expanded(
                                flex: 2,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    '投注项',
                                    style: TextStyles.textSize12,
                                  ),
                                )),
                            Expanded(
                                flex: 1,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: const Text(
                                    '中奖号',
                                    style: TextStyles.textSize12,
                                  ),
                                )),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Color(0xFFe3e3e3),
                              width: 0.4,
                            ),
                          ),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                    flex: 1,
                                    child: Container(
                                      // color: Colors.red,
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        widget
                                            .model.matchList!.first.boutIndex!,
                                        style: TextStyles.textGray12,
                                      ),
                                    )),
                                Expanded(
                                    flex: 2,
                                    child: Container(
                                      //    color: Colors.red,
                                      alignment: Alignment.center,
                                      child: Text(
                                        widget.model.matchList!.first.content!,
                                        style: TextStyles.textGray12,
                                      ),
                                    )),
                                Expanded(
                                    flex: 1,
                                    child: Container(
                                      //   color: Colors.red,
                                      alignment: Alignment.center,
                                      child: Text(
                                        widget.model.matchList!.first
                                            .lotteryNumber!,
                                        style: TextStyles.textGray12,
                                      ),
                                    )),
                              ],
                            ),
                          ],
                        ),
                      ),
                      if (widget.model.commonData!.type == '北京单场')
                        Container(
                            margin: const EdgeInsets.only(top: 15),
                            child: const Text(
                                '北京单场中奖奖金 = 2元 x 所选场次的单场开奖SP值连乘（第一场为SP1，第二场为SP2，……第n场为SPn）x 倍数x 65%。（注：开奖SP = 每场总投注金额/中奖彩票的总投金额；65%为官方返奖率）',
                                style: TextStyle(
                                    color: Color(0xFFf68174),
                                    fontSize: Dimens.font_sp12)))
                    ],
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}



//	<!-- 足球14场/足球任选9/四场进球/6场半全场 竞猜冠军 竞猜冠亚军 -->
class SecondOrderContentLayout extends StatefulWidget {
  final OrderDetailModel model;

  const SecondOrderContentLayout(this.model, {super.key});

  @override
  SecondOrderContentLayoutState createState() {
    return SecondOrderContentLayoutState();
  }
}

class SecondOrderContentLayoutState extends State<SecondOrderContentLayout> {
  String generate6cbResult(OrderDetailModel model, ContentMatch item) {
    String result = '未开奖';

    if (model.commonData!.type == '6场半全场') {
      if (item.firstHalf != null &&
          item.firstHalf != "" &&
          item.secondHalf != null &&
          item.secondHalf != "") {
        result = "(半) ${item.firstHalf} ||  (全) ${item.secondHalf}";
      } else {
        result = "未开奖";
      }
    } else {
      if (item.result != null && item.result != "") {
        result = item.result!;
      }
    }

    return result;
  }

//5列结果
  Widget generate5ItemsOption(OrderDetailModel model, ContentMatch item) {
    List<TextSpan> listSpan = [];

    if (item.option != null && item.option!.isNotEmpty) {
      listSpan = item.option!.map((e) {
        return TextSpan(
          text: "${e} ",
          style: TextStyle(
              color: e == item.result ? Colors.red : Colors.black,
              fontSize: Dimens.font_sp12),
        );
      }).toList();
    }

    // if (model.commonData!.type == '四场进球' &&
    //         item.homeOption != null &&
    //         item.homeOption!.isNotEmpty ||
    //     item.awaryOption != null && item.awaryOption!.isNotEmpty) {

    if (model.commonData!.type == '四场进球' &&
        item.homeOption != null &&
        item.homeOption!.isNotEmpty) {
      List<TextSpan> homeOptionSpan = item.homeOption!.map((child) {
        Color colorRGB = Colors.black;
        if (item.result != null && item.result!.isNotEmpty) {
          List<String> tmpResultList = item.result!.split(':');
          if (child == tmpResultList.first.trim() ||
              (child == '3+' && (int.parse(tmpResultList.first.trim()) >= 3))) {
            colorRGB = Colors.red;
          }
        }

        return TextSpan(
          text: "${child} ",
          style: TextStyle(color: colorRGB, fontSize: Dimens.font_sp12),
        );
      }).toList();

      List<TextSpan> awarySpan = item.awaryOption!.map((child) {
        Color colorRGB = Colors.black;
        if (item.result != null && item.result!.isNotEmpty) {
          List<String> tmpResultList = item.result!.split(':');
          if (child == tmpResultList[1].trim() ||
              (child == '3+' && (int.parse(tmpResultList[1].trim()) >= 3))) {
            colorRGB = Colors.red;
          }
        }

        return TextSpan(
          text: "${child} ",
          style: TextStyle(color: colorRGB, fontSize: Dimens.font_sp12),
        );
      }).toList();

      TextSpan homeWrapper = TextSpan(
        text: "(主) ",
        style: TextStyle(color: Colors.black, fontSize: Dimens.font_sp12),
      );

      TextSpan awayWrapper = TextSpan(
        text: "(客) ",
        style: TextStyle(color: Colors.black, fontSize: Dimens.font_sp12),
      );

      TextSpan joinSpan = const TextSpan(
        text: " || ",
        style: TextStyle(color: Colors.black, fontSize: Dimens.font_sp12),
      );

      listSpan = [
        homeWrapper,
        ...homeOptionSpan,
        joinSpan,
        awayWrapper,
        ...awarySpan
      ];
    }

    if (model.commonData!.type == '6场半全场') {
      // if (model.commonData!.type == '6场半全场' &&
      //         item.homeOption != null &&
      //         item.homeOption!.isNotEmpty ||
      //     item.awaryOption != null && item.awaryOption!.isNotEmpty) {

      List<TextSpan> homeOptionSpan = item.homeOption!.map((child) {
        Color colorRGB = Colors.black;

        if (item.firstHalf != null && item.firstHalf != "") {
          if (child == item.firstHalf || item.firstHalf == '*') {
            colorRGB = Colors.red;
          }
        }

        return TextSpan(
          text: "${child} ",
          style: TextStyle(color: colorRGB, fontSize: Dimens.font_sp12),
        );
      }).toList();

      List<TextSpan> awarySpan = item.awaryOption!.map((child) {
        Color colorRGB = Colors.black;
        if (item.secondHalf != null && item.secondHalf!.isNotEmpty) {
          if (child == item.secondHalf || item.firstHalf == '*') {
            colorRGB = Colors.red;
          }
        }

        return TextSpan(
          text: "${child} ",
          style: TextStyle(color: colorRGB, fontSize: Dimens.font_sp12),
        );
      }).toList();

      TextSpan joinSpan = const TextSpan(
        text: " || ",
        style: TextStyle(color: Colors.black, fontSize: Dimens.font_sp12),
      );

      TextSpan halfWrapper = TextSpan(
        text: "(半) ",
        style: TextStyle(color: Colors.black, fontSize: Dimens.font_sp12),
      );

      TextSpan homeWrapper = TextSpan(
        text: "(全) ",
        style: TextStyle(color: Colors.black, fontSize: Dimens.font_sp12),
      );

      listSpan = [
        halfWrapper,
        ...homeOptionSpan,
        joinSpan,
        homeWrapper,
        ...awarySpan
      ];
    }

    return Text.rich(
      textAlign: TextAlign.start,
      //  overflow: TextOverflow.ellipsis,
      TextSpan(
        text: "",
        style: TextStyles.textGray14,
        children: <TextSpan>[...listSpan],
      ),
    );
  }

  //竞彩14/9/4场进球/6场半
  List<Widget> build5ItemsList(
      OrderDetailModel model, List<CollectInfo> matchList) {
    List<Widget> _list;

    _list = matchList.first.listContent!.map((_item) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 15),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFe3e3e3),
              width: 0.4,
            ),
          ),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    flex: 1,
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        "${_item.matchNum}",
                        style: TextStyles.textSize12,
                      ),
                    )),
                Expanded(
                    flex: 2,
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        "${_item.homeTeam!}",
                        style: TextStyles.textSize12,
                      ),
                    )),
                Expanded(
                    flex: 3,
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        generate6cbResult(model, _item),
                        style: TextStyles.textSize12,
                      ),
                    )),
                Expanded(
                    flex: 2,
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        "${_item.awayTeam!}",
                        style: TextStyles.textSize12,
                      ),
                    )),
                Expanded(
                    flex: 6,
                    child: Container(
                      alignment: Alignment.center,
                      child: generate5ItemsOption(model, _item),
                    )),
              ],
            ),
          ],
        ),
      );
    }).toList();

    return _list;
  }

  String generate4ItemsTeamLabel(String type, CollectInfo item) {
    String _label;
    if (type == '竞猜冠亚军') {
      _label =
          item.team1 == '其它' ? item.team1! : "${item.team1!}- ${item.team2!}";
    } else {
      _label = item.team1!;
    }

    return _label;
  }

  //竞猜冠军 竞猜冠亚军
  List<Widget> build4ItemsList(
      OrderDetailModel model, List<CollectInfo> matchList) {
    List<Widget> _list;

    _list = matchList.map((_item) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 15),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFe3e3e3),
              width: 0.4,
            ),
          ),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    flex: 1,
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        "${_item.boutIndex}",
                        style: TextStyles.textSize12,
                      ),
                    )),
                Expanded(
                    flex: 1,
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        "${generate4ItemsTeamLabel(model.commonData!.type!, _item)}",
                        style: TextStyles.textSize12,
                      ),
                    )),
                Expanded(
                    flex: 1,
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        "${_item.sp!}",
                        style: TextStyles.textSize12,
                      ),
                    )),
                Expanded(
                    flex: 1,
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        "${_item.status!}",
                        style: _item.status == '获胜'
                            ? TextStyle(
                                color: Colors.red, fontSize: Dimens.font_sp12)
                            : TextStyles.textSize12,
                      ),
                    )),
              ],
            ),
          ],
        ),
      );
    }).toList();

    return _list;
  }

  @override
  Widget build(BuildContext context) {
    Widget _widget = Container();

    if (widget.model.commonData!.type == '足球任选9' ||
        widget.model.commonData!.type == '足球14场' ||
        widget.model.commonData!.type == '四场进球' ||
        widget.model.commonData!.type == '6场半全场') {
      _widget = Container(
        //     margin: EdgeInsets.only(top: 5),
        child: Column(
          children: [
            Container(
              color: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        child: const Text(
                          "订单内容",
                          style: TextStyles.textBold16,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      const Text(
                        "开奖期号 第",
                        style: TextStyles.textGray14,
                      ),
                      Text(
                        "${widget.model.matchList!.first.boutIndex}期",
                        style: TextStyles.textGray14,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: Column(
                children: [
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(vertical: 4),
                        color: const Color(0xFFf6f6f6),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                                flex: 1,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    '场次',
                                    style: TextStyles.textSize12,
                                  ),
                                )),

                            Expanded(
                                flex: 2,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    '主队',
                                    style: TextStyles.textSize12,
                                  ),
                                )),

                            Expanded(
                                flex: 3,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    '赛果',
                                    style: TextStyles.textSize12,
                                  ),
                                )),

                            Expanded(
                              flex: 2,
                              child: Container(
                                alignment: Alignment.center,
                                child: Text(
                                  '客队',
                                  style: TextStyles.textSize12,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 6,
                              child: Container(
                                alignment: Alignment.center,
                                child: Text(
                                  '投注项',
                                  style: TextStyles.textSize12,
                                ),
                              ),
                            ),
                            // if (model.commonData!.type ==
                            //         '竞彩足球' ||
                            //     model.commonData!.type ==
                            //         '竞彩篮球')
                            //   Expanded(child: Text('胆',style: TextStyles.textSize12,)) ,
                          ],
                        ),
                      ),
                      ...build5ItemsList(widget.model, widget.model.matchList!),
                      Container(
                          margin: const EdgeInsets.only(top: 15),
                          child: const Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('提示:',
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: Dimens.font_sp12)),
                              Text('若开奖赛果为" * "，该比赛场次任何竞猜结果均为猜中',
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: Dimens.font_sp12))
                            ],
                          ))
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      );
    }

    if (widget.model.commonData!.type == '竞猜冠军' ||
        widget.model.commonData!.type == '竞猜冠亚军') {
      _widget = Container(
        //     margin: EdgeInsets.only(top: 5),
        child: Column(
          children: [
            Container(
              color: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        child: const Text(
                          "订单内容",
                          style: TextStyles.textBold16,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      const Text(
                        "开奖期号 第",
                        style: TextStyles.textGray14,
                      ),
                      Text(
                        "${widget.model.matchList!.first.boutIndex}期",
                        style: TextStyles.textGray14,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: Column(
                children: [
                  Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        color: const Color(0xFFf6f6f6),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                                flex: 1,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    '编号',
                                    style: TextStyles.textSize12,
                                  ),
                                )),
                            Expanded(
                                flex: 1,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    '投注项',
                                    style: TextStyles.textSize12,
                                  ),
                                )),
                            Expanded(
                                flex: 1,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    '赔率',
                                    style: TextStyles.textSize12,
                                  ),
                                )),
                            Expanded(
                              flex: 1,
                              child: Container(
                                alignment: Alignment.center,
                                child: Text(
                                  '赛果',
                                  style: TextStyles.textSize12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      ...build4ItemsList(widget.model, widget.model.matchList!),
                      Container(
                          margin: const EdgeInsets.only(top: 15),
                          child: const Text('若开奖赛果为" * "，该比赛场次任何竞猜结果均为猜中',
                              style: TextStyle(
                                  color: Color(0xFFf68174),
                                  fontSize: Dimens.font_sp12)))
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      );
    }

    if (widget.model.commonData!.type == '刮刮乐' ||
        widget.model.commonData!.type == '顶呱刮') {
      _widget = Container(
        //     margin: EdgeInsets.only(top: 5),
        child: Column(
          children: [
            Container(
              color: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        child: const Text(
                          "订单内2容",
                          style: TextStyles.textBold16,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      const Text(
                        "开奖期号 第",
                        style: TextStyles.textGray14,
                      ),
                      Text(
                        "${widget.model.matchList!.first.boutIndex}期",
                        style: TextStyles.textGray14,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: Column(
                children: [
                  Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        color: const Color(0xFFf6f6f6),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                                flex: 1,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    '编号',
                                    style: TextStyles.textSize12,
                                  ),
                                )),
                            Expanded(
                                flex: 1,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    '投注项',
                                    style: TextStyles.textSize12,
                                  ),
                                )),
                            Expanded(
                                flex: 1,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    '赔率',
                                    style: TextStyles.textSize12,
                                  ),
                                )),
                            Expanded(
                              flex: 1,
                              child: Container(
                                alignment: Alignment.center,
                                child: Text(
                                  '赛果',
                                  style: TextStyles.textSize12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      ...build4ItemsList(widget.model, widget.model.matchList!),
                      Container(
                          margin: const EdgeInsets.only(top: 15),
                          child: const Text('若开奖赛果为" * "，该比赛场次任何竞猜结果均为猜中',
                              style: TextStyle(
                                  color: Color(0xFFf68174),
                                  fontSize: Dimens.font_sp12)))
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      );
    }

    return _widget;
  }
}
