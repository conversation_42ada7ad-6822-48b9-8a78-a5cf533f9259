import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/order/model/order_model.dart';
import 'package:sport_mobile/order/widgets/order_list_refresh.dart';
import 'package:sport_mobile/promotion/promotion_router.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';

import 'package:sport_mobile/widgets/my_app_bar.dart';

class OrderFollowPage extends StatefulWidget {

  const OrderFollowPage( {super.key});

  @override
  _OrderFollowPageState createState() => _OrderFollowPageState();
}

class _OrderFollowPageState extends State<OrderFollowPage> {
  String title = "我的跟单";

  @override
  void initState() {
    super.initState();
  }

  void shareQRCode() {
    NavigatorUtils.push(context, PromotionRouter.sharePage);
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
        create: (context) {
          return OrderModel();
        },
        child: Scaffold(
          backgroundColor: const Color(0xFFf2f2f2),
          appBar: MyAppBar(
            centerTitle: title,
            backImgColor: Colors.white,
          ),
          body: Column(
            children: [
              Expanded(
                child: Container(
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Color(0xFF4268cb), Color(0xFF8c7eec)], // 渐变色数组
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                    ),
                    child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                        decoration: const BoxDecoration(
                          color: Color(0xFFf2f2f2),
                          ///color: Colors.red,
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(10),
                              topRight: Radius.circular(10)), // 设置圆角大小
                        ),
                        child: const OrderListRefresh('follow'))),
              )
            ],
          ),
        ));
  }
}
