import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/net/ws_utils.dart';
import 'package:sport_mobile/order/entity/order_entity.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/util/date_utils.dart';
import 'package:sport_mobile/util/other_utils.dart';
import 'package:sport_mobile/net/network_state.dart';

class OrderModel extends ChangeNotifier {
  bool isLoad = false;
  NetworkStatus networkStatus = NetworkStatus.networkConnected;
  bool hasMoreData = true;

  List<OrderEntity> orderList = [];

  Future loadMore(int page, String action) async {
    isLoad = true;
    try {
      var tempList = await getOrderList(page, action);
      isLoad = false;
      networkStatus = NetworkStatus.networkConnected;

      if (tempList.isEmpty) {
        hasMoreData = false;
      } else {
        orderList.addAll(tempList);
      }
    } on TimeoutException catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkTimeout;
    } catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkFailed;
    }

    notifyListeners();
  }

  Future refreshData(int page, String action) async {
    isLoad = true;

    try {
      var tempList = await getOrderList(page, action);

      isLoad = false;
      networkStatus = NetworkStatus.networkConnected;

      if (tempList.isEmpty) {
        hasMoreData = false;
      } else {
        orderList = tempList;
        hasMoreData = true;
      }

      //后台没有给是否没数据的状态，只能再去获取一次
      getOrderList(page + 1, action).then((hasMoreList) {
        if (hasMoreList.isEmpty) {
          hasMoreData = false;
          notifyListeners();
        }
      });
    } on TimeoutException catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkTimeout;
    } catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkFailed;
    }

    notifyListeners();
  }

  Future getOrderList(int page, String action) async {
    String actionParam, result;
    switch (action) {
      case 'pending':
        actionParam = "0";
        result = "1";
        break;
      case 'result':
        actionParam = "0";
        result = "2";
        break;
      case 'follow':
        actionParam = "2";
        result = "0";
        break;
      default:
        actionParam = "0";
        result = "0";
    }
    String? token = SpUtil.getString(Constant.token);

    var parameters = {
      'version': Constant.version,
      'token': "$token",
      'page': "$page",
      'action': actionParam,
      'result': result,
      'channel': Utils.getDeviceName(),
    };

    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest =
        BaseRequestEntity(Api.getlistWithOrder, form);
    Map<String, dynamic> requestDataMap = baseRequest.toJson();
    var respStr = await WsUtils.instance.sendMessageAndWaitForResponse(
        jsonEncode(requestDataMap),
        showProgress: false);

    List<dynamic> dataList = jsonDecode(respStr);

    List<OrderEntity> res = dataList.map((value) {
      return OrderEntity.fromJson(value);
    }).toList();

    res.map((item) {
      item.orderAction = getActionType(item);
      item.yearMonthDay =
          DateFormatUtils.getYearMonthDayFromTimestamp(item.time!);
      item.gameImgPath = Utils.getMatchImage(item.playType!);
      item.statusImgPath = Utils.getStatusImage(item.status!);
      return item;
    }).toList();

    return res;
  }

  Future getOrderDetail(int orderID) async {
    String? token = SpUtil.getString(Constant.token);

    var parameters = {
      'version': Constant.version,
      'token': "$token",
      'order_id': orderID,
      'channel': Utils.getDeviceName(),
    };

    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest = BaseRequestEntity(Api.getOrderDetail, form);
    Map<String, dynamic> requestDataMap = baseRequest.toJson();
    var respStr = await WsUtils.instance
        .sendMessageAndWaitForResponse(jsonEncode(requestDataMap));

    List<dynamic> dataList = jsonDecode(respStr);

    List<OrderEntity> res = dataList.map((value) {
      return OrderEntity.fromJson(value);
    }).toList();

    res.map((item) {
      item.orderAction = getActionType(item);
      item.yearMonthDay =
          DateFormatUtils.getYearMonthDayFromTimestamp(item.time!);

      item.gameImgPath = Utils.getMatchImage(item.playType!);

      item.statusImgPath = Utils.getStatusImage(item.status!);

      return item;
    }).toList();

    return res;
  }

  String getActionType(OrderEntity item) {
    if (item.isCopy == "1") {
      return '跟单';
    } else if (item.isOptimization == "1" && item.isCopy != "1") {
      return '代购';
    } else if (item.isZhuihao == "0" &&
        item.isDaigou == "1" &&
        item.isAdd == "0" &&
        item.isOptimization == "0") {
      return '代购';
    } else if (item.isZhuihao == "1") {
      return '追号';
    } else if (item.isAdd == "1" && item.isCommunity == "1") {
      return '追号';
    } else if (item.isAdd == "0") {
      return '追加';
    } else if (item.isCommunity == "1") {
      return '合买';
    } else {
      return ' ';
    }
  }
}
