import 'package:flutter/material.dart';

class OrderStatusEntity {
  String? status;
  String? message;
  int? stauts;


  List<Data>? data;
  Map<String,Data>? dataMap;

  List<String>? reasonData;

  bool whetherCancel = false;


  OrderStatusEntity(
      {this.status, this.message, this.stauts, this.data, this.reasonData,this.whetherCancel =  false});

  OrderStatusEntity.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    stauts = json['stauts'];
    if (json['data'] != null) {
      data = <Data>[];

      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
    reasonData = json['reason_data'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['stauts'] = this.stauts;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['reason_data'] = this.reasonData;
    return data;
  }
}

class Data {
  String? status;
  String? msg;
  String? time;
  IconData? icon;

  Data({this.status, this.msg, this.time, this.icon});

  Data.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    msg = json['msg'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['msg'] = this.msg;
    data['time'] = this.time;
    return data;
  }
}
