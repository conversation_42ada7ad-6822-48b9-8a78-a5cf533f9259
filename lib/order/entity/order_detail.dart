import 'package:sport_mobile/order/entity/order_tip_entity.dart';

class OrderDetailEntity {
  String? status;
  CommonInfo? commonInfo;
  List<DrawInfo>? drawInfo;
  List<CollectInfo>? collectInfo;
  String? proxyShareUrl;
  String? shareUserId;
  String? shareUserHeadImg;
  String? shareUserPetName;
  String? shareOtherUrl;

  OrderDetailEntity(
      {this.status,
      this.commonInfo,
      this.drawInfo,
      this.collectInfo,
      this.proxyShareUrl,
      this.shareUserId,
      this.shareUserHeadImg,
      this.shareUserPetName,
      this.shareOtherUrl});

  OrderDetailEntity.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    commonInfo = json['common_info'] != null
        ? new CommonInfo.fromJson(json['common_info'])
        : null;
    if (json['draw_info'] != null) {
      drawInfo = <DrawInfo>[];
      json['draw_info'].forEach((v) {
        drawInfo!.add(new DrawInfo.fromJson(v));
      });
    }
    if (json['collect_info'] != null) {
      collectInfo = <CollectInfo>[];
      json['collect_info'].forEach((v) {
        collectInfo!.add(new CollectInfo.fromJson(v));
      });
    }
    proxyShareUrl = json['proxy_share_url'];
    shareUserId = json['share_user_id'];
    shareUserHeadImg = json['share_user_head_img'];
    shareUserPetName = json['share_user_pet_name'];
    shareOtherUrl = json['share_other_url'];
  }
}

class CommonInfo {
  String? type;
  String? amount;
  String? winMoney;
  int? multiple;
  String? buyTime;
  String? jjyh;
  String? playtype;
  String? result;
  String? termNo;
  String? status;
  String? kjResult;
  String? orderNo;
  String? isAdd;
  String? isCopy;
  String? isOptimization;
  int? isPic;
  String? cpPicURL;
  String? isZhuihao;
  String? isCommunity;
  String? mosaicMoney;
  String? mosaicWinMoney;
  String? couponMoney;
  String? cpPic;
  double? balanceMoney;
  String? shareUserId;
  String? shareUserHeadImg;
  String? shareUserPetName;
  String? endTime;
  int? isSelect;
  int? minMoney;
  String? sp;
  String? imgPath;

  String? remark;

  CommonInfo({
    this.type,
    this.amount,
    this.winMoney,
    this.multiple,
    this.buyTime,
    this.jjyh,
    this.playtype,
    this.result,
    this.termNo,
    this.status,
    this.kjResult,
    this.orderNo,
    this.isAdd,
    this.isCopy,
    this.isOptimization,
    this.isPic,
    this.cpPicURL,
    this.isZhuihao,
    this.isCommunity,
    this.mosaicMoney,
    this.mosaicWinMoney,
    this.couponMoney,
    this.cpPic,
    this.balanceMoney,
    this.shareUserId,
    this.shareUserHeadImg,
    this.shareUserPetName,
    this.endTime,
    this.isSelect,
    this.minMoney,
    this.sp,
    this.imgPath,
    this.remark,
  });

  CommonInfo.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    amount = json['amount'];
    winMoney = json['win_money'];
    multiple = json['multiple'];
    buyTime = json['buy_time'];
    jjyh = json['jjyh'];
    playtype = json['playtype'];
    result = json['result'];
    termNo = json['term_no'];
    status = json['status'];
    kjResult = json['kj_result'];
    orderNo = json['order_no'];
    isAdd = json['is_add'];
    isCopy = json['is_copy'];
    isOptimization = json['is_optimization'];
    if (json['is_pic'] != null) {
      isPic = json['is_pic'];
    }

    if (json['cp_pic'] != null) {
      cpPic = json['cp_pic'];
    }

    isZhuihao = json['is_zhuihao'];
    isCommunity = json['is_community'];
    mosaicMoney = json['mosaic_money'];
    mosaicWinMoney = json['mosaic_win_money'];
    couponMoney = json['coupon_money'];
    balanceMoney = processValue(json['balance_money']);
    shareUserId = json['share_user_id'];
    shareUserHeadImg = json['share_user_head_img'];
    shareUserPetName = json['share_user_pet_name'];
    endTime = json['end_time'];
    isSelect = json['is_select'];
    minMoney = json['min_money'];
    sp = json['sp'];
    if (json['remark'] != null) {
      remark = json['remark'];
    } else {
      remark = "";
    }
  }

  double processValue(dynamic value) {
    if (value is int) {
      return value.toDouble(); // 将整数转换为浮点数
    } else if (value is double) {
      return value; // 保持原始浮点数
    } else {
      throw ArgumentError('Invalid value');
    }
  }
}

class DrawInfo {
  String? content;
  String? code;
  String? playtype;
  String? ticketStatus;
  int? multiple;

  List<Map<String, dynamic>>? chuanList;
  String? type;

  String? boutIndex;

  DrawInfo({
    this.content,
    this.code,
    this.playtype,
    this.ticketStatus,
    this.multiple,
    this.chuanList,
    this.type,
    this.boutIndex,
  });

  DrawInfo.fromJson(Map<String, dynamic> json) {
    boutIndex = json['bout_index'];
    content = json['content'];
    code = json['code'];
    playtype = json['playtype'];
    ticketStatus = json['ticket_status'];

    multiple = json['multiple'].runtimeType == String
        ? int.parse(json['multiple'])
        : json['multiple'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['content'] = this.content;
    data['code'] = this.code;
    data['playtype'] = this.playtype;
    data['ticket_status'] = this.ticketStatus;
    data['multiple'] = this.multiple;
    return data;
  }
}

class CollectInfo {
  String? boutIndex;
  String? week;
  String? league;
  String? homeTeam;
  String? awayTeam;
  String? matchTime;
  List<String>? option;
  int? autoFlag;
  List<String>? matchResult;
  List<String>? scoreInfo;
  List<String>? typeName;
  List<String>? spResult;
  List<OrderTipEntity>? tips;
  String? chuan;
  int? matchId;
  int? jhMatchid;
  String? intentMatchId;
  String? concede;
  int? necessary;
  int? type;
  String? lotteryNumber;
  String? code;
  String? content;
  List<ContentMatch>? listContent;
  String? termNo;

  double? sp;
  String? status;
  String? team1;
  String? team2;

  CollectInfo({
    this.boutIndex,
    this.week,
    this.league,
    this.homeTeam,
    this.awayTeam,
    this.matchTime,
    this.option,
    this.autoFlag,
    this.matchResult,
    this.scoreInfo,
    this.typeName,
    this.spResult,
    this.chuan,
    this.matchId,
    this.jhMatchid,
    this.intentMatchId,
    this.concede,
    this.necessary,
    this.type,
    this.lotteryNumber,
    this.code,
    this.content,
    this.termNo,
    this.sp,
    this.status,
    this.team1,
    this.team2,
  });

  CollectInfo.fromJson(Map<String, dynamic> json) {
    if (json['bout_index'] is int || json['bout_index'] is double) {
      boutIndex = json['bout_index'].toString();
    } else {
      boutIndex = json['bout_index'];
    }

    week = json['week'] == null ? "" : json['week'];
    league = json['league'] == null ? "" : json['league'];
    homeTeam = json['home_team'];
    awayTeam = json['away_team'];
    matchTime = json['match_time'];
    option = json['option'] == null ? null : json['option'].cast<String>();
    autoFlag = json['auto_flag'];
    matchResult = json['match_result'] == null
        ? null
        : json['match_result'].cast<String>();

    typeName =
        json['type_name'] != null ? json['type_name'].cast<String>() : null;

    if (json['score_info'] != null) {
      scoreInfo = [];

      json['score_info'].forEach((item) {
        scoreInfo!.add(item.toString());
      });
    }

    if (json['sp_result'] != null) {
      spResult = [];

      json['sp_result'].forEach((item) {
        spResult!.add(item.toString());
      });
    }

    chuan = json['chuan'];
    matchId = json['match_id'];
    jhMatchid = json['jh_matchid'];
    intentMatchId = json['intent_match_id'];
    concede = json['concede'] ?? "";

    necessary = json['necessary'];
    type = json['type'];
    lotteryNumber = json['lottery_number'];
    code = json['code'];

    if (json['content'] != null) {
      if (json['content'] is String) {
        content = json['content'];
      }

      if (json['content'] is List<dynamic>) {
        listContent = [];

        json['content'].forEach((v) {
          ContentMatch _tmp = ContentMatch.fromJson(v);
          listContent!.add(_tmp);
        });
      }

      if (json['content'] is Map<String, dynamic>) {
        listContent = [];

        json['content'].forEach((k, v) {
          ContentMatch _tmp = ContentMatch.fromJson(v);
          listContent!.add(_tmp);
        });
      }
    }

    if (json['term_no'] != null) {
      termNo = json['term_no'].runtimeType == int
          ? json['term_no']
          : json['term_no'].toString();
    } else {
      termNo = "";
    }

    if (json['sp'] is int) {
      sp = json['sp'].toDouble();
    } else if (json['sp'] is String) {
      sp = double.parse(json['sp']);
    } else {
      //double
      sp = json['sp'];
    }

    if (json['status'] != null) {
      status = json['status'];
    } else {
      status = '';
    }

    if (json['team1'] != null) {
      team1 = json['team1'];
    } else {
      team1 = '';
    }

    if (json['team2'] != null) {
      team2 = json['team2'];
    } else {
      team2 = '';
    }
  }
}

class ContentMatch {
  String? homeTeam;
  String? awayTeam;
  int? matchNum;
  List<String>? option;
  String? result;
  String? firstHalf;
  String? secondHalf;
  String? resultsc;

  List<String>? homeOption;
  List<String>? awaryOption;

  ContentMatch({
    this.homeTeam,
    this.awayTeam,
    this.matchNum,
    this.option,
    this.result,
    this.firstHalf,
    this.secondHalf,
    this.resultsc,
  });

  ContentMatch.fromJson(Map<String, dynamic> json) {
    homeTeam = json['home_team'];
    awayTeam = json['awary_team'];
    matchNum = json['matchNum'];
    firstHalf = json['first_half'];
    secondHalf = json['second_half'];

    if (json['option'] != null) {
      option = [];
      json['option'].forEach((v) {
        option?.add(v);
      });
    }

    if (json['home_option'] != null) {
      homeOption = [];
      json['home_option'].forEach((v) {
        homeOption?.add(v);
      });
    }

    if (json['awary_option'] != null) {
      awaryOption = [];
      json['awary_option'].forEach((v) {
        awaryOption?.add(v);
      });
    }

    if (json['result'] != null) {
      result = json['result'];
    }

    if (json['resultsc'] != null) {
      resultsc = json['resultsc'];
    }
  }
}
