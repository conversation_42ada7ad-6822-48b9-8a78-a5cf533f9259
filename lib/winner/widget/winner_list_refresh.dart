import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/default/widgets/my_custom_button.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/net/network_state.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/util/image_utils.dart';
import 'package:sport_mobile/widgets/load_image.dart';
import 'package:sport_mobile/widgets/shimmer_struct.dart';
import 'package:sport_mobile/widgets/state_layout.dart';
import 'package:sport_mobile/winner/entity/winner_list_entity.dart';
import 'package:sport_mobile/winner/model/winner_model.dart';
import 'package:sport_mobile/winner/winner_router.dart';

class WinnerListRefresh extends StatefulWidget {
  final int action;

  const WinnerListRefresh(this.action, {super.key});

  @override
  _WinnerListRefreshState createState() => _WinnerListRefreshState();
}

class _WinnerListRefreshState extends State<WinnerListRefresh> {
  List commissionList = [];

  int page = 1;

  String sort = 'desc';

  @override
  void initState() {
    super.initState();

    Provider.of<WinnerModel>(context, listen: false)
        .refreshData(page, widget.action, sort);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WinnerModel>(builder: (context, model, child) {
      if ((model.isLoad == false &&
              model.networkStatus == NetworkStatus.networkFailed) ||
          (model.isLoad == false &&
              model.networkStatus == NetworkStatus.networkTimeout)) {
        return NetworkFailedWidget(
          onPressed: () {
            setState(() {
              model.getTop5Winner(reload: true);
              model.refreshData(page, widget.action, sort);
            });
          },
        );
      }

      if (model.isLoad) {
        return SingleChildScrollView(
          child: Column(
            children: [
              ...List<Widget>.generate(5, (index) {
                return WinnerItemPreloadItem(
                    //   winner: model.winnerList[index],
                    );
              }),
            ],
          ),
        );
      }

      if (model.winnerList.isEmpty && model.isLoad == false) {
        //加载完毕，没有数据
        return const StateLayout(
          type: StateType.empty,
        );
      }

      return NotificationListener(
          onNotification: (ScrollNotification note) {
            if (note.metrics.pixels == note.metrics.maxScrollExtent) {
              page = page + 1;
              Provider.of<WinnerModel>(context, listen: false)
                  .loadMore(page, widget.action, sort);
            }
            return true;
          },
          child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10)),
              ),
              child: RefreshIndicator(
                displacement: 50.0,
                onRefresh: () {
                  page = 1;

                  return model.refreshData(page, widget.action, sort);
                },
                child: Consumer<WinnerModel>(
                  builder: (_, provider, child) {
                    return CustomScrollView(
                      slivers: <Widget>[
                        // SliverOverlapInjector(
                        //   ///SliverAppBar的expandedHeight高度,避免重叠
                        //   handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                        // ),
                        child!,
                      ],
                    );
                  },
                  child: SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    sliver: model.winnerList.isEmpty
                        ? SliverFillRemaining(
                            child: StateLayout(type: StateType.loading))
                        : SliverList(
                            delegate: SliverChildBuilderDelegate(
                                (BuildContext context, int index) {
                              return index < model.winnerList.length
                                  ? WinnerItem(
                                      winner: model.winnerList[index],
                                    )
                                  : MoreWidget(model.winnerList.length,
                                      model.hasMoreData, 10);
                            }, childCount: model.winnerList.length + 1),
                          ),
                  ),
                ),
              )));
    });
  }
}

class WinnerItemPreloadItem extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {},
        child: Container(
          margin: const EdgeInsets.only(top: 10),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10), color: Colors.white),
          //  width: double.infinity,
          child: Column(
            children: [
              Stack(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: () {},
                          child: const ClipOval(
                            //     radius: 5,
                            child: ShimmerStruct.circular(
                              height: 55,
                              width: 55,
                            ),
                          ),
                        ),
                        const Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            /// Container(width: 100, child: ShimmerStruct.rectangular(height: 20),)
                            ///
                            ShimmerStruct.rectangular(
                              height: 10,
                              width: 40,
                            ),
                            Gaps.vGap4,
                            ShimmerStruct.rectangular(
                              height: 15,
                              width: 70,
                            ),
                            Gaps.vGap4,
                            ShimmerStruct.rectangular(
                              height: 10,
                              width: 40,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                      top: 0,
                      right: 0,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          ShimmerStruct.rectangular(height: 10, width: 25),
                          Gaps.vGap5,
                          ShimmerStruct.rectangular(
                            height: 10,
                            width: 45,
                          )
                        ],
                      )),
                ],
              ),
              Container(
                alignment: Alignment.centerLeft,
                child: const ShimmerStruct.rectangular(
                  height: 20,
                  width: 100,
                ),
              ),
              Gaps.vGap4,
              const ShimmerStruct.rectangular(height: 20),
            ],
          ),
        ));
  }
}

class WinnerItem extends StatelessWidget {
  final Winner winner;
  final String defaultImageAsset = ImageUtils.getImgPath('mine/avatar_default');

  WinnerItem({super.key, required this.winner});

  List<Widget> generateGameTitles() {
    List<Widget> _list;
    _list = winner.touzhuStyleList!.map((e) {
      return Container(
        margin: EdgeInsets.only(right: 2),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5.0),
          color: const Color(0xFF698ce8),
        ),
        padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
        child: Text(
          "$e",
          style: TextStyles.textWhite12,
        ),
      );
    }).toList();

    return _list;
  }

  String generateGameName(String gameID) {
    int gameIDInt = int.parse(gameID);

    String gameName = OrderType.values[gameIDInt]!.cnname;
    return gameName;
  }

  String generateGameENName(String gameID) {
    int gameIDInt = int.parse(gameID);

    String gameName = OrderType.values[gameIDInt]!.enname;
    return gameName;
  }

  String getYearMonthDayDateTime(int unixTime) {
    DateTime dateTime =
        DateTime.fromMillisecondsSinceEpoch(unixTime * 1000).toLocal();

    String month = dateTime.month.toString().padLeft(2, '0');

    String day = dateTime.day.toString().padLeft(2, '0');
    String minute = dateTime.minute.toString().padLeft(2, '0');

    // 格式化为年月日
    final formattedDate = '${month}月${day}日 ${dateTime.hour}:${minute}';

    return formattedDate;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          NavigatorUtils.push(context,
              "${WinnerRouter.winnerShareOrderPage}/${winner.shareId}");
        },
        child: Container(
          margin: const EdgeInsets.only(top: 10),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10), color: Colors.white),
          width: double.infinity,
          child: Column(
            children: [
              Stack(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            NavigatorUtils.push(context,
                                "${WinnerRouter.winnerProfilePage}/${winner.shareUserId}/7");
                          },
                          child: ClipOval(
                            //     radius: 5,
                            child: Image.network(
                              winner.headImg != null ? winner.headImg! : "",
                              width: 55,
                              height: 55,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                // 加载失败时显示默认图片
                                return Image.asset(
                                  defaultImageAsset,
                                  width: 55,
                                  height: 55,
                                  fit: BoxFit.cover,
                                );
                              },
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                // 加载中显示默认图片
                                if (loadingProgress == null) return child;
                                return Image.asset(
                                  defaultImageAsset,
                                  width: 55,
                                  height: 55,
                                  fit: BoxFit.cover,
                                );
                              },
                            ),
                          ),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("${winner.petName}"),
                            Gaps.vGap4,
                            Row(
                              children: [
                                Gaps.hGap4,
                                LoadAssetImage(
                                    'games/icon_${generateGameENName(winner.lotteryType!)}',
                                    width: 18.0),
                                Gaps.hGap4,
                                ...generateGameTitles(),
                                Gaps.hGap4,
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: Color(0xFFfd4627),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 2, horizontal: 6),
                                  child: Text(
                                    "起跟${winner.betting}元",
                                    style: TextStyles.textWhite12,
                                  ),
                                )
                              ],
                            ),
                            Gaps.vGap4,
                            Row(
                              children: [
                                winner.minForecastReward !=
                                        winner.maxForecastReward
                                    ? const Text(
                                        "奖金范围:",
                                        style: TextStyle(
                                            color: Color(0xFF999999),
                                            fontSize: Dimens.font_sp12),
                                      )
                                    : const Text(
                                        "中奖金额:",
                                        style: TextStyle(
                                            color: Color(0xFF999999),
                                            fontSize: Dimens.font_sp12),
                                      ),
                                Gaps.hGap4,
                                winner.minForecastReward !=
                                        winner.maxForecastReward
                                    ? Text(
                                        "${winner.minForecastReward} ~ ${winner.maxForecastReward}元",
                                        style: const TextStyle(
                                            color: Color(0xFF999999),
                                            fontSize: Dimens.font_sp12),
                                      )
                                    : Text(
                                        " ${winner.minForecastReward}元",
                                        style: const TextStyle(
                                            color: Color(0xFF999999),
                                            fontSize: Dimens.font_sp12),
                                      ),
                              ],
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                      top: 0,
                      right: 0,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "截止时间",
                            style: TextStyles.textSize12,
                          ),
                          Text(
                            "${getYearMonthDayDateTime(winner.endTime!)}",
                            style: TextStyles.textBoldRed12,
                          ),
                        ],
                      ))
                ],
              ),
              Container(
                padding: EdgeInsets.symmetric(vertical: 10),
                alignment: Alignment.centerLeft,
                child: Text(
                  "${winner.desc}",
                  style: TextStyles.textSize12,
                ),
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      generateGameName(winner.lotteryType!),
                      style: TextStyles.textBold14,
                    ),
                    Column(
                      children: [
                        Text(
                          "自购金额",
                          style: TextStyle(
                              fontSize: Dimens.font_sp12,
                              color: Color(0xFF999999)),
                        ),
                        Text(
                          "${winner.planMoney}",
                          style: TextStyles.textBold14,
                        ),
                      ],
                    ),
                    Container(
                      width: 1,
                      color: Color(0xFFeeeeee),
                      padding: EdgeInsets.symmetric(vertical: 20),
                    ),
                    Column(
                      children: [
                        Text(
                          "跟单人数",
                          style: TextStyle(
                              fontSize: Dimens.font_sp12,
                              color: Color(0xFF999999)),
                        ),
                        Text(
                          "${winner.copyNum}",
                          style: TextStyles.textBold14,
                        ),
                      ],
                    ),
                    MyCustomButton(
                      bgColor: Color(0xFFf16e56),
                      onPressed: null,
                      text: "我要跟单",
                      textColor: Colors.white,
                      fontSize: Dimens.font_sp14,
                      radius: 20,
                      padding:
                          EdgeInsets.symmetric(vertical: 6, horizontal: 18),
                    )
                  ],
                ),
              )
            ],
          ),
        ));
  }
}

class MoreWidget extends StatelessWidget {
  const MoreWidget(this.itemCount, this.hasMore, this.pageSize, {super.key});

  final int itemCount;
  final bool hasMore;
  final int pageSize;

  @override
  Widget build(BuildContext context) {
    final TextStyle style = const TextStyle(color: Color(0x8A000000));
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          if (hasMore) const CupertinoActivityIndicator(),
          if (hasMore) Gaps.hGap5,

          /// 只有一页的时候，就不显示FooterView了
          Text(hasMore ? '正在加载中...' : (itemCount < pageSize ? '' : '没有了呦~'),
              style: style),
        ],
      ),
    );
  }
}
