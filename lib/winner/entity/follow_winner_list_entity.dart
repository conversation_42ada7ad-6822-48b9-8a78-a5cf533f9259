class WinnerFollowListEntity {
  String? status;
  String? message;
  List<FollowWinner>? list;

  WinnerFollowListEntity({this.status, this.message, this.list});

  WinnerFollowListEntity.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['list'] != null) {
      list = <FollowWinner>[];
      json['list'].forEach((v) {
        list!.add(FollowWinner.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.list != null) {
      data['list'] = this.list!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class FollowWinner {
  String? userId;
  String? petName;
  String? headImg;
  String? fansNum;
  String? shareNum;
  String? rate;
  String? zhong;
  Day7? day7;
  String? day15Rate;
  String? day30Rate;
  String? sortRate;
  String? isAttention;
  String? isShare;
  String? attenNum;

  FollowWinner(
      {this.userId,
      this.petName,
      this.headImg,
      this.fansNum,
      this.shareNum,
      this.rate,
      this.zhong,
      this.day7,
      this.day15Rate,
      this.day30Rate,
      this.sortRate,
      this.isAttention,
      this.isShare,
      this.attenNum});

  FollowWinner.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    petName = json['pet_name'];
    headImg = json['head_img'];
    fansNum = json['fans_num'];
    shareNum = json['share_num'];
    rate = json['rate'];
    zhong = json['zhong'];
    day7 = json['day7'] != null ? new Day7.fromJson(json['day7']) : null;
    day15Rate = json['day15_rate'];
    day30Rate = json['day30_rate'];
    sortRate = json['sort_rate'];
    isAttention = json['is_attention'];
    isShare = json['is_share'];
    attenNum = json['atten_num'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_id'] = this.userId;
    data['pet_name'] = this.petName;
    data['head_img'] = this.headImg;
    data['fans_num'] = this.fansNum;
    data['share_num'] = this.shareNum;
    data['rate'] = this.rate;
    data['zhong'] = this.zhong;
    if (this.day7 != null) {
      data['day7'] = this.day7!.toJson();
    }
    data['day15_rate'] = this.day15Rate;
    data['day30_rate'] = this.day30Rate;
    data['sort_rate'] = this.sortRate;
    data['is_attention'] = this.isAttention;
    data['is_share'] = this.isShare;
    data['atten_num'] = this.attenNum;
    return data;
  }
}

class Day7 {
  int? amount;
  double? profit;
  double? profitRate;
  int? total;
  int? hit;
  double? hitRate;
  int? red;
  int? shareHuman;
  int? shareNum;
  int? shareRed;
  double? reward;

  Day7(
      {this.amount,
      this.profit,
      this.profitRate,
      this.total,
      this.hit,
      this.hitRate,
      this.red,
      this.shareHuman,
      this.shareNum,
      this.shareRed,
      this.reward});

  Day7.fromJson(Map<String, dynamic> json) {
    amount = json['amount'];

    if (json['profit'] is int) {
      profit = json['profit'].toDouble();
    } else {
      profit = json['profit'];
    }

    if (json['profit_rate'] is int) {
      profitRate = json['profit_rate'].toDouble();
    } else {
      profitRate = json['profit_rate'];
    }

    if (json['hit_rate'] is int) {
      hitRate = json['hit_rate'].toDouble();
    } else {
      hitRate = json['hit_rate'];
    }

    if (json['reward'] is int) {
      reward = json['reward'].toDouble();
    } else {
      reward = json['reward'];
    }

    if (json['share_human'] is String) {
      shareHuman = int.parse(json['share_human']);
    } else {
      shareHuman = json['share_human'];
    }

    if (json['share_human'] is String) {
      shareHuman = int.parse(json['share_human']);
    } else {
      shareHuman = json['share_human'];
    }

    if (json['share_red'] is String) {
      shareRed = int.parse(json['share_red']);
    } else {
      shareRed = json['share_red'];
    }

    total = json['total'];
    hit = json['hit'];
    red = json['red'];
    if (json['share_num'] is String) {
      shareNum = int.parse(json['share_num']);
    } else {
      shareNum = json['share_num'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['amount'] = this.amount;
    data['profit'] = this.profit;
    data['profit_rate'] = this.profitRate;
    data['total'] = this.total;
    data['hit'] = this.hit;
    data['hit_rate'] = this.hitRate;
    data['red'] = this.red;
    data['share_human'] = this.shareHuman;
    data['share_num'] = this.shareNum;
    data['share_red'] = this.shareRed;
    data['reward'] = this.reward;
    return data;
  }
}
