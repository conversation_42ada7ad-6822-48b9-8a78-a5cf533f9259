import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/util/image_utils.dart';
import 'package:sport_mobile/widgets/search_app_bar.dart';
import 'package:sport_mobile/widgets/state_layout.dart';
import 'package:sport_mobile/winner/entity/search_winner_list_entity.dart';
import 'package:sport_mobile/winner/model/winner_search_model.dart';
import 'package:sport_mobile/winner/widget/winner_list_refresh.dart';
import 'package:sport_mobile/winner/winner_router.dart';

class WinnerSearchPage extends StatefulWidget {
  @override
  _WinnerSearchPageState createState() => _WinnerSearchPageState();
}

class _WinnerSearchPageState extends State<WinnerSearchPage> {
  //String keyword = "";

  List<String> historyKeywords = [];

  int _index = 0;

  final SearchDelegateNotifier searchDelegateNotifier =
      SearchDelegateNotifier();

  @override
  void initState() {
    historyKeywords =
        SpUtil.getStringList(Constant.historyWithKeywords, defValue: [])!;
    searchDelegateNotifier.addListener(() {
      setState(() {});
    });
    super.initState();
  }

  void _search(String val) {
    if (val == "") {
      return;
    }

    historyKeywords =
        SpUtil.getStringList(Constant.historyWithKeywords, defValue: [])!;
    if (historyKeywords.length == 10) {
      historyKeywords.removeLast();
    }

    historyKeywords.insert(0, val);

    //remove duplicate keyword
    historyKeywords = historyKeywords.toSet().toList();

    SpUtil.putStringList(Constant.historyWithKeywords, historyKeywords);

    searchDelegateNotifier.fillup(val);
    ;
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(create: (context) {
      return WinnerSearchModel()..searchHotKeyWords();
    }, child: Consumer<WinnerSearchModel>(builder: (context, model, child) {
      return Scaffold(
        appBar: SearchPageAppBar(
          hintText: "请输入搜索关键词",
          onPressed: (val) {
            _search(val);
            model.refreshData(1, val);
          },
          delegateNotifier: searchDelegateNotifier,
        ),
        body: Container(
          margin: const EdgeInsets.symmetric(horizontal: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              searchDelegateNotifier.keyword.isEmpty
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Gaps.vGap32,
                        const Text(
                          "历史搜索",
                          style: TextStyles.textSize14,
                        ),
                        Gaps.vGap10,
                        GridView.count(
                          crossAxisCount: 5, // 每行显示的列数
                          childAspectRatio: 2.4, // 宽高比例
                          crossAxisSpacing: 20,
                          mainAxisSpacing: 6,
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(), // 禁止滚动
                          children: historyKeywords.map((e) {
                            return KeywordItem(
                              name: "$e",
                              onPressed: () {
                                _search(e);
                                model.refreshData(1, e);
                              },
                              key: Key(
                                "$e${_index++}",
                              ),
                            );
                          }).toList(),
                        ),
                        Gaps.vGap32,
                        Text(
                          "热门搜索${searchDelegateNotifier.keyword}",
                          style: TextStyles.textSize14,
                        ),
                        Gaps.vGap10,
                        Consumer<WinnerSearchModel>(
                            builder: (context, model, child) {
                          return GridView.count(
                            crossAxisCount: 5, // 每行显示的列数
                            childAspectRatio: 2.4, // 宽高比例
                            crossAxisSpacing: 20,
                            mainAxisSpacing: 6,
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            physics:
                                const NeverScrollableScrollPhysics(), // 禁止滚动
                            children: model.hotKeyWords.map((e) {
                              return KeywordItem(
                                name: "$e",
                                onPressed: () {
                                  _search(e);
                                  model.refreshData(1, e);
                                },
                                key: Key(
                                  "$e${_index++}",
                                ),
                              );
                            }).toList(),
                          );
                        }),
                      ],
                    )
                  : Container(),
              searchDelegateNotifier.keyword.isNotEmpty
                  ? Expanded(
                      child: WinnerSearchListRefresh(
                      searchDelegateNotifier: searchDelegateNotifier,
                    ))
                  : Container()
            ],
          ),
        ),
      );
    }));
  }
}

class WinnerSearchListRefresh extends StatefulWidget {
  final SearchDelegateNotifier? searchDelegateNotifier;

  const WinnerSearchListRefresh({this.searchDelegateNotifier, super.key});

  @override
  _WinnerFollowListRefreshState createState() =>
      _WinnerFollowListRefreshState();
}

class _WinnerFollowListRefreshState extends State<WinnerSearchListRefresh> {
  ScrollController _controller = ScrollController();

  int page = 1;

  GlobalKey keyOfHeight = GlobalKey();

  String keyword = '';

  @override
  void initState() {
    super.initState();

    _controller.addListener(() {
      if (_controller.offset < 1000) {}
    });

    WinnerSearchModel model =
        Provider.of<WinnerSearchModel>(context, listen: false);
    widget.searchDelegateNotifier?.addListener(() {
      keyword = widget.searchDelegateNotifier!.keyword;
      if (keyword.isEmpty && model.winnerList.isNotEmpty) {
        model.emptyDataList();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WinnerSearchModel>(builder: (context, model, child) {
      if (model.winnerList.isEmpty && model.isLoading) {
        //加载数据
        return const StateLayout(
          type: StateType.loading,
        );
      }

      if (model.winnerList.isEmpty && model.isLoading == false) {
        //加载完毕，没有数据
        return const StateLayout(
          type: StateType.empty,
        );
      }

      return NotificationListener(
          onNotification: (ScrollNotification note) {
            if (note.metrics.pixels == note.metrics.maxScrollExtent) {
              page = page + 1;
              Provider.of<WinnerSearchModel>(context, listen: false)
                  .loadMore(page, keyword!);
            }

            return true;
          },
          child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10)),
              ),
              child: RefreshIndicator(
                onRefresh: () {
                  page = 1;
                  return model.refreshData(page, keyword!);
                },
                child: Consumer<WinnerSearchModel>(
                  builder: (_, provider, child) {
                    return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: model.winnerList.isEmpty
                            ? const StateLayout(type: StateType.loading)
                            : ListView.builder(
                                controller: _controller,
                                itemCount: model.winnerList.length + 1,
                                itemBuilder: (context, index) {
                                  return index < model.winnerList.length
                                      ? SearchWinnerItem(
                                          model.winnerList[index],
                                          onPressed: () {
                                            NavigatorUtils.push(context,
                                                "${WinnerRouter.winnerProfilePage}/${model.winnerList[index].userId}/7");
                                          },
                                          key: Key("$index"),
                                        )
                                      : MoreWidget(model.winnerList.length,
                                          model.hasMoreData, 10);
                                }));
                  },
                ),
              )));
    });

//  return
  }
}

class SearchWinnerItem extends StatelessWidget {
  final SearchWinner winner;

  final VoidCallback? onPressed;

  final String defaultImageAsset = ImageUtils.getImgPath('mine/avatar_default');

  final VoidCallback? followAction;

  SearchWinnerItem(this.winner, {this.onPressed, this.followAction, super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed ?? onPressed,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ClipOval(
              child: Image.network(
                winner.headImg != null ? winner.headImg! : "",
                width: 45,
                height: 45,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // 加载失败时显示默认图片
                  return Image.asset(
                    defaultImageAsset,
                    width: 45,
                    height: 45,
                    fit: BoxFit.cover,
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  // 加载中显示默认图片
                  if (loadingProgress == null) return child;
                  return Image.asset(
                    defaultImageAsset,
                    width: 45,
                    height: 45,
                    fit: BoxFit.cover,
                  );
                },
              ),
            ),
            Gaps.hGap10,
            Expanded(
                child: Container(
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFe3e3e3),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Gaps.vGap10,
                      Text("${winner.petName}"),
                      Gaps.vGap24,
                    ],
                  ),
                ],
              ),
            ))
          ],
        ),
      ),
    );
  }
}

class KeywordItem extends StatelessWidget {
  final String name;
  final VoidCallback? onPressed;

  const KeywordItem({this.name = '', this.onPressed, super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 60,
        alignment: Alignment.center,
        margin: EdgeInsets.symmetric(horizontal: 4, vertical: 0),
        padding: EdgeInsets.symmetric(horizontal: 2, vertical: 0),
        decoration: BoxDecoration(
            color: Color(0xFFf5f5f5), borderRadius: BorderRadius.circular(6)),
        child: Text(
          "$name",
          style: TextStyle(
              color: Color(0xFF656565),
              fontSize: Dimens.font_sp12,
              overflow: TextOverflow.ellipsis),
        ),
      ),
    );
  }
}
