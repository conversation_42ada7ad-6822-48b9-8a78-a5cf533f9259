import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/util/theme_utils.dart';
import 'package:sport_mobile/widgets/load_image.dart';

/// design/9暂无状态页面/index.html#artboard3
class StateLayout extends StatelessWidget {
  
  const StateLayout({
    super.key,
    required this.type,
    this.hintText
  });
  
  final StateType type;
  final String? hintText;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        if (type == StateType.loading)
          const CupertinoActivityIndicator(radius: 16.0)
        else
          if (type != StateType.empty)
            Opacity(
              opacity: context.isDark ? 0.5 : 1,
              child: LoadAssetImage(
                'state/${type.img}',
                width: 120,
              ),
            ),
        const SizedBox(width: double.infinity, height: Dimens.gap_dp16,),
        Text(
          hintText ?? type.hintText,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(fontSize: Dimens.font_sp14),
        ),
        Gaps.vGap50,
      ],
    );
  }
}

enum StateType {

  /// 无网络
  network,
  /// 加载中
  loading,
  /// 空
  empty
}

extension StateTypeExtension on StateType {
  String get img => <String>[
    'zwwl', 'zwxx', 
     '', '']
  [index];
  
  String get hintText => <String>[
    '无网络连接', '加载中', 
     '没有数据', ''
  ][index];
}
