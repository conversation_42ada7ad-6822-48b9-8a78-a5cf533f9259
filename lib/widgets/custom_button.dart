import 'package:flutter/material.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/util/theme_utils.dart';
import 'package:sport_mobile/widgets/load_image.dart';

/// 默认字号18，白字蓝底，高度48
class CustomButton extends StatelessWidget {
  const CustomButton({
    super.key,
    this.text = '',
    this.fontSize = Dimens.font_sp12,
    this.textColor,
    this.disabledTextColor,
    this.bgColor = const Color(0xFFfe8320),
    this.disabledBackgroundColor,
    this.minHeight = 30.0,
    //this.minWidth = double.infinity,
    this.minWidth = 30,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0),
    this.radius = 10.0,
    this.side = BorderSide.none,
    this.gradient = false,
    this.iconImg = "",
    this.iconData,
    required this.onPressed,
  });

  final String text;
  final double fontSize;
  final Color? textColor;
  final Color? disabledTextColor;
  final Color? bgColor;
  final Color? disabledBackgroundColor;
  final double? minHeight;
  final double? minWidth;
  final EdgeInsetsGeometry padding;
  final double radius;
  final BorderSide side;
  final bool gradient;
  final String iconImg;
  final IconData? iconData;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    final bool isDark = context.isDark;
    return TextButton(
      onPressed: onPressed,
      style: ButtonStyle(
        foregroundColor: WidgetStateProperty.resolveWith(
          (states) {
            return textColor ?? Colors.white;
          },
        ),
        // 背景颜色
        backgroundColor: WidgetStateProperty.resolveWith((states) {
          return bgColor ?? bgColor;
        }),
       
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
       
        padding: WidgetStateProperty.all<EdgeInsetsGeometry>(
            gradient == false ? padding : const EdgeInsets.all(0)),
        shape: WidgetStateProperty.all<OutlinedBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radius),
          ),
        ),
        side: WidgetStateProperty.all<BorderSide>(side),
      ),

      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (iconImg != "") 
            LoadAssetImage(
              iconImg,
              width: 10,
              height: 10,
            ),

          if(iconData != null)
            Icon(iconData),
          
          Gaps.hGap4,
          Text(
            text,
            style: TextStyle(fontSize: fontSize),
          ),
        ],
      ),
    );
  }
}
