import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/util/image_utils.dart';
import 'package:sport_mobile/util/theme_utils.dart';
import 'package:sport_mobile/util/version_utils.dart';
import 'package:sport_mobile/widgets/my_button.dart';

Future<void> checkAndShowDialog(BuildContext context) async {
  String lastShowDialognDate =
      SpUtil.getString(Constant.lastShowDialognDate) ?? '';

  String todayDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
  if (lastShowDialognDate != todayDate) {
    showDialog<void>(context: context, builder: (_) => const UpdateDialog());
    SpUtil.putString(Constant.lastShowDialognDate, todayDate);
  }
}

class UpdateDialog extends StatefulWidget {
  const UpdateDialog({super.key});

  @override
  _UpdateDialogState createState() => _UpdateDialogState();
}

class _UpdateDialogState extends State<UpdateDialog> {
  final CancelToken _cancelToken = CancelToken();
  bool _isDownload = false;
  double _value = 0;

  @override
  void dispose() {
    if (!_cancelToken.isCancelled && _value != 1) {
      _cancelToken.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final Color primaryColor = Theme.of(context).primaryColor;
    return PopScope(
      canPop: true, //强制升级
      child: Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: Colors.transparent,
          body: Center(
            child: Stack(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(vertical: 20, horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Container(
                        height: 120.0,
                        width: 280.0,
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(8.0),
                              topRight: Radius.circular(8.0)),
                          image: DecorationImage(
                            image: ImageUtils.getAssetImage('update_head',
                                format: ImageFormat.jpg),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      Container(
                        width: 280.0,
                        decoration: BoxDecoration(
                            color: context.dialogBackgroundColor,
                            borderRadius: const BorderRadius.only(
                                bottomLeft: Radius.circular(8.0),
                                bottomRight: Radius.circular(8.0))),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15.0, vertical: 15.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            const Text('请点击【立即更新】按钮更新版本，或前往【我的】页面进行更新',
                                style: TextStyles.textSize16),
                            Gaps.vGap15,
                            if (_isDownload)
                              LinearProgressIndicator(
                                backgroundColor: Colours.line,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(primaryColor),
                                value: _value,
                              )
                            else
                              _buildButton(context),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                    top: 0,
                    right: 0,
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pop(context, true);
                      },
                      child: Container(
                        width: 50,
                        child: Icon(
                          Icons.cancel_rounded,
                          color: Colors.red,
                          size: 35,
                        ),
                      ),
                    ))
              ],
            ),
          )),
    );
  }

  Widget _buildButton(BuildContext context) {
    final Color primaryColor = Theme.of(context).primaryColor;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        SizedBox(
          width: 110.0,
          height: 36.0,
          child: MyButton(
            text: '立即更新',
            fontSize: Dimens.font_sp16,
            onPressed: () {
              if (defaultTargetPlatform == TargetPlatform.iOS) {
                String? iosDownLoadURL =
                    SpUtil.getString(Constant.iosDownLoadURL);

                NavigatorUtils.goBack(context);
                VersionUtils.jumpURL(iosDownLoadURL!);
              } else {
                String? androidLoadURL =
                    SpUtil.getString(Constant.androidDownLoadURL);

                NavigatorUtils.goBack(context);
                VersionUtils.jumpURL(androidLoadURL!, openDefaultBrowser: true);
              }
            },
            textColor: Colors.white,
            backgroundColor: primaryColor,
            disabledTextColor: Colors.white,
            disabledBackgroundColor: Colours.text_gray_c,
            radius: 18.0,
          ),
        )
      ],
    );
  }

  Future<bool> _checkPermission() async {
    final plugin = DeviceInfoPlugin();
    final android = await plugin.androidInfo;

    final storageStatus = android.version.sdkInt < 33
        ? await Permission.storage.request()
        : PermissionStatus.granted;

    //final storageStatus = await Permission.storage.request();

    if (storageStatus == PermissionStatus.granted) {
      return true;
    }

    if (storageStatus == PermissionStatus.denied) {
      return false;
    }
    if (storageStatus == PermissionStatus.permanentlyDenied) {
      openAppSettings();
      return false;
    }

    return false;
  }
}
