import 'package:flutter/material.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/widgets/load_image.dart';

class ClickItem extends StatelessWidget {

  const ClickItem({
    super.key,
    this.onTap,
    required this.title,
    this.content = '',
    this.textAlign = TextAlign.start,
    this.maxLines = 1,
    this.icon = '',
    this.minHeight = 80,

  });

  final GestureTapCallback? onTap;
  final String title;
  final String content;
  final String icon;
  final TextAlign textAlign;
  final int maxLines;

  final double minHeight;

  @override
  Widget build(BuildContext context) {
    Widget child = Row(
      //为了数字类文字居中
      crossAxisAlignment: maxLines == 1 ? CrossAxisAlignment.center : CrossAxisAlignment.start,
      children: <Widget>[
        if(icon != "")
          LoadAssetImage(icon,width: 18,),
        Gaps.hGap16,
        Text(title,style: TextStyles.textSize16,),
        const Spacer(),
        Gaps.hGap16,
        Expanded(
          flex: 4,
          child: Text(
            content,
            maxLines: maxLines,
            textAlign: maxLines == 1 ? TextAlign.right : textAlign,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(fontSize: Dimens.font_sp14),
          ),
        ),
        Gaps.hGap8,
        Opacity(
          opacity: onTap == null ? 0 : 1,
          child: Padding(
            padding: EdgeInsets.only(top: maxLines == 1 ? 0.0 : 2.0),
            child: Images.arrowRight,
          ),
        )
      ],
    );
    
    /// 分隔线
    child = Container(
    //  margin: const EdgeInsets.only(left: 5.0),
      padding: const EdgeInsets.fromLTRB(5, 15.0, 15.0, 15.0),
      constraints:  BoxConstraints(
        minHeight: minHeight,
      ),
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border(
          bottom: Divider.createBorderSide(context, width: 0.6),
        ),
      ),
      child: child,
    );
    
    return InkWell(
      onTap: onTap,
      child: child,
    );
  }
}
