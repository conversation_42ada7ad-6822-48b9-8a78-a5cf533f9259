import 'dart:io';

import 'package:flutter/material.dart';

class AvatarWidget extends StatelessWidget {
  final String imageUrl;
  final String defaultImageAsset;

  final File? file;

  final bool loadNetworkImage;

  final double? width;
  final double? height;

  final bool useOval;

  const AvatarWidget({
    super.key,
    required this.imageUrl,
    required this.defaultImageAsset,
    this.width = 80,
    this.height = 80,
    this.loadNetworkImage = true,
    this.file,
    this.useOval = true,
  });

  @override
  Widget build(BuildContext context) {
    if (useOval == true) {
      return CircleAvatar(
        radius: 40,
        backgroundColor: Colors.transparent,
        child: ClipOval(
          // 使用ClipOval裁剪成圆形
          child: loadNetworkImage
              ? Image.network(
                  imageUrl,
                  width: width ?? width,
                  height: height ?? height,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // 加载失败时显示默认图片
                    return Image.asset(
                      defaultImageAsset,
                      width: width ?? width,
                      height: height ?? height,
                      fit: BoxFit.cover,
                    );
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    // 加载中显示默认图片
                    if (loadingProgress == null) return child;
                    return Image.asset(
                      defaultImageAsset,
                      width: width ?? width,
                      height: height ?? height,
                      fit: BoxFit.cover,
                    );
                  },
                )
              : file != null
                  ? Image.file(
                      file!,
                      width: width ?? width,
                      height: height ?? height,
                    )
                  : Image.asset(
                      imageUrl!,
                      width: width ?? width,
                      height: height ?? height,
                    ),
        ),
      );
    } else {
      return ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: loadNetworkImage
            ? Image.network(
                imageUrl,
                width: width ?? width,
                height: height ?? height,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // 加载失败时显示默认图片
                  return Image.asset(
                    defaultImageAsset,
                    width: width ?? width,
                    height: height ?? height,
                    fit: BoxFit.cover,
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  // 加载中显示默认图片
                  if (loadingProgress == null) return child;
                  return Image.asset(
                    defaultImageAsset,
                    width: width ?? width,
                    height: height ?? height,
                    fit: BoxFit.cover,
                  );
                },
              )
            : file != null
                ? Image.file(
                    file!,
                    width: width ?? width,
                    height: height ?? height,
                  )
                : Image.asset(
                    imageUrl!,
                    width: width ?? width,
                    height: height ?? height,
                  ),
      );
    }
  }
}
