import 'package:flutter/material.dart';
import 'package:sport_mobile/res/styles.dart';

/// WebSocket状态
enum NetworkStatus {
  networkConnected, // 已连接
  networkFailed, // 失败
  networkTimeout, //超时
}



class NetworkFailedWidget extends StatelessWidget{


 final VoidCallback? onPressed;

  NetworkFailedWidget({this.onPressed, super.key});

  @override
  Widget build(BuildContext context) {
        return Center(
                  child: Container(
                  height: 300,
                  width: 300,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        "获取数据超时",
                        style: TextStyle(color: Colors.black),
                      ),
                      SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: onPressed,
                        child: const Text(
                          "重新加载",
                          style: TextStyles.textSize14,
                        ),
                      ),
                    ],
                  ),
                ));

  }




}