class FTStateStore {
  // 胜平负
  List<bool> spf = List.filled(3, false);
  // 让球胜平负
  List<bool> rqspf = List.filled(3, false);
  // 进球数
  List<bool> jqs = List.filled(8, false);
  // 猜比分
  List<bool> cbf = List.filled(31, false);
  // 半全场
  List<bool> bqc = List.filled(9, false);


  FTStateStore();


  bool hasSelect() {
    List<bool> array = [...spf, ...rqspf, ...jqs, ...cbf, ...bqc];
    return array.any((item) => item == true);
  }

  bool hasOnlySelectSPF() {
    bool spfSelected = hasSelectSPF();

    if (spfSelected) {
      List<bool> array = [...rqspf, ...jqs, ...cbf, ...bqc];
      return array.every((item) => !item);
    }

    return spfSelected;
  }

  bool hasOnlySelectRQSPF() {
    bool rqspfSelected = hasSelectRQSPF();

    if (rqspfSelected) {
      List<bool> array = [...spf, ...jqs, ...cbf, ...bqc];
      return array.every((item) => !item);
    }

    return rqspfSelected;
  }

  bool hasSelectSPF() {
    return spf.any((item) => item);
  }

  bool hasSelectRQSPF() {
    return rqspf.any((item) => item);
  }

  bool hasSelectJQS() {
    return jqs.any((item) => item);
  }

  bool hasSelectCBF() {
    return cbf.any((item) => item);
  }

  bool hasSelectBQC() {
    return bqc.any((item) => item);
  }

  int getAllSelectCount() {
    List<bool> array = [...spf, ...rqspf, ...jqs, ...cbf, ...bqc];
    return array.where((item) => item).length;
  }

  int getMoreSelectCount() {
    int count = 0;

    count += jqs.where((item) => item).length;
    count += cbf.where((item) => item).length;
    count += bqc.where((item) => item).length;

    return count;
  }


  
  FTStateStore.fromJson(FTStateStore json) {
    spf = json.spf;
    rqspf = json.rqspf;
    jqs = json.jqs;
    cbf = json.cbf;
    bqc = json.bqc;
  }



  FTStateStore toJson() {
    FTStateStore _ftStateStore =  FTStateStore();
    _ftStateStore.spf = spf.toList();
    _ftStateStore.rqspf = rqspf.toList();
    _ftStateStore.jqs = jqs.toList();
    _ftStateStore.cbf = cbf.toList();
    _ftStateStore.bqc = bqc.toList();
    return _ftStateStore;
  }


}


