import 'dart:convert';

class FT149Entitiy {
  String? status;
  String? message;
  Map<String, MapMatch>? matchlist;


  FT149Entitiy({this.status, this.message, this.matchlist});

  FT149Entitiy.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];

    matchlist = {};
    if (json['list'] != null) {
      json['list'].forEach((key, item) {
        MapMatch mapMatch = MapMatch.fromJson(key, item);
        matchlist?['$key'] = mapMatch;
      });
    }
  }
}

class MapMatch {
  String? date;
  late List<Match> listMatch;

  MapMatch(this.date, this.listMatch);

  MapMatch.fromJson(ymd, json) {
    

    date = ymd;

    listMatch = [];
    json.forEach((match) {
      Match tempMatch = Match.fromJson(match);
      listMatch?.add(tempMatch);
    });
  }

  get first => null;
}




class SelectedMatch {
  int? id;
  String? boutIndex;

  int? matchNum;

  Map<String,bool>?  matchActiveStatus;


  SelectedMatch(
      {this.id,
      this.boutIndex,
      this.matchNum,
      this.matchActiveStatus,
      
      });

  SelectedMatch.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    boutIndex = json['bout_index'];
    matchNum = json['matchNum'];
    matchActiveStatus = json['matchActiveStatus'];

  }
}


class Match {
  int? id;
  String? boutIndex;
  String? homeTeam;
  String? matchName;
  String? awaryTeam;
  int? matchNum;
  String? lotterySaleBegintime;
  String? lotterySaleEndtime;
  String? lotteryDrawTime;
  String? startTime;

  Match(
      {this.id,
      this.boutIndex,
      this.homeTeam,
      this.matchName,
      this.awaryTeam,
      this.matchNum,
      this.lotterySaleBegintime,
      this.lotterySaleEndtime,
      this.lotteryDrawTime,
      this.startTime});

  Match.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    boutIndex = json['bout_index'];
    homeTeam = json['home_team'];
    matchName = json['match_name'];
    awaryTeam = json['awary_team'];
    matchNum = json['matchNum'];
    lotterySaleBegintime = json['lotterySaleBegintime'];
    lotterySaleEndtime = json['lotterySaleEndtime'];
    lotteryDrawTime = json['lotteryDrawTime'];
    startTime = json['startTime'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = this.id;
    data['bout_index'] = this.boutIndex;
    data['home_team'] = this.homeTeam;
    data['match_name'] = this.matchName;
    data['awary_team'] = this.awaryTeam;
    data['matchNum'] = this.matchNum;
    data['lotterySaleBegintime'] = this.lotterySaleBegintime;
    data['lotterySaleEndtime'] = this.lotterySaleEndtime;
    data['lotteryDrawTime'] = this.lotteryDrawTime;
    data['startTime'] = this.startTime;
    return data;
  }
}
