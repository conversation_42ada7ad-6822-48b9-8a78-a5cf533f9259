class GameCheckoutEntity {
  String? s;
  String? version;
  String? priceRadio;
  String? shareMoney;
  String? shareDesc;
  String? isSp;
  String? isSelect;
  String? minMultiple;
  String? rateLimit;
  String? lotteryType;
  String? term;
  String? codes;
  String? money;
  String? multiple;
  String? addtional;
  String? touzhuStatistics;
  String? touzhuStyle;
  String? maxForecastReward;
  String? minForecastReward;
  String? sp;
  String? minSp;
  String? maxSp;
  String? b;
  String? oneMoney;
  String? isOptimization;
  String? couponId;
  String? otherFrom;
  String? necessary;
  String? jjyh;
  String? token;
  String? channel;
  String? status;
  String? balance;
  int? orderId;
  bool? shareStatus;
  String? shareMsg;

  String? message;

  GameCheckoutEntity(
      {this.s,
      this.version,
      this.priceRadio,
      this.shareMoney,
      this.shareDesc,
      this.isSp,
      this.isSelect,
      this.minMultiple,
      this.rateLimit,
      this.lotteryType,
      this.term,
      this.codes,
      this.money,
      this.multiple,
      this.addtional,
      this.touzhuStatistics,
      this.touzhuStyle,
      this.maxForecastReward,
      this.minForecastReward,
      this.sp,
      this.minSp,
      this.maxSp,
      this.b,
      this.oneMoney,
      this.isOptimization,
      this.couponId,
      this.otherFrom,
      this.necessary,
      this.jjyh,
      this.token,
      this.channel,
      this.status,
      this.balance,
      this.orderId,
      this.shareStatus,
      this.shareMsg,
      this.message
      });

  GameCheckoutEntity.fromJson(Map<String, dynamic> json) {
    s = json['s'];
    version = json['version'];
    priceRadio = json['price_radio'];
    shareMoney = json['share_money'];
    shareDesc = json['share_desc'];
    isSp = json['is_sp'];
    isSelect = json['is_select'];
    minMultiple = json['min_multiple'];
    rateLimit = json['rate_limit'];
    lotteryType = json['lotteryType'];
    term = json['term'];
    codes = json['codes'];
    money = json['money'];
    multiple = json['multiple'];
    addtional = json['addtional'];
    touzhuStatistics = json['touzhu_statistics'];
    touzhuStyle = json['touzhu_style'];
    maxForecastReward = json['max_forecast_reward'];
    minForecastReward = json['min_forecast_reward'];
    sp = json['sp'];
    minSp = json['min_sp'];
    maxSp = json['max_sp'];
    b = json['b'];
    oneMoney = json['oneMoney'];
    isOptimization = json['is_optimization'];
    couponId = json['coupon_id'];
    otherFrom = json['other_from'];
    necessary = json['necessary'];
    jjyh = json['jjyh'];
    token = json['token'];
    channel = json['channel'];
    status = json['status'];
    message = json['message'];
    balance = json['balance'];
    orderId = json['order_id'];
    shareStatus = json['share_status'];
    shareMsg = json['share_msg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['s'] = this.s;
    data['version'] = this.version;
    data['price_radio'] = this.priceRadio;
    data['share_money'] = this.shareMoney;
    data['share_desc'] = this.shareDesc;
    data['is_sp'] = this.isSp;
    data['is_select'] = this.isSelect;
    data['min_multiple'] = this.minMultiple;
    data['rate_limit'] = this.rateLimit;
    data['lotteryType'] = this.lotteryType;
    data['term'] = this.term;
    data['codes'] = this.codes;
    data['money'] = this.money;
    data['multiple'] = this.multiple;
    data['addtional'] = this.addtional;
    data['touzhu_statistics'] = this.touzhuStatistics;
    data['touzhu_style'] = this.touzhuStyle;
    data['max_forecast_reward'] = this.maxForecastReward;
    data['min_forecast_reward'] = this.minForecastReward;
    data['sp'] = this.sp;
    data['min_sp'] = this.minSp;
    data['max_sp'] = this.maxSp;
    data['b'] = this.b;
    data['oneMoney'] = this.oneMoney;
    data['is_optimization'] = this.isOptimization;
    data['coupon_id'] = this.couponId;
    data['other_from'] = this.otherFrom;
    data['necessary'] = this.necessary;
    data['jjyh'] = this.jjyh;
    data['token'] = this.token;
    data['channel'] = this.channel;
    data['status'] = this.status;
    data['balance'] = this.balance;
    data['order_id'] = this.orderId;
    data['share_status'] = this.shareStatus;
    data['share_msg'] = this.shareMsg;
    return data;
  }
}
