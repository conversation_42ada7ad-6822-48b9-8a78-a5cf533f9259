import 'package:intl/intl.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/game/data/basketball_state.dart';
import 'package:sport_mobile/game/data/bet_beishu_data.dart';
import 'package:sport_mobile/game/data/bet_commit.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/data/odds_data.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/util/date_utils.dart';

class JCLQEntity {
  // 类型
  int type = MatchDataType.Basketball;

  String? boutIndex;
  String? weak;
  String? league;
  String? homeTeam;
  String? awayTeam;
  double? concede;
  String? matchTime;
  String? stopSaleTime;
  List<String> state = [];
  String? odds;
  double? dxfNum;

  String? homeAvatar;
  String? awayAvatar;
  String? matchShortTime;
  String? stopSaleSimpleTime;
  String? stopSaleShortTime;
  String? matchDay;
  BasketballOddsData? oddsData;
  BasketballState? basketballState;
  BasketballBeisuData? beisuData;
  int? stopSaleStatus;

  // 最小投注sp
  double? minSP;
  // 最小投注sp的倍数
  int minSPBeisu = 0;
  // 最大投注sp
  List? maxSpList;
  List? maxSPBeisuList;

  // 倍数
  int? beisu = 1;

  // 胆
  bool courage = false;
  // spf对应的最大sp
  Map<String, BetCommitState>? maxSpMap;

  JCLQEntity(
      {this.boutIndex,
      this.weak,
      this.league,
      this.homeTeam,
      this.awayTeam,
      this.concede,
      this.matchTime,
      this.stopSaleTime,
      required this.state,
      this.odds,
      this.homeAvatar,
      this.awayAvatar});

  JCLQEntity.fromJson(List<dynamic> match) {
    boutIndex = match[0];
    weak = match[1];
    league = match[2];
    homeTeam = match[3];
    awayTeam = match[4];
    // matchTime = match[5].replaceAll(RegExp(r'-'), '/').replaceAll(RegExp(r'\.0$'), '');

    matchTime = match[5].replaceAll('.0', '');

    stopSaleTime = match[6].replaceAll('.0', '');

    // stopSaleSimpleTime = match[6].replaceAll(RegExp(r'-'), '/').replaceAll(RegExp(r'\.0$'), '');
    //state = match[7];

    if (match[7] != null && match[7].runtimeType == String) {
      String _stateStr = match[7];
      state = _stateStr.split('').map((e) {
        String _t = e.toString();
        return _t;
      }).toList();
    } else {
      state = [];
    }

    odds = match[15];

    if (match.length == 18) {
      homeAvatar = match[16];
      awayAvatar = match[17];
    }

    DateTime matchDateTime = DateTime.parse(matchTime!);

    //'2023/08/28 21:58:00'
    //"2023/08/29 15:30:00"

    DateTime stopSaleDateTime = DateTime.parse(stopSaleTime!);


    var isRoutine = SpUtil.getInt(Constant.isRoutine, defValue: 0);
    var isTimeout = SpUtil.getInt(Constant.isTimeout, defValue: 0);


   // stopSaleShortTime = formatDate(stopSaleDateTime, 'HH:mm');
    matchShortTime = formatDate(matchDateTime, 'MM月dd日 HH:mm');
       bool bSpareTime = DateFormatUtils.compareTime(
        matchTime!, stopSaleTime!, isTimeout!, isRoutine!);
    if (bSpareTime) {
      stopSaleStatus = 1;
      stopSaleShortTime = '超时下单';
    } else {
      stopSaleStatus = 0;
      stopSaleShortTime = "${DateFormat('HH:mm').format(stopSaleDateTime)}截止";
    }

    String year = boutIndex!.substring(0, 2);
    String month = boutIndex!.substring(2, 4);
    String day = boutIndex!.substring(4, 6);

    matchDay = '20$year-$month-$day（${weak!.substring(0, 2)}）';

    oddsData = BasketballOddsData.parser(odds!);

    concede = oddsData!.concede;

    dxfNum = oddsData!.dxfNum;

    basketballState = BasketballState();

    beisuData = BasketballBeisuData();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    data['type'] = MatchDataType.Basketball;
    data['boutIndex'] = boutIndex;
    data['weak'] = weak;
    data['league'] = league;
    data['homeTeam'] = homeTeam;
    data['awayTeam'] = awayTeam;
    data['concede'] = concede;
    data['matchTime'] = matchTime;
    data['stopSaleTime'] = stopSaleTime;
    data['state'] = state;
    data['odds'] = odds;

    data['homeAvatar'] = homeAvatar != "" ? homeAvatar : "";
    data['awayAvatar'] = awayAvatar != "" ? awayAvatar : "";
    data['matchShortTime'] = matchShortTime;
    data['stopSaleSimpleTime'] = stopSaleSimpleTime;
    data['stopSaleShortTime'] = stopSaleShortTime;
    data['matchDay'] = matchDay;
    data['oddsData'] = oddsData;
    data['ftStateStore'] = BasketballState.fromJson(basketballState!.toJson());
    data['beisuData'] = beisuData;
    data['minSP'] = minSP;
    data['minSPBeisu'] = minSPBeisu;
    data['maxSpList'] = maxSpList;
    data['maxSPBeisuList'] = maxSPBeisuList;
    data['beisu'] = beisu;
    data['courage'] = courage;
    data['maxSpMap'] = maxSpMap;

    return data;
  }

  JCLQEntity.fromObject(JCLQEntity _match) {
    type = _match.type;
    boutIndex = _match.boutIndex;
    weak = _match.weak;
    league = _match.league;
    homeTeam = _match.homeTeam;
    awayTeam = _match.awayTeam;
    concede = _match.concede;
    matchTime = _match.matchTime;
    stopSaleTime = _match.stopSaleTime;
    state = _match.state;
    odds = _match.odds;
    dxfNum = _match.dxfNum;
    homeAvatar = _match.homeAvatar;
    awayAvatar = _match.awayAvatar;
    matchShortTime = _match.matchShortTime;
    stopSaleSimpleTime = _match.stopSaleSimpleTime;
    stopSaleShortTime = _match.stopSaleShortTime;
    matchDay = _match.matchDay;
    oddsData = _match.oddsData;
    basketballState = _match.basketballState;
    beisuData = _match.beisuData;
    minSP = _match.minSP;
    minSPBeisu = _match.minSPBeisu;
    maxSpList = _match.maxSpList;
    maxSPBeisuList = _match.maxSPBeisuList;
    beisu = _match.beisu;
    courage = _match.courage;
    maxSpMap = _match.maxSpMap;
  }

  JCLQEntity toObject() {
    JCLQEntity _JCLQEntity = JCLQEntity(state: []);
    _JCLQEntity.type = type;
    _JCLQEntity.boutIndex = boutIndex;
    _JCLQEntity.weak = weak;
    _JCLQEntity.league = league;
    _JCLQEntity.homeTeam = homeTeam;
    _JCLQEntity.awayTeam = awayTeam;
    _JCLQEntity.concede = concede;
    _JCLQEntity.matchTime = matchTime;
    _JCLQEntity.stopSaleTime = stopSaleTime;
    _JCLQEntity.state = state;
    _JCLQEntity.odds = odds;
    _JCLQEntity.dxfNum = dxfNum;

    _JCLQEntity.homeAvatar = homeAvatar != "" ? homeAvatar : "";
    _JCLQEntity.awayAvatar = awayAvatar != "" ? awayAvatar : "";
    _JCLQEntity.matchShortTime = matchShortTime;
    _JCLQEntity.stopSaleSimpleTime = stopSaleSimpleTime;
    _JCLQEntity.stopSaleShortTime = stopSaleShortTime;
    _JCLQEntity.matchDay = matchDay;

    if (oddsData != null) {
      _JCLQEntity.oddsData =
          BasketballOddsData.fromObject(oddsData!.toObject());
    } else {
      _JCLQEntity.oddsData = BasketballOddsData();
    }

    _JCLQEntity.basketballState =
        BasketballState.fromJson(basketballState!.toJson());

    if (beisuData != null) {
      _JCLQEntity.beisuData =
          BasketballBeisuData.fromObject(beisuData!.toObject());
    } else {
      _JCLQEntity.beisuData = BasketballBeisuData();
    }

    _JCLQEntity.minSP = minSP;
    _JCLQEntity.minSPBeisu = minSPBeisu;
    _JCLQEntity.maxSpList = maxSpList;
    _JCLQEntity.maxSPBeisuList = maxSPBeisuList;
    _JCLQEntity.beisu = beisu;
    _JCLQEntity.courage = courage;

    Map<String, BetCommitState> cloneMaxSpMap = {};

    if (maxSpMap != null) {
      maxSpMap!.forEach((key, BetCommitState _betCommitState) {
        cloneMaxSpMap[key] = BetCommitState.fromJson(_betCommitState.toJson());
      });
      _JCLQEntity.maxSpMap = cloneMaxSpMap;
    }

    return _JCLQEntity;
  }

  String formatDate(DateTime dateTime, String format) {
    return DateFormat(format).format(dateTime);
  }

  void resetCheckState() {
    basketballState = BasketballState();
  }

  bool isZero(int index) {
    return state[index] == '0';
  }

  bool canSF(bool single) {
    int index = 0;
    return isZero(index) ||
        (single ? state[index] == '1' : state[index] == '2');
  }

  bool canRFSF(bool single) {
    int index = 1;
    return isZero(index) ||
        (single ? state[index] == '1' : state[index] == '2');
  }

  bool canDXF(bool single) {
    int index = 2;
    return isZero(index) ||
        (single ? state[index] == '1' : state[index] == '2');
  }

  bool canSFC(bool single) {
    int index = 3;
    return isZero(index) ||
        (single ? state[index] == '1' : state[index] == '2');
  }

  bool canSFSingle() {
    return state[0] == '0' || state[0] == '1';
  }

  bool canRFSFSingle() {
    return state[1] == '0' || state[1] == '1';
  }

  // 足球单关-混投
  bool canSingle() {
    return canSFSingle() || canRFSFSingle();
  }

  canSingleChuan() {
    if (canSingle()) {
      if (canSFSingle() && canRFSFSingle()) {
        // 2个选项都是单关,不处理
        return false;
      } else if (canSFSingle()) {
        return basketballState!.hasOnlySelectSF();
      } else if (canRFSFSingle()) {
        return basketballState!.hasOnlySelectRFSF();
      } else {
        return false;
      }
    } else {
      return false;
    }
  }
}
