class GamePrivilegesEntity {
  int? regular;
  int? startTime;
  int? endTime;
  String? status;
  String? lsbalance;
  double? lotterybalance;
  String? termNo;
  double? mosaicGold;

  double? totalBalance;

  int? isPic;

  GamePrivilegesEntity(
      {this.regular,
      this.startTime,
      this.endTime,
      this.status,
      this.lsbalance,
      this.lotterybalance,
      this.termNo,
      this.mosaicGold,
      this.isPic});

  GamePrivilegesEntity.fromJson(Map<String, dynamic> json) {
    regular = json['regular'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    status = json['status'];
    lsbalance = json['lsbalance'];
    lotterybalance = json['lotterybalance'] != null
        ? double.parse(json['lotterybalance'])
        : 0;
    termNo = json['termNo'];
    mosaicGold =
        json['mosaic_gold'] != null ? double.parse(json['mosaic_gold']) : 0;
    totalBalance = lotterybalance! + mosaicGold!;
    if (json['is_pic'] != null) {
      isPic = json['is_pic'];
    } else {
      isPic = 0;
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['regular'] = this.regular;
    data['start_time'] = this.startTime;
    data['end_time'] = this.endTime;
    data['status'] = this.status;
    data['lsbalance'] = this.lsbalance;
    data['lotterybalance'] = this.lotterybalance;
    data['termNo'] = this.termNo;
    data['mosaic_gold'] = this.mosaicGold;
    return data;
  }
}
