import 'package:flutter/material.dart';

import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/drawing/page/drawing_page.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/page/model/checkout_model.dart';
import 'package:sport_mobile/game/page/model/lottery_ssq_model.dart';
import 'package:sport_mobile/game/page/widgets/back_dialog.dart';
import 'package:sport_mobile/game/routers/game_router.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/util/toast_utils.dart';
import 'package:sport_mobile/widgets/my_app_bar.dart';

class SSQConfirmPage extends StatefulWidget {
  const SSQConfirmPage({super.key});

  @override
  _SSQConfirmPageState createState() {
    return _SSQConfirmPageState();
  }
}

class _SSQConfirmPageState extends State<SSQConfirmPage> {
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    // 添加监听器
    _controller.addListener(() {
      int? _times = 0;
      String text = _controller.text;
      if (text.isNotEmpty) {
        _times = int.tryParse(text);
        if (_times == null) {
          _controller.text = '1';
          _controller.selection = TextSelection.fromPosition(
              TextPosition(offset: _controller.text.length));
        }
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  List<Widget> buildDigitalItem(List<String> nums, Color itemColor) {
    List<Widget> numbers = [];
    for (int i = 0; i < nums.length; i++) {
      var _tmpWidget;
      _tmpWidget = LotteryDigtalItem(
        name: nums[i],
        itemColor: itemColor,
      );
      numbers.add(_tmpWidget);
    }
    return numbers;
  }

  @override
  Widget build(BuildContext context) {
    void _showExitDialog() {
      LotterySSQModel provider =
          Provider.of<LotterySSQModel>(context, listen: false);

      showDialog<void>(
          context: context,
          builder: (_) => BackDialog(
                onBackPressed: () {
                  provider.flushExit();
                  Navigator.of(context).pop(true);
                },
          ));
    }

    return Consumer<LotterySSQModel>(builder: (context, model, child) {
      return Scaffold(
          backgroundColor: const Color(0xFFf2f2f2),
          appBar: MyAppBar(
          centerTitle: "双色球",
          isShowDialog: true,
        
          onBackPressed: () {
            _showExitDialog();
          },
        ),
          body: Column(
            children: [
              Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFF4268cb), Color(0xFF8c7eec)], // 渐变色数组
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                  ),
                  child: Container(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(10),
                                topRight: Radius.circular(10)),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                "【第${model.ssqInfo.boutIndex}期】",
                                style: TextStyles.textSize12,
                              ),
                              Text(
                                "截止时间:${model.ssqInfo.stopTime}",
                                style: TextStyles.textSize12,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  )),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 30, vertical: 10),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(
                      color: Color(0xFFe3e3e3),
                      width: 0.5,
                    ),
                    bottom: BorderSide(
                      color: Color(0xFFe3e3e3),
                      width: 0.5,
                    ),
                  ),
                ),
                width: double.infinity,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ...model.confirmSelectdList.map((item) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              child: Container(
                            padding: EdgeInsets.only(bottom: 25),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: Text("${item.pos}"),
                                ),
                                Expanded(
                                  flex: 8,

                                  ///child: Text("${item.nums.join(",")}"),
                                  child: Row(
                                    children: [
                                      ...buildDigitalItem(
                                          item.nums,
                                          item.pos == '红球区'
                                              ? Color(0xFFfa4e4e)
                                              : Color(0xFF3480ff))
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          )),
                        ],
                      );
                    })
                      ..toList(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Container(
                          padding: EdgeInsets.only(bottom: 25),
                          //   color: Colors.red,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text("倍数"),
                              ),
                              Expanded(
                                flex: 8,
                                child: Container(
                                  child: Row(
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          model
                                              .timesDecrement(_controller.text);
                                          _controller.text =
                                              model.summary.times.toString();
                                        },
                                        child: Container(
                                          child: const Icon(
                                            Icons.do_not_disturb_on_outlined,
                                            color: Color(0xFFee0a24),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        width: 100,
                                        height: 30,
                                        child: TextField(
                                          keyboardType: TextInputType.number,
                                          textAlign: TextAlign.center,
                                          controller: _controller,
                                          onChanged: (newTimes) {
                                            if (newTimes == "") {
                                              Toast.show("倍数不能为空");
                                              return;
                                            }
                                            int times = int.parse(newTimes);
                                            model.changeBetTimes(times);
                                            return;
                                          },
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(
                                                RegExp('[0-9]'))
                                          ],
                                          decoration: InputDecoration(
                                            hintText: "倍数",
                                            hintStyle: TextStyles.textGray14,
                                            focusedBorder: OutlineInputBorder(
                                              borderSide: const BorderSide(
                                                color: Color(0xFFEEEEEE),
                                                width:
                                                    1.0, // focused state border width
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(30.0),
                                            ),
                                            contentPadding:
                                                const EdgeInsets.symmetric(
                                                    horizontal: 5, vertical: 0),
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(30.0),
                                            ),
                                          ),
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          model
                                              .timesIncrement(_controller.text);

                                          _controller.text =
                                              model.summary.times.toString();
                                        },
                                        child: Container(
                                          child: const Icon(
                                            Icons.add_circle,
                                            color: Color(0xFFee0a24),
                                          ),
                                        ),
                                      ),
                                      Text("倍")
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )),
                      ],
                    ),
                  ],
                ),
              ),
              Expanded(
                  child: Container(
                      alignment: Alignment.bottomCenter,
                      width: double.infinity,
                      child: Container(
                        alignment: Alignment.center,
                        color: Colors.white,
                        padding: EdgeInsets.only(left: 30),
                        height: 56,
                        child: Row(
                          children: [
                            Expanded(
                                flex: 2,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text.rich(
                                      overflow: TextOverflow.ellipsis,
                                      TextSpan(
                                        text: "共",
                                        style: TextStyles.textSize14,
                                        children: <TextSpan>[
                                          TextSpan(
                                            text: "${model.summary.totalCount}",
                                            style: const TextStyle(
                                              color: Colors.red,
                                              fontSize: Dimens.font_sp16,
                                            ),
                                          ),
                                          const TextSpan(
                                            text: "注",
                                          ),
                                          TextSpan(
                                            text: "${model.summary.times}",
                                            style: const TextStyle(
                                              color: Colors.red,
                                              fontSize: Dimens.font_sp16,
                                            ),
                                          ),
                                          const TextSpan(
                                            text: "倍 ",
                                          ),
                                          TextSpan(
                                            text:
                                                "${model.summary.totalAmount}",
                                            style: const TextStyle(
                                              color: Colors.red,
                                              fontSize: Dimens.font_sp16,
                                            ),
                                          ),
                                          const TextSpan(
                                            text: "元",
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                )),
                            Expanded(
                                flex: 1,
                                child: GestureDetector(
                                  onTap: () {
                                    if (model.summary.totalCount <= 0 ||
                                        model.summary.totalAmount <= 0) {
                                      EasyLoading.showToast('请重新选择投注号码');
                                      return;
                                    }

                                    model.readyCommit(MatchDataType.Ssq);

                                    CheckoutModel checkoutModel =
                                        Provider.of<CheckoutModel>(context,
                                            listen: false);

                                    checkoutModel.fillupInfo(model.summary);

                                    NavigatorUtils.push(
                                        context, GameRouter.checkoutPage);
                                  },
                                  child: Container(
                                    color: Colors.red,
                                    alignment: Alignment.center,
                                    child: const Text(
                                      "确认下单",
                                      style: TextStyle(
                                          fontSize: Dimens.font_sp18,
                                          color: Colors.white),
                                    ),
                                  ),
                                )),
                          ],
                        ),
                      )))
            ],
          ));
    });
  }
}
