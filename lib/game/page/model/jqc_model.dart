import 'dart:async';
import 'dart:collection';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/page/entity/game_summary.dart';
import 'package:sport_mobile/game/page/entity/jqc_entity.dart';
import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/net/network_state.dart';
import 'package:sport_mobile/net/ws_utils.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/util/other_utils.dart';

class JQCModel extends ChangeNotifier {
  bool isLoad = true;
  NetworkStatus networkStatus = NetworkStatus.networkConnected;
  bool hasMoreData = true;
  bool hasError = false;

  String gameName = "";

  int gameID = MatchDataType.Jqc;

  Map<String, JQCMatch> selectedMatches = {};

  Map<String, JQCMatchMap> matchList = {};

  GameSummary summary = GameSummary();

  String stopTime = "";

  String termNo = "";

  int totalMatchCount = 0;

  List<String> boutList = [];

  Map<String, JQCMatchMap> sortedRes = {};

  Future refreshData() async {
    isLoad = true;

    try {
      Map<String, JQCMatchMap> tempList = await getJQCList();
      isLoad = false;

      if (tempList.isEmpty) {
        hasMoreData = false;
      } else {
        matchList = tempList;
        hasMoreData = true;
      }
    } on TimeoutException catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkTimeout;
    } catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkFailed;
    }

    notifyListeners();
  }

  bool isActived(JQCMatch currenMatchItem, String type, String num) {
    String matchID = currenMatchItem.id.toString();
    if (selectedMatches.containsKey(matchID)) {
      JQCMatch match = selectedMatches[matchID]!;
      JQCMatchStatusList matchActiveStatus = match.jqcMatchStatusList!;

      List<String> nums;

      if (type == 'home') {
        nums = matchActiveStatus.home;
      } else {
        nums = matchActiveStatus.away;
      }

      if (nums.contains(num)) {
        return true;
      }
    }

    return false;
  }

  confirmUpdateSelectedDigital(JQCMatch currenMatchItem, String spf) {
    // List indexofs = [];
    // selectedMatches.forEach((k, m) {
    //   indexofs.add(m);
    // });

    // //{"1":{matchnum:1, matchActiveStatus:{1:true,2:false,3:true}}}
    // String matchID = currenMatchItem.id.toString();
    // if (selectedMatches.containsKey(matchID)) {
    //   SelectedMatch match = selectedMatches[matchID]!;
    //   Map<String, bool> matchActiveStatus = match.matchActiveStatus!;

    //   if (matchActiveStatus.containsKey(spf)) {
    //     matchActiveStatus[spf] = !matchActiveStatus[spf]!;

    //     matchActiveStatus.removeWhere((key, value) => value == false);

    //     if (matchActiveStatus.isEmpty) {
    //       selectedMatches.remove(matchID);
    //     }
    //   } else {
    //     matchActiveStatus.addAll({spf: true});
    //   }
    // } else {
    //   Map<String, bool>? _matchActiveStatus = {"$spf": true};
    //   selectedMatches[matchID] = SelectedMatch(
    //       matchNum: currenMatchItem.matchNum,
    //       matchActiveStatus: _matchActiveStatus);
    // }

    // if (selectedMatches.length >= 14) {
    //   int _count = 1;

    //   selectedMatches.forEach((key, item) {
    //     _count *= item.matchActiveStatus!.length;
    //   });
    //   summary.totalCount = _count;
    //   summary.totalAmount = _count * summary.times * 2;
    // }

    notifyListeners();
  }

  calculateBet() {
    int sum = 1;
    int totalFields = 0;

    selectedMatches.forEach((key, match) {
      if (match.jqcMatchStatusList!.home.isNotEmpty &&
          match.jqcMatchStatusList!.away.isNotEmpty) {
        match.field = 1;
      } else {
        match.field = 0;
      }
      match.matchSelects = match.jqcMatchStatusList!.home.length *
          match.jqcMatchStatusList!.away.length;

      sum = match.matchSelects! * sum;

      totalFields = match.field! + totalFields;
    });

    summary.totalCount = sum; //注数

    totalMatchCount = totalFields;

    summary.totalAmount = summary.totalCount * summary.times * 2;
  }

  updateSelectedDigital(JQCMatch currenMatchItem, String type, String num) {
    String matchID = currenMatchItem.id.toString();

    List<String> nums;
    if (selectedMatches.containsKey(matchID)) {
      JQCMatch match = selectedMatches[matchID]!;
      JQCMatchStatusList matchActiveStatus = match.jqcMatchStatusList!;

      if (type == 'home') {
        nums = matchActiveStatus.home;
      } else {
        nums = matchActiveStatus.away;
      }

      if (nums.contains(num)) {
        nums.remove(num);
      } else {
        nums.add(num);
      }
    } else {
      JQCMatchStatusList jqcJoined = JQCMatchStatusList(home: [], away: []);

      nums = [num];
      if (type == 'home') {
        jqcJoined.home = nums;
      } else {
        jqcJoined.away = nums;
      }

      selectedMatches[matchID] = JQCMatch(
          jqcMatchStatusList: jqcJoined,
          boutIndex: currenMatchItem.boutIndex,
          field: 0,
          matchNum: currenMatchItem.matchNum,
          id: currenMatchItem.id);
    }


    //做顺序
    var sortedEntries = selectedMatches.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    selectedMatches = Map.fromEntries(sortedEntries);

    calculateBet();

    notifyListeners();
  }

  void readyCommit(int gameID) {
    // setBeisu(beisuTotalInput);
    String term = termNo;
    List<String> arr = [];

    selectedMatches.forEach((key, match) {
      String str = "";
      match.jqcMatchStatusList!.home.forEach((status) {
        str += '$status';
      });

      String awayStr = "";
      match.jqcMatchStatusList!.away.forEach((status) {
        awayStr += '$status';
      });

      arr.add("${match.matchNum}#1^$str");
      arr.add("${match.matchNum}#2^$awayStr");
    });

    String codes = 'fs-${arr.join(',')}-$term';

    String statistics = '$term>${arr.join('|')}';

    summary.courageCode = 0;
    summary.codes = codes;
    summary.statistics = statistics;
    //summary.totalAmount = summary.totalCount * summary.times * 2;

    summary.cnname = OrderType.values[gameID]?.cnname;
    summary.enname = OrderType.values[gameID]?.enname;

    summary.gameID = gameID;

    summary.sp = SPObject();
  }

  void timesIncrement(String currenTimes) {
    int count = int.tryParse(currenTimes) ?? 1;

    count++;
    changeBetTimes(count);
  }

  void timesDecrement(String currenTimes) {
    int count = int.tryParse(currenTimes) ?? 1;
    if (count > 1) {
      count--;
    }
    changeBetTimes(count);
  }

  changeBetTimes(int newTimes) {
    summary.times = newTimes;

    /// summary.totalAmount = summary.totalCount * summary.times * 2;
    calculateBet();
    notifyListeners();
  }

  flushAllSelected() {
    selectedMatches = {};
    totalMatchCount = 0;
    notifyListeners();
  }

  flushExit() {
    selectedMatches = {};
    summary = GameSummary();
    matchList = {};
    totalMatchCount = 0;
  }

  //获取赛事
  Future getJQCList() async {
    String? token = SpUtil.getString(Constant.token);

    var parameters = {
      'version': Constant.version,
      'token': "$token",
    };

    Map<String, JQCMatchMap> result = {};

    String form = Utils.encodeParams(parameters);

    String api = Api.getJQCList;

    BaseRequestEntity baseRequest = BaseRequestEntity(api, form);
    Map<String, dynamic> requestDataMap = baseRequest.toJson();
    var respStr = await WsUtils.instance.sendMessageAndWaitForResponse(
        jsonEncode(requestDataMap),
        showProgress: false);

    var dataList = jsonDecode(respStr);
    var baseRes = BaseEntity.fromJson(dataList);

    if (baseRes.status != "_0000") {
      hasError = true;

      return result;
    } else {
      hasError = false;
    }

    JQCEntitiy jqcEntitiy = JQCEntitiy.fromJson(dataList);
    result = jqcEntitiy.matchlist!;

    boutList = result.keys.toList()..sort();

    sortedRes = {};
    for (var key in boutList) {
      if (result[key] != null) {
        sortedRes[key] = result[key]!;
      }
    }

    int _index = 0;
    sortedRes.forEach((key, item) {
      if (_index == 0) {
        termNo = key;
        stopTime = item.listMatch.first.lotterySaleEndtime!;
      }
      _index++;
    });

    return sortedRes;
  }

  changeBoutData(String bout) {
    termNo = bout;
    stopTime = sortedRes[bout]!.listMatch.first.lotterySaleEndtime!;
    notifyListeners();
  }
}
