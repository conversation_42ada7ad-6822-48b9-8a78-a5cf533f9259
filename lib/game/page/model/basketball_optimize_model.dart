import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/game/data/basketball_bet_commit.dart';
import 'package:sport_mobile/game/data/basketball_state.dart';
import 'package:sport_mobile/game/data/bet_commit.dart';
import 'package:sport_mobile/game/data/league_filter.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/page/entity/game_summary.dart';
import 'package:sport_mobile/game/page/entity/jclq_entity.dart';
import 'package:sport_mobile/game/page/model/chuan_select.dart';
import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/net/ws_utils.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/util/other_utils.dart';

class BasketballOptimizeTouzhuItem {
  String name;
  List<BetTouzhuItem> list;

  double beisu;

  double price;

  String chuan;

  double total;

  BasketballOptimizeTouzhuItem(
      this.name, this.list, this.beisu, this.price, this.chuan, this.total);
}

class BasketballOptimizeModel extends ChangeNotifier {
  bool isLoading = false;
  bool hasMoreData = true;
  bool hasError = false;
//  List<LeagueFilter> leagueFilters = [LeagueFilter('全部', true)];
  late List<LeagueFilter> leagueFilters;
  late List<LeagueFilter> cloneLeagueList;

  List<ChuanSelect> chuanSelectList = [];

  String gameName = "";

  String typeTitle = '';

  String typeKey = "";

  int gameID = MatchDataType.Basketball;

  Map<String, JCLQEntity> selectedMatches = {}; //选中的赛事，但是没有更新倍数相关信息的，不能在确认页显示

  List<JCLQEntity> confirmMatchList = []; //确认页需要渲染的

  List<Map<String, List<JCLQEntity>>> matchList = []; //在体育第一页显示，包含所有赛事
  List<Map<String, List<JCLQEntity>>> originalMatchList = [];

  GameSummary summary = GameSummary();

  BasketballBetCommit basketballBetCommit = BasketballBetCommit();

  Map<String, List<BetTouzhuItem>> matchBeisuMap = {};

  List<bool> optimizeTypes = [true, false, false];

  int optimizeID = 0;

  List<BasketballOptimizeTouzhuItem> optimizeMatchList = [];

  Map<String, BasketballOptimizeTouzhuItem> optimizeMap = {};

  bool noSupport = false;

  String chuanSelectTip = '';

  String stopTime = "";

  String termNo = "";

  bool isChuan = true;

  int beisuTotalInput = 1;

  String stopSaleTime = "";

  List<int> chuanList = [];

  double minMoney = 0;

  Future refreshData() async {
    isLoading = true;
    notifyListeners();

    List<Map<String, List<JCLQEntity>>> tempList = await getBasketballList();
    isLoading = false;

    if (tempList.isEmpty) {
      hasMoreData = false;
    } else {
      originalMatchList = tempList;
      hasMoreData = true;
      selectMatchMap(typeKey);
    }

    notifyListeners();
  }

  //用户在体育投注确认页点击
  updateChuanItem() {
    List<String> _tmpChuanListTips = [];

    List chuanList = [];
    chuanSelectList.forEach((item) {
      if (item.select) {
        _tmpChuanListTips.add(item.name);
        chuanList.add(item.value);
      }

      if (_tmpChuanListTips.isEmpty) {
        chuanSelectTip = '选择投注方式';
      } else {
        chuanSelectTip = _tmpChuanListTips.join(' ');
      }

      basketballBetCommit.calculate(chuanList);

      updateChuanBaseInfo();
    });

    notifyListeners();
  }

  void chooseOptimizeTypes(int index) {
    optimizeTypes.asMap().forEach((_index, select) {
      if (index == _index) {
        optimizeTypes[_index] = true;
      } else {
        optimizeTypes[_index] = false;
      }
    });

    optimizeID = index;
    calculateOptimize();

    notifyListeners();
  }

  calculateOptimize() {
    noSupport = false;

    switch (optimizeID) {
      case 0:
        {
          calculateAverage();
          break;
        }
      case 1:
        {
          calculateHot();
          break;
        }
      case 2:
        {
          calculateCold();
          break;
        }
    }



    Map<String, BasketballOptimizeTouzhuItem> unsortedOptimizeMap = {};
    for (BasketballOptimizeTouzhuItem touzhuItem in optimizeMatchList) {
      unsortedOptimizeMap[touzhuItem.name] = touzhuItem;
    }

    //进行排序
    List<String> sortedKeys = unsortedOptimizeMap.keys.toList();
    sortedKeys.sort((a, b) => int.parse(a).compareTo(int.parse(b)));
    optimizeMap = {};
    for (var key in sortedKeys) {
      optimizeMap[key] = unsortedOptimizeMap[key]!;
    }

    updateInfo();
    notifyListeners();
  }

  updateInfo() {
    Map<String, dynamic> matchBetMap = {};
    int count = 0;

    //因为touzhuitem是浅copy，造成touzhuItem.beisu = beisu;更改倍数时会影响别的方案的倍数
    optimizeMap.forEach((omk, omv) {
      List<BetTouzhuItem> newList = omv.list.map((omTouzhuItem) {
        BetTouzhuItem _omTouzhuItem =
            BetTouzhuItem.fromJson(omTouzhuItem.toJson());
        return _omTouzhuItem;
      }).toList();

      omv.list = newList;
    });

    optimizeMap.forEach((key, optimizeItem) {
      int beisu = optimizeItem.beisu.round();
      count += beisu;

      optimizeItem.list.forEach((touzhuItem) {
        touzhuItem.beisu = beisu;

        List<BetTouzhuItem> touzhuList =
            matchBetMap.containsKey(touzhuItem.boutIndex)
                ? matchBetMap[touzhuItem.boutIndex]
                : [];

        if (touzhuList.isEmpty) {
          matchBetMap[touzhuItem.boutIndex] = touzhuList;
        }
        touzhuList.add(touzhuItem);
      });
    });

    basketballBetCommit.matchList.forEach((matchData) {
      if (matchBetMap.containsKey(matchData.boutIndex) == false) {
        matchData.beisuData!.reset(beisu: 0);
      } else {
        List<BetTouzhuItem> touzhuList = matchBetMap[matchData.boutIndex];

        matchData.beisuData!.addItemList(touzhuList);
      }
    });

    summary.totalCount = count;
    summary.totalAmount = count * 2;

    if (isChuan) {
      basketballBetCommit.calculateChuanWithBeisu(optimizeMap);
      summary.totalMinMoney = basketballBetCommit.minSp;
      summary.totalMaxMoney = basketballBetCommit.maxSp;
    } else {
      basketballBetCommit.calculateWithBeisu(chuanList);
      summary.totalMaxMoney = basketballBetCommit.maxSp * 2;
    }
  }

  // fillUpActiveMatchItem(JCLQEntity match, String method, int index) {
  //   switch (method) {
  //     case "sf":
  //       match.basketballState?.sf[index] = !match.basketballState!.sf[index];
  //       break;

  //     case "rfsf":
  //       match.basketballState?.rfsf[index] =
  //           !match.basketballState!.rfsf[index];
  //       break;

  //     case "sfc":
  //       match.basketballState?.sfc[index] = !match.basketballState!.sfc[index];
  //       break;

  //     case "dxf":
  //       match.basketballState?.dxf[index] = !match.basketballState!.dxf[index];
  //       break;

  //     default:
  //   }

  //   if (match.basketballState!.hasSelect()) {
  //     selectedMatches[match.boutIndex!] = match;
  //   } else {
  //     selectedMatches.remove(match.boutIndex!);
  //   }

  //   refreshMatchCount();
  //   notifyListeners();
  // }

  //更新赛事场次
  refreshMatchCount() {
    int count = 0;
    selectedMatches.forEach((key, matchData) {
      if (matchData.basketballState!.hasSelect()) {
        count += 1;
      }
    });

    summary.selectMatchCount = count;
  }

  List<LeagueFilter> getLeagueList() {
    cloneLeagueList = [];

    leagueFilters.forEach((LeagueFilter league) {
      cloneLeagueList.add(LeagueFilter.fromJson(league.toJson()));
    });

    return leagueFilters;
  }

  beisuUpdate(JCLQEntity match, List<BetTouzhuItem> touzhuItemList) {
    for (var item in confirmMatchList) {
      if (item.boutIndex == match.boutIndex) {
        //  item.ftStateStore = match.ftStateStore;
        item.basketballState =
            BasketballState.fromJson(match.basketballState!.toJson());
      }
    }

    matchBeisuMap[match.boutIndex!] = touzhuItemList;
    updateMatchList();
  }

  noticeBeisuUpdate(BasketballOptimizeTouzhuItem touzhuItemList) {
    optimizeMap[touzhuItemList.name] = touzhuItemList;
    updateInfo();
    notifyListeners();
  }

  void flushConfirmMatch() {
    for (var mapMatchList in matchList) {
      mapMatchList.forEach((key, matchList) {
        for (var match in matchList) {
          match.resetCheckState();
        }
      });
    }
    summary = GameSummary();

    selectedMatches = {};
  }

  bool checkLeague(JCLQEntity matchData) {
    if (leagueFilters.first.select) {
      return true;
    }

    bool hasSelect = false;

    for (LeagueFilter item in leagueFilters) {
      if (item.name == matchData.league) {
        hasSelect = item.select;
      }
    }

    return hasSelect;
  }

  updateMatchList() {
    basketballBetCommit.setMatchMap(selectedMatches, confirmMatchList);
    List<int> chuanList = [];
    for (ChuanSelect item in chuanSelectList) {
      if (item.select) {
        chuanList.add(item.value);
      }
    }

    List<List<BetTouzhuItem>> touzhuList =
        basketballBetCommit.calculateTouzhu(chuanList);

    List<BasketballOptimizeTouzhuItem> list = [];
    touzhuList.asMap().forEach((
      index,
      touzhuList,
    ) {
      double price = 2;
      List<BetTouzhuItem> newTouzhuList = [];
      for (BetTouzhuItem item in touzhuList) {
        if (item.itemType == 'rqspf' && !item.name.contains('让球')) {
          item.name = "让球${item.name}";
        }
        price = price * item.sp;
        newTouzhuList.add(item);
      }

      String chuan = '单关';
      if (newTouzhuList.length > 1) {
        chuan = "${newTouzhuList.length}串1";
      }

      BasketballOptimizeTouzhuItem touzhuItem = BasketballOptimizeTouzhuItem(
          "$index", newTouzhuList, 1, price, chuan, price);
      list.add(touzhuItem);
    });

    optimizeMatchList = list;

    minMoney = touzhuList.length * 2;

    stopSaleTime = basketballBetCommit.stopSaleTime;

    calculateOptimize();
  }

  updateChuanSelectList(maxCount) {
    if (!isChuan) {
      chuanSelectList = [ChuanSelect("单关", true, 1)];
      return;
    }

    List<ChuanSelect> list = [];

    // i 的基数 = 胆的数量 + 1
    int startCount = 2;
    int courageCount = basketballBetCommit.courageCount;

    if (courageCount != 0) {
      startCount = (courageCount + 1);
    }

    for (int i = startCount; i <= maxCount; i++) {
      ChuanSelect item = ChuanSelect("$i串1", false, i);
      list.add(item);
    }

    List<ChuanSelect> csListTemp = chuanSelectList.where((item) {
      return item.select;
    }).toList();

    if (csListTemp.isNotEmpty) {
      list = list.where((item) {
        var i = csListTemp.any((chuan) => chuan.name == item.name);
        if (i) {
          item.select = true;
        }
        return true;
      }).toList();

      List selectCount = list.where((item) => item.select).toList();

      if (selectCount.isEmpty) {
        chuanSelectTip = '选择投注方式';
      } else {
        chuanSelectTip = selectCount.map((item) => item.name).join(' ');
      }
    } else {
      // 默认
      if (list.isNotEmpty && chuanSelectTip != '选择投注方式') {
        list[list.length - 1].select = true;
        chuanSelectTip = list[list.length - 1].name;
      }
    }

    chuanSelectList = [];
    chuanSelectList.addAll(list);
  }

  updateChuanBaseInfo() {
    if (isChuan) {
      int totalBeisu = beisuTotalInput;

      summary.totalCount = basketballBetCommit.betCount;
      summary.totalAmount = double.parse(
          (basketballBetCommit.betCount * totalBeisu * 2).toStringAsFixed(2));
      summary.totalMinMoney = double.parse(
          (basketballBetCommit.minSp * totalBeisu * 2).toStringAsFixed(2));

      summary.totalMaxMoney = double.parse(
          (basketballBetCommit.maxSp * totalBeisu * 2).toStringAsFixed(2));
    } else {
      int beisu = 0;

      matchBeisuMap.forEach((key, List<BetTouzhuItem> value) {
        List<BetTouzhuItem> list = value;

        list.forEach((item) {
          beisu += item.beisu;
        });
      });

      summary.totalCount = beisu;
      summary.totalAmount = beisu * 2;

      summary.totalMinMoney = basketballBetCommit.minSp * 2;
      summary.totalMaxMoney = basketballBetCommit.maxSp * 2;
    }
  }

  selectMatchMap(String type) {
    if (type != typeKey) {
      flushConfirmMatch();
    }

    switch (type) {
      case "ht":
        matchList = [];
        for (Map<String, List<JCLQEntity>> mapMatchList in originalMatchList) {
          //{202023:[match,match] }

          Map<String, List<JCLQEntity>> _mapMatchList = {};

          mapMatchList.forEach((key, matchListItem) {
            List<JCLQEntity> _list = [];
            for (JCLQEntity match in matchListItem) {
              if (checkLeague(match)) {
                if (selectedMatches[match.boutIndex] != null) {
                  match.basketballState =
                      selectedMatches[match.boutIndex]?.basketballState;
                } else {
                  match.resetCheckState();
                }
                _list.add(match);
              }
            }
            _mapMatchList[key] = _list;
          });
          matchList.add(_mapMatchList);
        }

        isChuan = true;
        break;

      case "sf":
        isChuan = true;

        matchList = [];
        for (Map<String, List<JCLQEntity>> mapMatchList in originalMatchList) {
          //{202023:[match,match] }

          Map<String, List<JCLQEntity>> _mapMatchList = {};

          mapMatchList.forEach((key, matchListItem) {
            List<JCLQEntity> _list = [];
            for (JCLQEntity match in matchListItem) {
              if (checkLeague(match) && match.canSF(!isChuan)) {
                if (selectedMatches[match.boutIndex] != null) {
                  match.basketballState =
                      selectedMatches[match.boutIndex]?.basketballState;
                } else {
                  match.resetCheckState();
                }
                _list.add(match);
              }
            }
            _mapMatchList[key] = _list;
          });
          matchList.add(_mapMatchList);
        }

        break;

      case "rfsf":
        isChuan = true;

        matchList = [];
        for (Map<String, List<JCLQEntity>> mapMatchList in originalMatchList) {
          //{202023:[match,match] }

          Map<String, List<JCLQEntity>> _mapMatchList = {};

          mapMatchList.forEach((key, matchListItem) {
            List<JCLQEntity> _list = [];
            for (JCLQEntity match in matchListItem) {
              if (checkLeague(match) && match.canRFSF(!isChuan)) {
                if (selectedMatches[match.boutIndex] != null) {
                  match.basketballState =
                      selectedMatches[match.boutIndex]?.basketballState;
                } else {
                  match.resetCheckState();
                }
                _list.add(match);
              }
            }
            _mapMatchList[key] = _list;
          });
          matchList.add(_mapMatchList);
        }

        break;

      case "dxf":
        isChuan = true;

        matchList = [];
        for (Map<String, List<JCLQEntity>> mapMatchList in originalMatchList) {
          //{202023:[match,match] }

          Map<String, List<JCLQEntity>> _mapMatchList = {};

          mapMatchList.forEach((key, matchListItem) {
            List<JCLQEntity> _list = [];
            for (JCLQEntity match in matchListItem) {
              if (checkLeague(match) && match.canDXF(!isChuan)) {
                if (selectedMatches[match.boutIndex] != null) {
                  match.basketballState =
                      selectedMatches[match.boutIndex]?.basketballState;
                } else {
                  match.resetCheckState();
                }
                _list.add(match);
              }
            }
            _mapMatchList[key] = _list;
          });
          matchList.add(_mapMatchList);
        }

        break;

      case "sfc":
        isChuan = true;

        matchList = [];
        for (Map<String, List<JCLQEntity>> mapMatchList in originalMatchList) {
          //{202023:[match,match] }

          Map<String, List<JCLQEntity>> _mapMatchList = {};

          mapMatchList.forEach((key, matchListItem) {
            List<JCLQEntity> _list = [];
            for (JCLQEntity match in matchListItem) {
              if (checkLeague(match) && match.canSFC(!isChuan)) {
                if (selectedMatches[match.boutIndex] != null) {
                  match.basketballState =
                      selectedMatches[match.boutIndex]?.basketballState;
                } else {
                  match.resetCheckState();
                }
                _list.add(match);
              }
            }
            _mapMatchList[key] = _list;
          });
          matchList.add(_mapMatchList);
        }

        break;

      case "dg":
        matchList = [];
        for (Map<String, List<JCLQEntity>> mapMatchList in originalMatchList) {
          //{202023:[match,match] }

          Map<String, List<JCLQEntity>> _mapMatchList = {};

          mapMatchList.forEach((key, matchListItem) {
            List<JCLQEntity> _list = [];
            for (JCLQEntity match in matchListItem) {
              if (checkLeague(match)) {
                if (selectedMatches[match.boutIndex] != null) {
                  match.basketballState =
                      selectedMatches[match.boutIndex]?.basketballState;
                } else {
                  match.resetCheckState();
                }
                _list.add(match);
              }
            }
            _mapMatchList[key] = _list;
          });
          matchList.add(_mapMatchList);
        }
        isChuan = false;

        break;
      default:
        isChuan = true;
        break;
    }

    notifyListeners();
  }

  void timesIncrement(String currenTimes) {
    int count = int.tryParse(currenTimes) ?? 1;
    count++;
    beisuTotalInput = count;

    updateChuanBaseInfo();
    notifyListeners();
  }

  void timesDecrement(String currenTimes) {
    int count = int.tryParse(currenTimes) ?? 1;
    if (count > 1) {
      count--;
    }
    beisuTotalInput = count;
    updateChuanBaseInfo();
    notifyListeners();
  }

  changeBetTimes(int newTimes) {
    beisuTotalInput = newTimes;
    updateChuanBaseInfo();
    notifyListeners();
  }

  flushAllSelected() {
    selectedMatches = {};
    //  matchList = [];
    confirmMatchList = [];
    summary = GameSummary();
    beisuTotalInput = 1;
    matchBeisuMap = {};
    flushConfirmMatch();
    notifyListeners();
  }

  flushExit() {
    selectedMatches = {};
    summary = GameSummary();
    basketballBetCommit = BasketballBetCommit();
    matchList = [];
    confirmMatchList = [];
    chuanSelectTip = '';
    chuanSelectList = [];
    stopSaleTime = '';
    beisuTotalInput = 1;
    matchBeisuMap = {};
    notifyListeners();
  }

  //获取足球赛事
  Future getBasketballList() async {
    String? token = SpUtil.getString(Constant.token);

    var parameters = {
      'version': Constant.version,
      'token': "$token",
      'action': "ajaxMatchInfoForPhone",
      'type': 'jclq'
    };

    String form = Utils.encodeParams(parameters);

    String api = Api.getJCInfo;

    BaseRequestEntity baseRequest = BaseRequestEntity(api, form);
    Map<String, dynamic> requestDataMap = baseRequest.toJson();
    var respStr = await WsUtils.instance
        .sendMessageAndWaitForResponse(jsonEncode(requestDataMap));

    List<dynamic> dataList = jsonDecode(respStr);

    // "[{'202203':[{'match1','match2',}]}]"
    List<Map<String, List<JCLQEntity>>> listRes = [];

//{'202203':['match1','match2'], '202204':['match1','match2'],}
    Map<String, List<JCLQEntity>> mapRes = {};

    Map<String, bool> _league = {};
    for (var value in dataList) {
      JCLQEntity match = JCLQEntity.fromJson(value);
      _league[match.league!] = false;
      if (mapRes.containsKey(match.matchDay)) {
        mapRes[match.matchDay]?.add(match);
      } else {
        mapRes["${match.matchDay}"] = [match];
      }
    }

    mapRes.forEach((key, value) {
      listRes.add({key: value});
    });
    leagueFilters = [LeagueFilter('全部', true)];

    _league.forEach((key, value) {
      leagueFilters.add(LeagueFilter(key, value));
    });

    return listRes;
  }

  @override
  void notifyListeners() {
    super.notifyListeners();
  }

  List<String> generateChuanTips() {
    List<String> chuanTips = [];
    chuanList.forEach((chuan) {
      if (chuan == 1) {
        chuanTips.add('单关');
      } else {
        chuanTips.add("$chuan串1");
      }
    });

    return chuanTips;
  }

  double checkItemCount(double itemCount) {
    if (itemCount < 1) {
      return 1;
    }

    return itemCount.floor().toDouble();
  }

  calculateAverage() {
    double count = summary.totalAmount / 2;

    List<BasketballOptimizeTouzhuItem> selectItem =
        List.from(optimizeMatchList);

    if (selectItem.isEmpty) {
      return;
    }

    selectItem.sort((o1, o2) {
      if (o1.price > o2.price) {
        return 1;
      } else if (o1.price < o2.price) {
        return -1;
      }

      return 0;
    });

    double firstOdd = selectItem[0].price;

    double firstTmpVal = 1;
    for (int i = 1; i < selectItem.length; i++) {
      firstTmpVal = firstTmpVal + (firstOdd / selectItem[i].price);
    }

    double firstCount = count / firstTmpVal;
    double averageOdds = firstCount * firstOdd;

    firstCount = checkItemCount(firstCount);

    double tempTotalCount = firstCount;
    List<double> itemCountList = [firstCount];

    for (int i = 1; i < selectItem.length; i++) {
      double tempOdd = checkItemCount(averageOdds / selectItem[i].price);
      tempTotalCount = tempTotalCount + tempOdd;
      itemCountList.add(tempOdd);
    }

    if (tempTotalCount < count) {
      double lastCount = count - tempTotalCount;

      for (int i = 0; i < lastCount; i++) {
        double minPrice = 9999999999999;
        int minIndex = -1;
        for (int j = 0; j < selectItem.length; j++) {
          double price = selectItem[j].price * itemCountList[j];
          if (price < minPrice) {
            minPrice = price;
            minIndex = j;
          }
        }

        if (minIndex != -1) {
          itemCountList[minIndex] += 1;
        }
      }
    } else if (tempTotalCount > count) {
      double lastCount = tempTotalCount - count;
      for (int i = 0; i < lastCount; i++) {
        double maxCount = 0;
        int maxIndex = -1;
        for (int j = 0; j < selectItem.length; j++) {
          if (itemCountList[j] > maxCount) {
            maxCount = itemCountList[j];
            maxIndex = j;
          }
        }

        if (maxIndex != -1 && maxCount > 1) {
          itemCountList[maxIndex] -= 1;
        }
      }
    }

    for (int i = 0; i < selectItem.length; i++) {
      BasketballOptimizeTouzhuItem item = selectItem[i];
      double itemCount = itemCountList[i];
      item.beisu = itemCount;
      item.total = itemCount * item.price;
    }

    optimizeMatchList = selectItem;
  }

  calculateHot() {
    double count = summary.totalAmount / 2;
    double totalMoney = summary.totalAmount;

    // List<BJDCOptimizeTouzhuItem> selectItem = optimizeMatchList;

    List<BasketballOptimizeTouzhuItem> selectItem =
        List.from(optimizeMatchList);

    if (selectItem.isEmpty) {
      noSupport = true;
      return;
    }

    selectItem.sort((o1, o2) {
      if (o1.price > o2.price) {
        return 1;
      } else if (o1.price < o2.price) {
        return -1;
      }

      return 0;
    });

    double tempTotalCount = 0;
    List<double> itemCountList = [];

    for (int i = 0; i < selectItem.length; i++) {
      int itemCount = (totalMoney / selectItem[i].price).round();

      ///let itemCount = Math.ceil(totalMoney.div(selectItem[i].price))
      tempTotalCount = tempTotalCount + itemCount;
      itemCountList.add(itemCount.toDouble());
    }

    if (tempTotalCount > count) {
      noSupport = true;
      return;
    }

    itemCountList[0] += count - tempTotalCount;

    for (int i = 0; i < selectItem.length; i++) {
      BasketballOptimizeTouzhuItem item = selectItem[i];
      double itemCount = itemCountList[i];
      item.beisu = itemCount;
      item.total = itemCount * item.price;
    }

    optimizeMatchList = selectItem;
  }

  calculateCold() {
    double count = summary.totalAmount / 2;
    double totalMoney = summary.totalAmount;

    List<BasketballOptimizeTouzhuItem> selectItem =
        List.from(optimizeMatchList);

    if (selectItem.isEmpty) {
      noSupport = true;
      return;
    }

    selectItem.sort((o1, o2) {
      if (o1.price > o2.price) {
        return -1;
      } else if (o1.price < o2.price) {
        return 1;
      }

      return 0;
    });

    int tempTotalCount = 0;
    List<double> itemCountList = [];
    for (int i = 0; i < selectItem.length; i++) {
      int itemCount = (totalMoney / selectItem[i].price).round();

      ///    let itemCount = Math.ceil(totalMoney.div(selectItem[i].price))
      tempTotalCount += itemCount;
      itemCountList.add(itemCount.toDouble());
    }

    if (tempTotalCount > count) {
      noSupport = true;
      return;
    }

    itemCountList[0] += count - tempTotalCount;

    for (int i = 0; i < selectItem.length; i++) {
      BasketballOptimizeTouzhuItem item = selectItem[i];
      double itemCount = itemCountList[i];
      item.beisu = itemCount;
      item.total = itemCount * item.price;
    }

    optimizeMatchList = selectItem;
  }

  readyCommit() {
    List<List<BetTouzhuItem>> touzhuList = [];

    optimizeMatchList.forEach((itemMap) {
      itemMap.list.forEach((item) {
        item.beisu = itemMap.beisu.toInt();
      });
      touzhuList.add(itemMap.list);
    });

    double minSp =
        (basketballBetCommit.minSp - summary.totalAmount) / summary.totalAmount;
    double maxSp =
        (basketballBetCommit.maxSp - summary.totalAmount) / summary.totalAmount;
    summary.optimizeID = optimizeID;

    if (isChuan) {
      summary.times = beisuTotalInput;
      summary.sp = SPObject(
          minPrice: summary.totalMinMoney,
          maxPrice: summary.totalMaxMoney,
          minSp: minSp,
          maxSp: maxSp);

      // List<List<BetTouzhuItem>> touzhuList =
      //     basketballBetCommit.calculateTouzhu(chuanList);

      String codes = basketballBetCommit.getTouzhuCodeBeisu(touzhuList);
      String statistics = basketballBetCommit.getTouzhuStatistics(touzhuList);
      String courageCode = basketballBetCommit.getCourageCode();

      summary.codes = codes;
      summary.statistics = statistics;
      summary.courageCode = courageCode != '' ? int.parse(courageCode) : 0;
    } else {
      summary.times = 1;

      summary.sp = SPObject(
          minPrice: summary.totalMaxMoney,
          maxPrice: summary.totalMaxMoney,
          minSp: minSp,
          maxSp: maxSp);

      // List<List<BetTouzhuItem>> touzhuList = [];

      // matchBeisuMap.forEach((key, List<BetTouzhuItem> value) {
      //   List<BetTouzhuItem> list = value;
      //   touzhuList.add(list);
      // });

      String codes = basketballBetCommit.getTouzhuCodeSingle(touzhuList);
      String statistics = basketballBetCommit.getTouzhuStatistics(touzhuList);

      summary.codes = codes;
      summary.statistics = statistics;
    }

    if (summary.totalAmount < 100) {
      summary.isSelect = 9;
    } else {
      summary.isSelect = 2;
    }
    summary.gameID = gameID;
    summary.chuanTip = generateChuanTips().join(',');
    summary.cnname = OrderType.values[gameID]?.cnname;
    summary.enname = OrderType.values[gameID]?.enname;
  }
}
