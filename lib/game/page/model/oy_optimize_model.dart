import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:sport_mobile/game/data/bet_commit.dart';
import 'package:sport_mobile/game/data/oyzq_bet_commit.dart';
import 'package:sport_mobile/game/data/oy_state_store.dart';
import 'package:sport_mobile/game/data/league_filter.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/page/entity/game_summary.dart';
import 'package:sport_mobile/game/page/entity/oyzq_entity.dart';
import 'package:sport_mobile/game/page/model/chuan_select.dart';

class OYOptimizeTouzhuItem {
  String name;
  List<BetTouzhuItem> list;

  double beisu;

  double price;

  String chuan;

  double total;

  OYOptimizeTouzhuItem(
      this.name, this.list, this.beisu, this.price, this.chuan, this.total);
}

class OYOptimizeModel extends ChangeNotifier {
  bool noSupport = false;

  List<bool> optimizeTypes = [true, false, false];

  int optimizeID = 0;

  late List<LeagueFilter> leagueFilters;

  late List<LeagueFilter> cloneLeagueList;

  List<ChuanSelect> chuanSelectList = [];

  String gameName = "";

  String typeTitle = '';

  String typeKey = "";

  int gameID = MatchDataType.Oyjz;

  Map<String, OYZQEntity> selectedMatches = {}; //选中的赛事，但是没有更新倍数相关信息的，不能在确认页显示

  List<OYZQEntity> confirmMatchList = []; //确认页需要渲染的

  List<Map<String, List<OYZQEntity>>> matchList = []; //在体育第一页显示，包含所有赛事
  List<Map<String, List<OYZQEntity>>> originalMatchList = [];

  List<OYOptimizeTouzhuItem> optimizeMatchList = [];

  GameSummary summary = GameSummary();

  OYZQBetCommit oyBetCommit = OYZQBetCommit();

  Map<String, List<BetTouzhuItem>> matchBeisuMap = {};

  String chuanSelectTip = '';

  String stopTime = "";

  String termNo = "";

  bool isChuan = true;

  int beisuTotalInput = 1;

  String stopSaleTime = "";

  double minMoney = 0;

  List<int> chuanList = [];
  Map<String, OYOptimizeTouzhuItem> optimizeMap = {};

  void chooseOptimizeTypes(int index) {
    optimizeTypes.asMap().forEach((_index, select) {
      if (index == _index) {
        optimizeTypes[_index] = true;
      } else {
        optimizeTypes[_index] = false;
      }
    });

    optimizeID = index;
    calculateOptimize();

    notifyListeners();
  }

  //更新赛事场次
  refreshMatchCount() {
    int count = 0;
    selectedMatches.forEach((key, matchData) {
      if (matchData.oyStateStore!.hasSelect()) {
        count += 1;
      }
    });

    summary.selectMatchCount = count;
  }

  List<LeagueFilter> getLeagueList() {
    cloneLeagueList = [];

    leagueFilters.forEach((LeagueFilter league) {
      cloneLeagueList.add(LeagueFilter.fromJson(league.toJson()));
    });

    return leagueFilters;
  }

  itemMatchUpdate(OYZQEntity match) {
    bool isNotUpdateList = true;

    if (match.oyStateStore!.hasSelect()) {
      selectedMatches[match.boutIndex!] = match;
    } else {
      isNotUpdateList = false;
      selectedMatches.remove(match.boutIndex!);
    }

    updateMatchList(isBeisuUpdate: isNotUpdateList);
    notifyListeners();
  }

  beisuUpdate(OYZQEntity match, List<BetTouzhuItem> touzhuItemList) {
    for (var item in confirmMatchList) {
      if (item.boutIndex == match.boutIndex) {
        //  item.oyStateStore = match.oyStateStore;

        item.oyStateStore = OYStateStore.fromJson(match.oyStateStore!.toJson());
      }
    }

    matchBeisuMap[match.boutIndex!] = touzhuItemList;
    updateMatchList(isBeisuUpdate: true);
  }

  noticeBeisuUpdate(OYOptimizeTouzhuItem touzhuItemList) {
    optimizeMap[touzhuItemList.name] = touzhuItemList;
    updateInfo();
    notifyListeners();
  }

  void flushConfirmMatch() {
    for (var mapMatchList in matchList) {
      mapMatchList.forEach((key, matchList) {
        for (var match in matchList) {
          match.resetCheckState();
        }
      });
    }
    summary = GameSummary();

    selectedMatches = {};
  }

  bool checkLeague(OYZQEntity matchData) {
    if (leagueFilters.first.select) {
      return true;
    }

    bool hasSelect = false;

    for (LeagueFilter item in leagueFilters) {
      if (item.name == matchData.league) {
        hasSelect = item.select;
      }
    }

    return hasSelect;
  }

  updateMatchList({bool isBeisuUpdate = false}) {
    oyBetCommit.setMatchMap(selectedMatches, confirmMatchList);
    List<int> chuanList = [];
    for (ChuanSelect item in chuanSelectList) {
      if (item.select) {
        chuanList.add(item.value);
      }
    }

    List<List<BetTouzhuItem>> touzhuList =
        oyBetCommit.calculateTouzhu(chuanList);

    List<OYOptimizeTouzhuItem> list = [];
    touzhuList.asMap().forEach((
      index,
      touzhuList,
    ) {
      double price = 2;
      List<BetTouzhuItem> newTouzhuList = [];
      for (BetTouzhuItem item in touzhuList) {
        if (item.itemType == 'rqspf' && !item.name.contains('让球')) {
          item.name = "让球${item.name}";
        }
        price = price * item.sp;
        newTouzhuList.add(item);
      }

      String chuan = '单关';
      if (newTouzhuList.length > 1) {
        chuan = "${newTouzhuList.length}串1";
      }

      OYOptimizeTouzhuItem touzhuItem =
          OYOptimizeTouzhuItem("$index", newTouzhuList, 1, price, chuan, price);
      list.add(touzhuItem);
    });

    optimizeMatchList = list;

    minMoney = touzhuList.length * 2;

    stopSaleTime = oyBetCommit.stopSaleTime;

    calculateOptimize();
  }

  updateChuanSelectList(maxCount) {
    if (!isChuan) {
      chuanSelectList = [ChuanSelect("单关", true, 1)];
      return;
    }

    List<ChuanSelect> list = [];

    // i 的基数 = 胆的数量 + 1
    int startCount = 2;
    int courageCount = oyBetCommit.courageCount;

    if (courageCount != 0) {
      startCount = (courageCount + 1);
    }

    for (int i = startCount; i <= maxCount; i++) {
      ChuanSelect item = ChuanSelect("$i串1", false, i);
      list.add(item);
    }

    List<ChuanSelect> csListTemp = chuanSelectList.where((item) {
      return item.select;
    }).toList();

    if (csListTemp.isNotEmpty) {
      list = list.where((item) {
        var i = csListTemp.any((chuan) => chuan.name == item.name);
        if (i) {
          item.select = true;
        }
        return true;
      }).toList();

      List selectCount = list.where((item) => item.select).toList();

      if (selectCount.isEmpty) {
        chuanSelectTip = '选择投注方式';
      } else {
        chuanSelectTip = selectCount.map((item) => item.name).join(' ');
      }
    } else {
      // 默认
      if (list.isNotEmpty && chuanSelectTip != '选择投注方式') {
        list[list.length - 1].select = true;
        chuanSelectTip = list[list.length - 1].name;
      }
    }

    chuanSelectList = [];
    chuanSelectList.addAll(list);
  }

  selectMatchMap(String type) {
    if (type != typeKey) {
      flushConfirmMatch();
    }

    switch (type) {
      case "ht":
        matchList = [];
        for (Map<String, List<OYZQEntity>> mapMatchList in originalMatchList) {
          //{202023:[match,match] }

          Map<String, List<OYZQEntity>> _mapMatchList = {};

          mapMatchList.forEach((key, matchListItem) {
            List<OYZQEntity> _list = [];
            for (OYZQEntity match in matchListItem) {
              if (checkLeague(match)) {
                if (selectedMatches[match.boutIndex] != null) {
                  match.oyStateStore =
                      selectedMatches[match.boutIndex]?.oyStateStore;
                } else {
                  match.resetCheckState();
                }
                _list.add(match);
              }
            }
            _mapMatchList[key] = _list;
          });
          matchList.add(_mapMatchList);
        }

        isChuan = true;
        break;

      case "spf":
        matchList = [];
        for (Map<String, List<OYZQEntity>> mapMatchList in originalMatchList) {
          //{202023:[match,match] }

          Map<String, List<OYZQEntity>> _mapMatchList = {};

          mapMatchList.forEach((key, matchListItem) {
            List<OYZQEntity> _list = [];
            for (OYZQEntity match in matchListItem) {
              if (checkLeague(match) && match.canOySPF()) {
                if (selectedMatches[match.boutIndex] != null) {
                  match.oyStateStore =
                      selectedMatches[match.boutIndex]?.oyStateStore;
                } else {
                  match.resetCheckState();
                }
                _list.add(match);
              }
            }
            _mapMatchList[key] = _list;
          });
          matchList.add(_mapMatchList);
        }

        isChuan = true;

        break;

      default:
        isChuan = true;
        break;
    }

    notifyListeners();
  }

  @override
  void notifyListeners() {
    super.notifyListeners();
  }

  List<String> generateChuanTips() {
    List<String> chuanTips = [];
    chuanList.forEach((chuan) {
      if (chuan == 1) {
        chuanTips.add('单关');
      } else {
        chuanTips.add("$chuan串1");
      }
    });

    return chuanTips;
  }

  calculateOptimize() {
    noSupport = false;

    switch (optimizeID) {
      case 0:
        calculateAverage();
        break;
      case 1:
        calculateHot();
        break;
      case 2:
        calculateCold();
        break;
    }


    Map<String, OYOptimizeTouzhuItem> unsortedOptimizeMap = {};
    for (OYOptimizeTouzhuItem touzhuItem in optimizeMatchList) {
      unsortedOptimizeMap[touzhuItem.name] = touzhuItem;
    }

    //进行排序
    List<String> sortedKeys = unsortedOptimizeMap.keys.toList();
    sortedKeys.sort((a, b) => int.parse(a).compareTo(int.parse(b)));
    optimizeMap = {};
    for (var key in sortedKeys) {
      optimizeMap[key] = unsortedOptimizeMap[key]!;
    }

    updateInfo();
    notifyListeners();
  }

  updateInfo() {
    Map<String, dynamic> matchBetMap = {};
    int count = 0;

    //因为touzhuitem是浅copy，造成touzhuItem.beisu = beisu;更改倍数时会影响别的方案的倍数
    optimizeMap.forEach((omk, omv) {
      List<BetTouzhuItem> newList =   omv.list.map((omTouzhuItem) {
          BetTouzhuItem  _omTouzhuItem =  BetTouzhuItem.fromJson(omTouzhuItem.toJson());
          return   _omTouzhuItem;

        }).toList();

      omv.list = newList;
    });

    optimizeMap.forEach((key, _optimizeItem) {
      int beisu = _optimizeItem.beisu.round().toInt();
      count += beisu;
      _optimizeItem.list.forEach((touzhuItem) {
        touzhuItem.beisu = beisu;

        List<BetTouzhuItem> touzhuList =
            matchBetMap.containsKey(touzhuItem.boutIndex)
                ? matchBetMap[touzhuItem.boutIndex]
                : [];

        if (touzhuList.isEmpty) {
          matchBetMap[touzhuItem.boutIndex] = touzhuList;
        }
        touzhuList.add(touzhuItem);
      });
    });

    oyBetCommit.matchList.forEach((matchData) {
      if (matchBetMap.containsKey(matchData.boutIndex) == false) {
        matchData.beisuData!.reset(beisu: 0);
      } else {
        List<BetTouzhuItem> touzhuList = matchBetMap[matchData.boutIndex];

        matchData.beisuData!.addItemList(touzhuList);
      }
    });

    summary.totalCount = count;
    summary.totalAmount = count * 2;

    if (isChuan) {
      oyBetCommit.calculateChuanWithBeisu(optimizeMap);
      summary.totalMinMoney = oyBetCommit.minSp;
      summary.totalMaxMoney = oyBetCommit.maxSp;
    } else {
      oyBetCommit.calculateWithBeisu(chuanList);
      summary.totalMaxMoney = oyBetCommit.maxSp * 2;
    }
  }

  double checkItemCount(double itemCount) {
    if (itemCount < 1) {
      return 1;
    }

    return itemCount.floor().toDouble();
  }

  calculateAverage() {
    double count = summary.totalAmount / 2;

    List<OYOptimizeTouzhuItem> selectItem = List.from(optimizeMatchList);

    if (selectItem.isEmpty) {
      return;
    }

    selectItem.sort((o1, o2) {
      if (o1.price > o2.price) {
        return 1;
      } else if (o1.price < o2.price) {
        return -1;
      }

      return 0;
    });


    double firstOdd = selectItem.first.price;

    double firstTmpVal = 1;
    for (int i = 1; i < selectItem.length; i++) {
      firstTmpVal = firstTmpVal + (firstOdd / selectItem[i].price);
    }

    double firstCount = (count / firstTmpVal);
    // firstCount = firstCount.roundToDouble();

    double averageOdds = firstCount * firstOdd;

    firstCount = checkItemCount(firstCount);

    double tempTotalCount = firstCount;
    List<double> itemCountList = [firstCount];

    for (int i = 1; i < selectItem.length; i++) {
      double tempOdd = checkItemCount(averageOdds / selectItem[i].price);
      tempTotalCount = tempTotalCount + tempOdd;
      itemCountList.add(tempOdd);
    }

    if (tempTotalCount < count) {
      double lastCount = count - tempTotalCount;

      for (int i = 0; i < lastCount; i++) {
        double minPrice = 9999999999999;
        int minIndex = -1;
        for (int j = 0; j < selectItem.length; j++) {
          double price = selectItem[j].price * itemCountList[j];
          if (price < minPrice) {
            minPrice = price;
            minIndex = j;
          }
        }

        if (minIndex != -1) {
          itemCountList[minIndex] += 1;
        }
      }
    } else if (tempTotalCount > count) {
      double lastCount = tempTotalCount - count;
      for (int i = 0; i < lastCount; i++) {
        double maxCount = 0;
        int maxIndex = -1;
        for (int j = 0; j < selectItem.length; j++) {
          if (itemCountList[j] > maxCount) {
            maxCount = itemCountList[j];
            maxIndex = j;
          }
        }

        if (maxIndex != -1 && maxCount > 1) {
          itemCountList[maxIndex] -= 1;
        }
      }
    }

    for (int i = 0; i < selectItem.length; i++) {
      OYOptimizeTouzhuItem item = selectItem[i];
      //   double itemCount = itemCountList[i].round().toDouble();
      double itemCount = itemCountList[i];

      itemCount = itemCount;
      item.beisu = itemCount;
      item.total = itemCount * item.price;
    }

    optimizeMatchList = selectItem;
  }

  calculateHot() {
    double count = summary.totalAmount / 2;
    double totalMoney = summary.totalAmount;

    List<OYOptimizeTouzhuItem> selectItem = List.from(optimizeMatchList);

    if (selectItem.isEmpty) {
      noSupport = true;
      return;
    }

    selectItem.sort((o1, o2) {
      if (o1.price > o2.price) {
        return 1;
      } else if (o1.price < o2.price) {
        return -1;
      }

      return 0;
    });

    double tempTotalCount = 0;
    List<double> itemCountList = [];

    for (int i = 0; i < selectItem.length; i++) {
      int itemCount = (totalMoney / selectItem[i].price).ceil();

      tempTotalCount = tempTotalCount + itemCount;
      itemCountList.add(itemCount.toDouble());
    }

    if (tempTotalCount > count) {
      noSupport = true;
      return;
    }

    itemCountList[0] += count - tempTotalCount;

    for (int i = 0; i < selectItem.length; i++) {
      OYOptimizeTouzhuItem item = selectItem[i];
      double itemCount = itemCountList[i];
      item.beisu = itemCount;
      item.total = itemCount * item.price;
    }

    optimizeMatchList = selectItem;
  }

  calculateCold() {
    double count = summary.totalAmount / 2;
    double totalMoney = summary.totalAmount;

    List<OYOptimizeTouzhuItem> selectItem = List.from(optimizeMatchList);

    if (selectItem.isEmpty) {
      noSupport = true;
      return;
    }

    selectItem.sort((o1, o2) {
      if (o1.price > o2.price) {
        return -1;
      } else if (o1.price < o2.price) {
        return 1;
      }

      return 0;
    });

    int tempTotalCount = 0;
    List<double> itemCountList = [];
    for (int i = 0; i < selectItem.length; i++) {
      int itemCount = (totalMoney / selectItem[i].price).ceil();

      ///    let itemCount = Math.ceil(totalMoney.div(selectItem[i].price))
      tempTotalCount += itemCount;
      itemCountList.add(itemCount.toDouble());
    }

    if (tempTotalCount > count) {
      noSupport = true;
      return;
    }

    itemCountList[0] += count - tempTotalCount;

    for (int i = 0; i < selectItem.length; i++) {
      OYOptimizeTouzhuItem item = selectItem[i];
      double itemCount = itemCountList[i];
      item.beisu = itemCount;
      item.total = itemCount * item.price;
    }

    optimizeMatchList = selectItem;
  }

  readyCommit() {
    List<List<BetTouzhuItem>> touzhuList = [];

    optimizeMatchList.forEach((itemMap) {
      itemMap.list.forEach((item) {
        item.beisu = itemMap.beisu.toInt();
      });
      touzhuList.add(itemMap.list);
    });

        double minSp =
        (oyBetCommit.minSp - summary.totalAmount) / summary.totalAmount;
    double maxSp =
        (oyBetCommit.maxSp - summary.totalAmount) / summary.totalAmount;

    summary.optimizeID  = optimizeID;

    if (isChuan) {
      summary.times = beisuTotalInput;
      summary.sp = SPObject(
          minPrice: summary.totalMinMoney,
          maxPrice: summary.totalMaxMoney,
          minSp: minSp,
          maxSp: maxSp);

      // List<List<BetTouzhuItem>> touzhuList =
      //     ftBetCommit.calculateTouzhu(chuanList);

      String codes = oyBetCommit.getTouzhuCodeBeisu(touzhuList);
      String statistics = oyBetCommit.getTouzhuStatistics(touzhuList);
      String courageCode = oyBetCommit.getCourageCode();

      summary.codes = codes;
      summary.statistics = statistics;
      summary.courageCode = courageCode != '' ? int.parse(courageCode) : 0;
    } else {
      summary.times = 1;

      summary.sp = SPObject(
          minPrice: summary.totalMaxMoney,
          maxPrice: summary.totalMaxMoney,
          minSp: minSp,
          maxSp: maxSp);

      // List<List<BetTouzhuItem>> touzhuList = [];

      // matchBeisuMap.forEach((key, List<BetTouzhuItem> value) {
      //   List<BetTouzhuItem> list = value;
      //   touzhuList.add(list);
      // });

      String codes = oyBetCommit.getTouzhuCodeSingle(touzhuList);
      String statistics = oyBetCommit.getTouzhuStatistics(touzhuList);

      summary.codes = codes;
      summary.statistics = statistics;
    }

    if (summary.totalAmount < 100) {
      summary.isSelect = 9;
    } else {
      summary.isSelect = 2;
    }

  

    summary.chuanTip = generateChuanTips().join(',');

    summary.gameID = gameID;

    summary.cnname = OrderType.values[gameID]?.cnname;

    summary.enname = OrderType.values[gameID]?.enname;
  }
}
