import 'dart:async';
import 'dart:convert';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/game/data/lottery_data.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/page/entity/game_checkout._entity.dart';
import 'package:sport_mobile/game/page/entity/game_privileges_entity.dart';
import 'package:sport_mobile/game/page/entity/game_summary.dart';
import 'package:sport_mobile/game/page/entity/ggl_entity.dart';
import 'package:sport_mobile/game/page/widgets/ggl_dialog.dart';
import 'package:sport_mobile/game/routers/game_router.dart';
import 'package:sport_mobile/login/model/user_model.dart';
import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/net/ws_utils.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/net/network_state.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/util/device_utils.dart';
import 'package:sport_mobile/util/other_utils.dart';

class GGLModel extends ChangeNotifier {
  bool isLoad = false;
  NetworkStatus networkStatus = NetworkStatus.networkConnected;

  bool hasMoreData = true;

  bool hasError = false;

  String gameName = "";

  GameSummary summary = GameSummary();

  GGLEntity gglEntity = GGLEntity();

  List<GGLList> gglList = [];

  List<String> kjh = [];
  List<List<List<String>>> sjh = [];

  List<String> zjh = [];

  String orderNumber = "";

  String zjj = "";

  String xyj = "";

  String typeCode = "";

  String gameCode = "ggl_zgh";

  String? status;

  int? orderStatus;

  String? amount;

  String? orderID;

  late int gameID;

  late List<LotteryGroup> confirmSelectdList;

  GGLOrderDetailEntity? gglOrderDetail;

  GGLPreprareParms? gglPreprareParms;

  bool rebuy = false;

  Future getList() async {
    try {
      var parameters = {
        'version': Constant.version,
      };
      isLoad = true;
      networkStatus = NetworkStatus.networkConnected;

      String form = Utils.encodeParams(parameters);

      BaseRequestEntity baseRequest = BaseRequestEntity(Api.getGGLInfo, form);

      Map<String, dynamic> requestDataMap = baseRequest.toJson();
      var respStr = await WsUtils.instance.sendMessageAndWaitForResponse(
          jsonEncode(requestDataMap),
          showProgress: false);
      isLoad = false;
      networkStatus = NetworkStatus.networkConnected;

      var dataList = jsonDecode(respStr);

      gglEntity = GGLEntity.fromJson(dataList);

      gglList = gglEntity.list!;
    } on TimeoutException catch (_) {
      isLoad = false;
      networkStatus = NetworkStatus.networkTimeout;
    } catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkFailed;
    }

    notifyListeners();
    return gglEntity;
  }

  Future<GGLOrderDetailEntity?> getGGLByOrderID(String id) async {
    try {
      var parameters = {
        'version': Constant.version,
        'orderid': "$id",
        'type': "24",
        "game_code": "ggl_zgh",
        "type_code": "zgh_te"
      };
      isLoad = true;
      networkStatus = NetworkStatus.networkConnected;

      String form = Utils.encodeParams(parameters);

      BaseRequestEntity baseRequest =
          BaseRequestEntity(Api.getGGLInfoByOrderID, form);

      Map<String, dynamic> requestDataMap = baseRequest.toJson();
      var respStr = await WsUtils.instance.sendMessageAndWaitForResponse(
          jsonEncode(requestDataMap),
          showProgress: false);
      isLoad = false;
      networkStatus = NetworkStatus.networkConnected;

      var dataList = jsonDecode(respStr);

      gglOrderDetail = GGLOrderDetailEntity.fromJson(dataList);

      if (gglOrderDetail != null) {
        kjh = splitKJH(gglOrderDetail!.list.kjh);
        sjh = splitSJH(gglOrderDetail!.list.sjh);
        zjh = splitKJH(gglOrderDetail!.list.zjh);
        xyj = gglOrderDetail!.list.xyj;
        zjj = gglOrderDetail!.list.zjj;
        orderNumber = gglOrderDetail!.list.numberNo;
        status = gglOrderDetail?.status;

        orderStatus = gglOrderDetail?.list.status;
      }
    } on TimeoutException catch (_) {
      isLoad = false;
      networkStatus = NetworkStatus.networkTimeout;
    } catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkFailed;
    }

    notifyListeners();
    return gglOrderDetail;
  }

  void consecutiveBuy(BuildContext context) {
    if (status == "3") {
      EasyLoading.showToast("未刮完彩票，在待开奖里");
      return;
    }

    GGLList item = GGLList(bonusFace: int.parse(amount!), lotteryTitle: "中国红");

    gglPreprareParms = goConfirm(item, digitalLotteryGgl);

    if (gglPreprareParms != null) {
      showBuyDialog(context, item, gglPreprareParms!);
    }
  }

  void showBuyDialog(
    BuildContext context,
    GGLList gglListItem,
    GGLPreprareParms gglPreprareParms,
  ) {
    showDialog<void>(
        context: context,
        builder: (_) => GGLDialog(
              title: gglListItem.lotteryTitle,
              facevalues: gglPreprareParms.buyMoney!,
              initValue: gglPreprareParms.buyMoney?.first,
              onPressed: (int amount) async {
                var gamePrivileges = await checkUserGamePrivileges();

                var balance = gamePrivileges.lotterybalance;

                if (balance == null) {
                  EasyLoading.showToast("余额不足");
                  return false;
                }

                var totalBalance = balance + gamePrivileges.mosaicGold!;

                if (balance <= 0) {
                  EasyLoading.showToast("余额不足");
                  return false;
                }

                if (totalBalance < amount) {
                  EasyLoading.showToast("余额不足");
                  return false;
                }
                var encryptBalance = balance.toStringAsFixed(2);
                var bTxt = EncryptUtil.encodeMd5(
                    EncryptUtil.encodeMd5(encryptBalance) + Constant.GGLKEY);

                Map<String, dynamic> params = {
                  'price_radio': "0",
                  'is_pic': "0",
                  'share_money': "$amount",
                  'share_desc': "中国红10元",
                  'is_sp': "0",
                  'is_select': "9",
                  'min_multiple': "1",
                  'rate_limit': "0",
                  'lotteryType': "ggl",
                  'term': gamePrivileges.termNo,
                  'codes': "",
                  'money': "$amount",
                  'multiple': "1",
                  'addtional': '0',
                  'touzhu_statistics': "",
                  'touzhu_style': "${gglListItem.lotteryTitle}元",
                  'max_forecast_reward': "${gglPreprareParms.maxPrice}",
                  'min_forecast_reward': "${gglPreprareParms.minPrice}",
                  'b': bTxt,
                  'oneMoney': "$amount",
                  'is_optimization': '0',
                  'coupon_id': "0",
                  'other_from': 'qzc',
                  'sp': '',
                  'min_sp': '',
                  'max_sp': '',
                  'necessary': '',
                  'jjyh': '',
                };

                String nickName = SpUtil.getString(Constant.nickName) ?? '';

                String idKeys = [
                  nickName,
                  amount.toString(),
                  gamePrivileges.termNo!,
                  params['lotteryType'],
                  "",
                  params['multiple'].toString(),
                  amount.toString(),
                  Constant.GGLKEY
                ].join('|');

                params['sign'] = EncryptUtil.encodeMd5(idKeys);
                var resp = await buy(params);
                if (resp.status == "_0000") {
                  EasyLoading.showToast("购买成功");
                  Navigator.of(context).popUntil((route) {
                    final routeName = route.settings.name ?? '';
                    if (routeName.startsWith('/games/gglDetail')) {
                      return true;
                    }
                    return false;
                  });

                  NavigatorUtils.push(context,
                      "${GameRouter.gglDetailPage}/${resp.orderId}/${amount.toString()}",
                      replace: true);
                } else {
                  EasyLoading.showToast("${resp.message}");
                }

                return true;
              },
              onBackPressed: () {
                Navigator.of(context).pop(true);
              },
            ));
  }

  Future<BaseEntity?> gglDrawing(String orderID) async {
    BaseEntity? baseRes;
    String? token = SpUtil.getString(Constant.token);
     var parameters = {
        'token': "$token",
        'version': Constant.version,
        'orderid': "$orderID",
        'type': "24",
        "game_code": "ggl_zgh",
        "type_code": "zgh_te"
      };

      String form = Utils.encodeParams(parameters);

      BaseRequestEntity baseRequest = BaseRequestEntity(Api.gglDrawing, form);

      Map<String, dynamic> requestDataMap = baseRequest.toJson();
      var respStr = await WsUtils.instance.sendMessageAndWaitForResponse(
          jsonEncode(requestDataMap),
          showProgress: false);
      isLoad = false;

      var dataList = jsonDecode(respStr);

      baseRes = BaseEntity.fromJson(dataList);

    return baseRes;
  }

  Future<GameCheckoutEntity> buy(Map<String, dynamic> parameters) async {
    String? token = SpUtil.getString(Constant.token);

    parameters['channel'] = Utils.getDeviceName();
    // parameters['channel'] = "ios";

    parameters['token'] = token;
    parameters['version'] =  Device.getVersion();

    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest = BaseRequestEntity(Api.commitOrder, form);

    Map<String, dynamic> requestDataMap = baseRequest.toJson();

    var respStr = await WsUtils.instance
        .sendMessageAndWaitForResponse(jsonEncode(requestDataMap));

    var dataList = jsonDecode(respStr);

    GameCheckoutEntity resp = GameCheckoutEntity.fromJson(dataList);

    return resp;
  }

  //获取用户钱包相关
  Future<GamePrivilegesEntity> checkUserGamePrivileges() async {
    String type = OrderType.values[MatchDataType.GGL]!.cnname;

    String? token = SpUtil.getString(Constant.token);
    var parameters = {
      'version': Constant.version,
      'type': type.toLowerCase(),
      'channel': Utils.getDeviceName(),
      'token': "$token",
    };

    String form = Utils.encodeParams(parameters);

    BaseRequestEntity baseRequest =
        BaseRequestEntity(Api.getGamePrivileges, form);

    Map<String, dynamic> requestDataMap = baseRequest.toJson();
    var respStr = await WsUtils.instance
        .sendMessageAndWaitForResponse(jsonEncode(requestDataMap));

    var dataList = jsonDecode(respStr);

    var gamePrivileges = GamePrivilegesEntity.fromJson(dataList);

    return gamePrivileges;
  }

  List<String> splitKJH(String str) {
    List<String> pairs = str.split('|');

    List<String> result = [];

    for (String pair in pairs) {
      if (pair.isNotEmpty) {
        // 过滤掉空字符串
        List<String> parts = pair.split('-');
        result.add(parts.first);
      }
    }

    return result;
  }

  List<String> splitZJH(String str) {
    List<String> numbers = str.split('|');
    return numbers;
  }

  List<List<List<String>>> splitSJH(String str) {
    // 按竖线切割
    List<String> pairs = str.split('|');

    List<List<String>> result = [];

    for (String pair in pairs) {
      if (pair.isNotEmpty) {
        // 过滤掉空字符串
        List<String> parts = pair.split('-');
        result.add(parts);
      }
    }

    List<List<List<String>>> groupedResult = [];
    for (int i = 0; i < result.length; i += 4) {
      groupedResult.add(
          result.sublist(i, i + 4 > result.length ? result.length : i + 4));
    }
    return groupedResult;
  }

  @override
  void notifyListeners() {
    super.notifyListeners();
  }
}

class DigitalLotteryGgl {
  final int id;
  final String name;
  final int type;
  final String gameCode;
  final String release;
  final List<FaceValue> faceValue;
  final BuyNotice buyNotice;
  final InfoTxt infoTxt;
  final RulesTxt rulesTxt;

  DigitalLotteryGgl({
    required this.id,
    required this.name,
    required this.type,
    required this.gameCode,
    required this.release,
    required this.faceValue,
    required this.buyNotice,
    required this.infoTxt,
    required this.rulesTxt,
  });
}

class FaceValue {
  final int isSale;
  final String name;
  final dynamic money; // Can be int or String
  final String code;
  final int level;
  final int winNum;
  final String winSide;
  final int maxPrize;
  final String maxPrizeName;
  final List<bool> checked;
  final List<BonusLevel> bonusLevel;
  final String info;

  FaceValue({
    required this.isSale,
    required this.name,
    required this.money,
    required this.code,
    required this.level,
    required this.winNum,
    required this.winSide,
    required this.maxPrize,
    required this.maxPrizeName,
    required this.checked,
    required this.bonusLevel,
    required this.info,
  });
}

class BonusLevel {
  final String award;
  final int money;

  BonusLevel({
    required this.award,
    required this.money,
  });
}

class BuyNotice {
  final String name;
  final String content;

  BuyNotice({
    required this.name,
    required this.content,
  });
}

class InfoTxt {
  final String name;
  final String content;

  InfoTxt({
    required this.name,
    required this.content,
  });
}

class RulesTxt {
  final String name;
  final String content;
  final String contentTxt;

  RulesTxt({
    required this.name,
    required this.content,
    required this.contentTxt,
  });
}

final digitalLotteryGgl = [
  DigitalLotteryGgl(
    id: 1,
    name: '中国红',
    type: 24,
    gameCode: 'ggl_zgh',
    release: '国家体育局总局体育彩票管理中心发行',
    faceValue: [
      FaceValue(
        isSale: 1,
        name: "面值5元",
        money: 5,
        code: 'zgh_fi',
        level: 9,
        winNum: 9,
        winSide: '10.11%',
        maxPrize: 100000,
        maxPrizeName: "10万元",
        checked: [false],
        bonusLevel: [
          BonusLevel(award: '1', money: 100000),
          BonusLevel(award: '2', money: 1000),
          BonusLevel(award: '3', money: 500),
          BonusLevel(award: '4', money: 200),
          BonusLevel(award: '5', money: 100),
          BonusLevel(award: '6', money: 50),
          BonusLevel(award: '7', money: 20),
          BonusLevel(award: '8', money: 10),
          BonusLevel(award: '9', money: 5),
        ],
        info: '最高奖金为10万元，共有9次中奖机会',
      ),
      FaceValue(
        isSale: 1,
        name: "面值10元",
        money: 10,
        code: 'zgh_te',
        level: 9,
        winNum: 13,
        winSide: '19.23%',
        maxPrize: 250000,
        maxPrizeName: "25万元",
        checked: [false],
        bonusLevel: [
          BonusLevel(award: '1', money: 250000),
          BonusLevel(award: '2', money: 10000),
          BonusLevel(award: '3', money: 1000),
          BonusLevel(award: '4', money: 500),
          BonusLevel(award: '5', money: 200),
          BonusLevel(award: '6', money: 100),
          BonusLevel(award: '7', money: 50),
          BonusLevel(award: '8', money: 20),
          BonusLevel(award: '9', money: 10),
        ],
        info: '最高奖金为25万元，共有13次中奖机会',
      ),
      FaceValue(
        isSale: 1,
        name: "面值20元",
        money: 20,
        code: 'zgh_tw',
        level: 10,
        winNum: 21,
        winSide: '40%',
        maxPrize: 1000000,
        maxPrizeName: "100万元",
        checked: [false],
        bonusLevel: [
          BonusLevel(award: '1', money: 1000000),
          BonusLevel(award: '2', money: 100000),
          BonusLevel(award: '3', money: 10000),
          BonusLevel(award: '4', money: 5000),
          BonusLevel(award: '5', money: 1000),
          BonusLevel(award: '6', money: 500),
          BonusLevel(award: '7', money: 200),
          BonusLevel(award: '8', money: 50),
          BonusLevel(award: '9', money: 30),
          BonusLevel(award: '10', money: 20),
        ],
        info: '最高奖金为100万元，共有21次中奖机会',
      ),
      FaceValue(
        isSale: 1,
        name: "面值5-10-20元",
        money: '5-10-20',
        code: 'zgh_hh',
        level: 10,
        winNum: 21,
        winSide: '20%',
        maxPrize: 1000000,
        maxPrizeName: "100万元",
        checked: [false],
        bonusLevel: [
          BonusLevel(award: '1', money: 1000000),
          BonusLevel(award: '2', money: 100000),
          BonusLevel(award: '3', money: 10000),
          BonusLevel(award: '4', money: 5000),
          BonusLevel(award: '5', money: 1000),
          BonusLevel(award: '6', money: 500),
          BonusLevel(award: '7', money: 200),
          BonusLevel(award: '8', money: 50),
          BonusLevel(award: '9', money: 30),
          BonusLevel(award: '10', money: 20),
        ],
        info: '最高奖金为100万元，共有21次中奖机会',
      ),
    ],
    buyNotice: BuyNotice(
      name: '购买须知',
      content: '',
    ),
    infoTxt: InfoTxt(
      name: '游戏说明‌',
      content:
          '‌刮刮乐“中国红”‌是一种非常受欢迎的彩票游戏，以其鲜艳的红色和吉祥的象征意义深受购彩者喜爱。‌“中国红”是体彩顶呱刮的经典票种‌，自上市销售以来，一直受到广泛欢迎和追捧，并且中奖记录不断‌',
    ),
    rulesTxt: RulesTxt(
      name: '游戏规则‌',
      content:
          '刮开覆盖膜，如果你的号码中任意一个号码与中奖号码之一相同，即可中得该号码下方所示的金额；如果出现“红”标志，即中得该标志下方所示金额的2倍。如果出现“灯笼”标志，即中得该标志下方所示金额的5倍。如果出现“中国结”标志，即中得刮开区内所示的20个金额之和。如果在幸运奖区出现金额标志，即中得该金额。中奖奖金兼中兼得。不同面值的中奖规则‌：20元面值‌：最高奖金为100万元，共有21次中奖机会，设有10个奖级。10元面值‌：最高奖金为25万元，共有13次中奖机会，设有9个奖级。5元面值‌：最高奖金为10万元，共有9次中奖机会，设有9个奖级。其他信息‌：在线购买刮：本游戏为在线购买刮，需要提供真实奖金。中奖概率与真实彩票不同，请以真实购买彩票为准。',
      contentTxt: '',
    ),
  ),
  DigitalLotteryGgl(
    id: 2,
    name: '中国龙',
    type: 24,
    gameCode: 'ggl_zgl',
    release: '国家体育局总局体育彩票管理中心发行',
    faceValue: [
      FaceValue(
        isSale: 1,
        name: "面值5元",
        money: 5,
        code: 'zgl_fi',
        level: 9,
        winNum: 9,
        winSide: '20%',
        maxPrize: 100000,
        maxPrizeName: "10万元",
        checked: [false],
        bonusLevel: [
          BonusLevel(award: '1', money: 100000),
          BonusLevel(award: '2', money: 1000),
          BonusLevel(award: '3', money: 500),
          BonusLevel(award: '4', money: 200),
          BonusLevel(award: '5', money: 100),
          BonusLevel(award: '6', money: 50),
          BonusLevel(award: '7', money: 20),
          BonusLevel(award: '8', money: 10),
          BonusLevel(award: '9', money: 5),
        ],
        info: '最高奖金为10万元，共有9次中奖机会',
      ),
      FaceValue(
        isSale: 1,
        name: "面值10元",
        money: 10,
        code: 'zgl_te',
        level: 9,
        winNum: 9,
        winSide: '20%',
        maxPrize: 100000,
        maxPrizeName: "25万元",
        checked: [false],
        bonusLevel: [
          BonusLevel(award: '1', money: 250000),
          BonusLevel(award: '2', money: 10000),
          BonusLevel(award: '3', money: 1000),
          BonusLevel(award: '4', money: 500),
          BonusLevel(award: '5', money: 200),
          BonusLevel(award: '6', money: 100),
          BonusLevel(award: '7', money: 50),
          BonusLevel(award: '8', money: 20),
          BonusLevel(award: '9', money: 10),
        ],
        info: '最高奖金为10万元，共有9次中奖机会',
      ),
      FaceValue(
        isSale: 1,
        name: "面值20元",
        money: 10,
        code: 'zgl_tw',
        level: 9,
        winNum: 21,
        winSide: '20%',
        maxPrize: 100000,
        maxPrizeName: "100万元",
        checked: [false],
        bonusLevel: [
          BonusLevel(award: '1', money: 1000000),
          BonusLevel(award: '2', money: 100000),
          BonusLevel(award: '3', money: 10000),
          BonusLevel(award: '4', money: 5000),
          BonusLevel(award: '5', money: 1000),
          BonusLevel(award: '6', money: 500),
          BonusLevel(award: '7', money: 200),
          BonusLevel(award: '8', money: 50),
          BonusLevel(award: '9', money: 30),
          BonusLevel(award: '10', money: 20),
        ],
        info: '最高奖金为10万元，共有21次中奖机会',
      ),
    ],
    buyNotice: BuyNotice(
      name: '购买须知',
      content: '',
    ),
    infoTxt: InfoTxt(
      name: '游戏说明‌',
      content: '',
    ),
    rulesTxt: RulesTxt(
      name: '游戏规则‌',
      content: '',
      contentTxt: '',
    ),
  ),
];

class GGLPreprareParms {
  String? typeCode;

  int? winLevel;
  int? winNum;

  String? maxPrizeName;

  int? maxPrice;
  int? minPrice;

  String? maxBonus;
  String? maxWinMoney;
  List<int>? buyMoney;

  GGLPreprareParms({
    this.typeCode,
    this.winLevel,
    this.winNum,
    this.maxPrizeName,
    this.maxPrice,
    this.minPrice,
    this.maxWinMoney,
    this.buyMoney,
    this.maxBonus,
  });
}

GGLPreprareParms goConfirm(
    GGLList item, List<DigitalLotteryGgl> digitalLotteryGgl) {
  String typeTitle = '${item.lotteryTitle}元';
  int byMoney = item.bonusFace!; //面值
  String gameCode = 'ggl_zgh';

  GGLPreprareParms params = GGLPreprareParms();

  if (digitalLotteryGgl.isNotEmpty) {
    for (var digital in digitalLotteryGgl) {
      if (digital.gameCode == gameCode) {
        for (var faceValue in digital.faceValue) {
          if (faceValue.money == byMoney) {
            params.typeCode = faceValue.code;
            List<BonusLevel> bonusLevel = faceValue.bonusLevel;
            int bonLen = bonusLevel.length - 1;
            params.winLevel = faceValue.level;
            params.winNum = faceValue.winNum;
            params.maxPrizeName = faceValue.maxPrizeName;
            params.maxPrice = bonusLevel[0].money;
            params.minPrice = bonusLevel[bonLen].money;
            break;
          }
        }
      }
    }
  }

  params.maxWinMoney = item.maxBonus;

  params.buyMoney =
      byMoney.toString().split('-').map((e) => int.parse(e)).toList();
  return params;
}

class ChangeBackgroundModel extends ChangeNotifier {
  bool showBackground = false;

  changeBackground() {
    showBackground = !showBackground;
    notifyListeners();
  }
}
