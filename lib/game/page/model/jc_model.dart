import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:sp_util/sp_util.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/page/entity/gjjc_entity.dart';
import 'package:sport_mobile/game/page/entity/game_summary.dart';
import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/base_entity.dart';
import 'package:sport_mobile/net/ws_utils.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/util/other_utils.dart';
import 'package:sport_mobile/net/network_state.dart';

class JCModel extends ChangeNotifier {
  bool isLoad = true;
  NetworkStatus networkStatus = NetworkStatus.networkConnected;
  bool hasMoreData = true;
  bool hasError = false;

  String gameName = "";

  int gameID = MatchDataType.Gj;

  Map<String, JCMatch> selectedMatchList = {};

  List<JCMatch> jcList = [];
  GameSummary gjjcSummary = GameSummary();

  String stopTime = "";
  String termNo = "";
  List<int> chuanList = [];

  Future refreshData() async {
    isLoad = true;

    try {
      var tempList = await getJCList();
      isLoad = false;
      networkStatus = NetworkStatus.networkConnected;

      if (tempList.isEmpty) {
        hasMoreData = false;
      } else {
        jcList = tempList;
        hasMoreData = true;
      }
    } on TimeoutException catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkTimeout;
    } catch (e) {
      isLoad = false;
      networkStatus = NetworkStatus.networkFailed;
    }

    notifyListeners();
  }

  changeSelected(bool joinable, JCMatch match) {
    if (joinable == false) {
      if (selectedMatchList.isNotEmpty) {
        selectedMatchList.remove("${match.boutIndex}");
      }
    } else {
      if (!selectedMatchList.containsKey("${match.boutIndex}")) {
        selectedMatchList["${match.boutIndex}"] = match;
      }
    }
    summaryWithJC();
    notifyListeners();
  }

  void readyCommit() {
    String term = termNo;
    List<String> arr = [];
    List<String> arr1 = [];

    selectedMatchList.forEach((k, match) {
      arr.add('${match.boutIndex}_${match.sp}');
      arr1.add("${match.boutIndex}");
    });

    String codes = 'fs-${arr1.join(",")}-$term';

    String statistics = '$term>${arr.join('/')}';

    gjjcSummary.sp = SPObject();
    gjjcSummary.codes = codes;
    gjjcSummary.statistics = statistics;
    gjjcSummary.gameID = gameID;
    gjjcSummary.chuanTip = generateChuanTips().join(',');
  }

  List<String> generateChuanTips() {
    List<String> chuanTips = [];

    chuanList = [gjjcSummary.totalCount];
    chuanList.forEach((chuan) {
      if (chuan == 1) {
        chuanTips.add('单关');
      } else {
        chuanTips.add("$chuan串1");
      }
    });

    return chuanTips;
  }

  changeBetTimes(int newTimes) {
    gjjcSummary.times = newTimes;
    summaryWithJC();
    notifyListeners();
  }

  void timesIncrement(String currenTimes) {
    int count = int.tryParse(currenTimes) ?? 0;

    count++;
    changeBetTimes(count);
  }

  void timesDecrement(String currenTimes) {
    int count = int.tryParse(currenTimes) ?? 0;
    if (count > 1) {
      count--;
    }
    changeBetTimes(count);
  }

  flushAllSelected() {
    selectedMatchList = {};
    chuanList = [];
    notifyListeners();
  }

  flushExit() {
    selectedMatchList = {};
    gjjcSummary = GameSummary();
    chuanList = [];
    jcList = [];
  }

//计算投注
  void summaryWithJC() {
    if (selectedMatchList.isEmpty) {
      return;
    }

    gjjcSummary.totalCount = selectedMatchList.length;

    List<double> ads = [];

    selectedMatchList.forEach((key, jcMatch) {
      ads.add(double.parse(jcMatch.sp!));
    });

    double _max = ads.reduce(max);
    double _min = ads.reduce(min);

    gjjcSummary.totalAmount = gjjcSummary.totalCount * gjjcSummary.times * 2;
    gjjcSummary.maxAmount = _max * gjjcSummary.times * 2;
    gjjcSummary.minAmount = _min * gjjcSummary.times * 2;
    gjjcSummary.cnname = OrderType.values[gameID]?.cnname;
    gjjcSummary.enname = OrderType.values[gameID]?.enname;
    gjjcSummary.gameID = gameID;
  }

  //获取竞猜赛事
  Future getJCList() async {
    String? token = SpUtil.getString(Constant.token);

    var parameters = {
      'version': Constant.version,
      'token': "$token",
    };

    String form = Utils.encodeParams(parameters);

    String api = gameName == "冠军" ? Api.getGJJCList : Api.getGYJJCList;

    BaseRequestEntity baseRequest = BaseRequestEntity(api, form);
    Map<String, dynamic> requestDataMap = baseRequest.toJson();
    var respStr = await WsUtils.instance
        .sendMessageAndWaitForResponse(jsonEncode(requestDataMap),showProgress: false);

    var dataList = jsonDecode(respStr);
    var baseRes = BaseEntity.fromJson(dataList);

    if (baseRes.status != "_0000") {
      hasError = true;
      return [];
    } else {
      hasError = false;
    }

    GJJCEntity gjjcEntity = GJJCEntity.fromJson(dataList);
    List<JCMatch> matchList = gjjcEntity.list!;

    stopTime = gjjcEntity.stopTime ?? "";

    if (matchList.isNotEmpty) {
      termNo = matchList.first.termNo ?? "";
    }

    return matchList;
  }
}
