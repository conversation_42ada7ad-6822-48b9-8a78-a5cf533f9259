
class ChuanSelect {
  late String name;
  late bool select;
  late int value;
  ChuanSelect(this.name, this.select, this.value);

  ChuanSelect.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    select = json['select'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = name;
    data['select'] = select;
    data['value'] = value;
    return data;
  }
}