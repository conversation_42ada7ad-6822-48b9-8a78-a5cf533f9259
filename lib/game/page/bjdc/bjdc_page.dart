import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/model/game_menu_controller.dart';
import 'package:sport_mobile/game/page/model/bjdc_model.dart';
import 'package:sport_mobile/game/page/widgets/bjdc_leagues_filter.dart';
import 'package:sport_mobile/game/page/widgets/bjdc_list_refresh.dart';
import 'package:sport_mobile/game/page/widgets/jczq_app_bar.dart';
import 'package:sport_mobile/game/page/widgets/toogle_menu.dart';
import 'package:sport_mobile/game/routers/game_router.dart';
import 'package:sport_mobile/order/order_router.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/util/toast_utils.dart';
import 'package:sport_mobile/drop_down_menu/flutter_down_menu.dart';

class BJDCPage extends StatefulWidget {
  const BJDCPage({super.key});

  @override
  _BJDCPageState createState() {
    return _BJDCPageState();
  }
}

class _BJDCPageState extends State<BJDCPage> {
  final GameMenuController gameMenuController = GameMenuController();
  GameMenuController sportMenuController = GameMenuController();

  final List<FilterBean> list1 = [];
  final List<FilterBean> list2 = [];
  final List<FilterBean> list3 = [];

  bool isMenuVisible = false;
  bool isDropDown = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _showLeagueFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      builder: (BuildContext context) {
        return const BJDCLeaguesFilter();
      },
    );
  }

  List<Menu> buildFTMatchTypeList() {
    List<Menu> _menuList = [];

    BJDCModel model = Provider.of<BJDCModel>(context, listen: false);

    // if (model.typeKey != "") {
    //   sportMenuController.title = model.typeTitle;
    //   sportMenuController.currentKey = model.typeKey;
    // } else {
    //   model.typeKey = 'spf';
    //   model.typeTitle = '胜平负';
    //   sportMenuController.title = '胜平负';
    //   sportMenuController.currentKey = 'spf';
    // }

    sportMenuController.change(model.cate);

    BJDCCollections.toList().forEach((Cate item) {
      Menu _tmp = Menu(item.typeTile!, () {
        sportMenuController.change(item);
        model.selectMatchMap(item.typeKey!);
        model.cate = item;
      }, gameKey: item.typeKey!);
      _menuList.add(_tmp);
    });

    // BJDCMatchTypes.values.forEach((key, item) {
    //   Menu _tmp = Menu(item.name, () {
    //     model.typeTitle = item.name;

    //     sportMenuController.changeType(key);
    //     sportMenuController.changeTitle(item.name);
    //     model.selectMatchMap(key);
    //     model.typeKey = key;
    //   }, gameKey: key);

    //   _menuList.add(_tmp);
    // });

    return _menuList;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BJDCModel>(builder: (context, model, child) {
      return PopScope(
          canPop: true,
          onPopInvoked: (_) async {
            model.cate = BJDCCollections.spf;
            model.flushExit();
          },
          child: Scaffold(
              backgroundColor: const Color(0xFFf2f2f2),
              appBar: JCZQAppBar(
                sportMenuController,
                gameMenuController,
                [
                  IconButton(
                      onPressed: () {
                        _showLeagueFilterBottomSheet(context);
                      },
                      icon: const Icon(
                        Icons.filter_alt_off_outlined,
                        color: Color(0xFFFFFFFF),
                        size: 20,
                      )),
                  IconButton(
                      padding: const EdgeInsets.all(0),
                      constraints: const BoxConstraints(),
                      onPressed: () {
                        if (gameMenuController.isShow) {
                          gameMenuController.hide();
                        } else {
                          gameMenuController.show();
                        }
                      },
                      icon: const Icon(
                        Icons.more_horiz,
                        color: Color(0xFFFFFFFF),
                        size: 20,
                      ))
                ],
                key: Key('BJDCAppBar'),
                // onBackPressed: () {
                //   model.flushExit();
                // },
              ),
              body: Stack(
                children: [
                  Container(
                    child: Column(
                      children: [
                        Expanded(
                          child: BJDCListRefresh(
                            model.gameID,
                            key: Key("bjdcList"),
                          ),
                        ),
                        Consumer<BJDCModel>(builder: (context, model, child) {
                          return Container(
                            color: Colors.white,
                            height: 65,
                            //     padding: EdgeInsets.symmetric(horizontal: 20,vertical: 10),
                            width: double.infinity,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: GestureDetector(
                                      onTap: () {
                                        model.flushAllSelected();
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        child: Text("清空"),
                                      )),
                                ),
                                Expanded(
                                    flex: 3,
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text.rich(
                                          TextSpan(
                                            text: "已经选择 ",
                                            style: TextStyles.textBold14,
                                            children: <TextSpan>[
                                              TextSpan(
                                                text:
                                                    "${model.summary.selectMatchCount}",
                                                style: const TextStyle(
                                                  color: Colors.red,
                                                  fontSize: Dimens.font_sp16,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              const TextSpan(
                                                text: " 场比赛",
                                              ),
                                            ],
                                          ),
                                        ),
                                        if (model.isChuan &&
                                            model.cate.typeKey != 'sfgg')
                                          const Text(
                                            "请至少选择两场比赛",
                                            style: TextStyles.textGray12,
                                          ),
                                        if (model.isChuan &&
                                            model.cate.typeKey == 'sfgg')
                                          const Text(
                                            "请至少选择3场比赛",
                                            style: TextStyles.textGray12,
                                          ),
                                      ],
                                    )),
                                Expanded(
                                    flex: 2,
                                    child: GestureDetector(
                                      onTap: () {
                                        int count = 0;
                                        model.selectedMatches
                                            .forEach((key, _match) {
                                          if (_match.bjdcState!.hasSelect()) {
                                            count += 1;
                                          }
                                        });

                                        if (count > 15) {
                                          Toast.show('最多选择15场比赛');
                                          return;
                                        }

                                        if (model.isChuan) {
                                          if (count < 3 &&
                                              model.cate.typeKey == 'sfgg') {
                                            Toast.show('至少选择3场比赛',
                                                position: "center");

                                            return;
                                          } else if (count < 2) {
                                            Toast.show('至少选择2场比赛',
                                                position: "center");
                                            return;
                                          }
                                        } else {
                                          if (count < 1) {
                                            //单关
                                            Toast.show('请选择比赛',
                                                position: "center");
                                            return;
                                          }
                                        }
                                        //进行confirm页准备

                                        model.chuanSelectList = [];
                                        model.updateMatchList();

                                        model.DGItemAdd();

                                        NavigatorUtils.pushResult(
                                            context, GameRouter.bjdcConfirmPage,
                                            (obj) {
                                          model.refreshData();
                                        });
                                      },
                                      child: Container(
                                        color: Colors.red,
                                        alignment: Alignment.center,
                                        child: const Text(
                                          "选好了",
                                          style: TextStyle(
                                              fontSize: Dimens.font_sp18,
                                              color: Colors.white),
                                        ),
                                      ),
                                    )),
                              ],
                            ),
                          );
                        })
                      ],
                    ),
                  ),
                  ToogleMenu(
                    [
                      Menu("投注记录", () {
                        NavigatorUtils.push(
                            context, '${OrderRouter.orderPage}?action=all');
                      },
                          customIcon: const Icon(
                            Icons.description,
                            color: Color(0xFFfd4627),
                          )),
                      Menu("玩法介绍", () {
                        NavigatorUtils.push(context,
                            "${GameRouter.gameHelperPage}/${model.gameID}");
                      },
                          customIcon: const Icon(
                            Icons.error,
                            color: Color(0xFFfd4627),
                          )),
                    ],
                    isMenuVisible,
                    100,
                    gameMenuController,
                    singleItem: false,
                    key: const Key('ft_helper'),
                  ),
                  ToogleMenu(
                    buildFTMatchTypeList(),
                    isMenuVisible,
                    210,
                    sportMenuController,
                    singleItem: true,
                    key: const Key('ft_cate'),
                  ),
                ],
              )));
    });
  }
}
