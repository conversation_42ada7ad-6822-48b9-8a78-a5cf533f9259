import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/page/entity/jclq_entity.dart';
import 'package:sport_mobile/game/page/model/basketball_model.dart';
import 'package:sport_mobile/game/page/widgets/basketball_ht_bottom_sheet.dart';
import 'package:sport_mobile/game/page/widgets/ft_button_item.dart';
import 'package:sport_mobile/game/page/widgets/ft_custom_expansion_tile.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/widgets/load_image.dart';

class BasketballMatchRowItem extends StatefulWidget {
  final Map<String, List<JCLQEntity>> match;

  final String gameType;

  final bool isConfirmPage;

  final bool isSingle;

  const BasketballMatchRowItem(this.match, this.gameType,
      {super.key, this.isConfirmPage = false, this.isSingle = false});

  @override
  _BasketballMatchRowItemState createState() => _BasketballMatchRowItemState();
}

class _BasketballMatchRowItemState extends State<BasketballMatchRowItem> {
  late List<Widget> list;

  late BasketballModel provider;

  @override
  void initState() {
    super.initState();
  }

  void _showBottomSheet(BuildContext context, JCLQEntity e) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      builder: (BuildContext context) {
        return BasketballHTBottomSheet(
          e,
          widget.isSingle,
          key: const Key('basketballHTBottomSheet'),
        );
      },
    );
  }

  String generateConcedeTitle(double concede) {
    if (concede > 0) {
      return "+ ${concede.toString()}";
    } else if (concede < 0) {
      return concede.toString();
    } else {
      return "-";
    }
  }

  List<Widget> buildMatchItem(List<JCLQEntity> list) {
    return list.map((e) {
      return Container(
        decoration: const BoxDecoration(
            border: Border(
          bottom: BorderSide(
            color: Color(0xFFd9d9d9),
            width: 1,
          ),
        )),
        child: Stack(
          children: [
            Container(
              padding: const EdgeInsets.only(
                  left: 10, right: 10, top: 5, bottom: 10),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text.rich(
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                          TextSpan(
                            text: "${e.awayTeam}",
                            style: TextStyles.textSize14,
                            children: const <TextSpan>[],
                          ),
                        ),
                        const LoadAssetImage(
                          'games/vs',
                          width: 17,
                          height: 11,
                          fit: BoxFit.contain,
                        ),
                        Text.rich(
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                          TextSpan(
                            text: " ${e.homeTeam}",
                            style: TextStyles.textSize14,
                            children: const <TextSpan>[
                              TextSpan(
                                text: "【主】",
                                style: TextStyle(
                                  color: Color(0xFFa7a7a7),
                                  fontSize: Dimens.font_sp14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        margin: EdgeInsets.only(right: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "${e.league}",
                              style: TextStyles.textSize14,
                            ),
                            Text(
                              "${e.weak}",
                              style: TextStyles.textGray12,
                            ),
                            Text(
                               "${e.stopSaleShortTime}",
                                style: e.stopSaleStatus == 0 ? TextStyles.textGray12 : TextStyles.textRed12,
                              ),
                         
                          ],
                        ),
                      ),

                      //表格

                      Expanded(
                          child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: const Color(0xFFd9d9d9),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                                flex: 6,
                                child: Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Expanded(
                                            flex: 1,
                                            child: Container(
                                              height: 35,
                                              alignment: Alignment.center,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 0,
                                                      vertical: 0),
                                              decoration: const BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.only(
                                                    topLeft:
                                                        Radius.circular(10),
                                                  ),
                                                  color: Color(0xFFdce0ec)),
                                              child: const Text(
                                                "0",
                                                style: TextStyles.textSize12,
                                              ),
                                            )),
                                        Expanded(
                                            flex: 5,
                                            child: FTButtonItem(
                                                "负", "${e.oddsData?.sf[1]}",
                                                isActived: e.basketballState!
                                                    .sf[1], onPressed: () {
                                              provider.fillUpActiveMatchItem(
                                                  e, "sf", 1);
                                            },
                                                key: const Key("sf1"),
                                                isOverTime: e.stopSaleStatus == 1 ?  true  : false,

                                                isAvailable:
                                                    e.canSF(widget.isSingle))),
                                        const Expanded(
                                            flex: 5,
                                            child: FTButtonItem("-", "",
                                                isActived: false,
                                                onPressed: null,
                                                isAvailable: false)),
                                        Expanded(
                                            flex: 5,
                                            child: FTButtonItem(
                                                "胜", "${e.oddsData?.sf[0]}",
                                                key: const Key("sf0"),
                                                isActived: e.basketballState!
                                                    .sf[0], onPressed: () {
                                              provider.fillUpActiveMatchItem(
                                                  e, "sf", 0);
                                                  
                                            },
                                                isOverTime: e.stopSaleStatus == 1 ?  true  : false,

                                                isAvailable:
                                                    e.canSF(widget.isSingle))),
                                      ],
                                    ),
                                    Container(
                                      height: 1,
                                      width: double.infinity,
                                      color: Color(0xFFdcdcdc),
                                    ),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Expanded(
                                            flex: 1,
                                            child: Container(
                                              height: 35,
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      const BorderRadius.only(
                                                    bottomLeft:
                                                        Radius.circular(10),
                                                  ),
                                                  color: e.concede! > 0
                                                      ? Color(0xFFff664c)
                                                      : Color(0xFF688ce8)),
                                              child: Text(
                                                "让",
                                                style: e.concede! > 0
                                                    ? TextStyles.textWhite12
                                                    : TextStyles.textWhite12,
                                              ),
                                            )),
                                        Expanded(
                                            flex: 5,
                                            child: FTButtonItem(
                                                "负", "${e.oddsData?.rfsf[1]}",
                                                isActived: e.basketballState!
                                                    .rfsf[1], onPressed: () {
                                              provider.fillUpActiveMatchItem(
                                                  e, "rfsf", 1);
                                            },
                                                isOverTime: e.stopSaleStatus == 1 ?  true  : false,

                                                isAvailable: e
                                                    .canRFSF(widget.isSingle))),
                                        Expanded(
                                          flex: 5,
                                          child: CommonColorButtonItem(
                                            generateConcedeTitle(e.concede!),
                                            rise: e.concede! > 0 ? true : false,
                                          ),
                                        ),
                                        Expanded(
                                            flex: 5,
                                            child: FTButtonItem(
                                                "胜", "${e.oddsData?.rfsf[0]}",
                                                isActived: e.basketballState!
                                                    .rfsf[0], onPressed: () {
                                              provider.fillUpActiveMatchItem(
                                                  e, "rfsf", 0);
                                            },
                                                isOverTime: e.stopSaleStatus == 1 ?  true  : false,

                                                isAvailable: e
                                                    .canRFSF(widget.isSingle))),
                                      ],
                                    ),
                                  ],
                                )),
                            Expanded(
                                flex: 1,
                                child: GestureDetector(
                                  onTap: () {
                                    _showBottomSheet(context, e);
                                  },
                                  child: Container(
                                    height: 71,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 2),
                                    decoration: BoxDecoration(
                                        color: e.basketballState!
                                                    .getMoreSelectCount() >
                                                0
                                            ? Color(0xFFFD4630)
                                            : Colors.white,
                                        borderRadius: const BorderRadius.only(
                                            topRight: Radius.circular(10),
                                            bottomRight: Radius.circular(10))),
                                    child: Text(
                                      e.basketballState!.getMoreSelectCount() >
                                              0
                                          ? "已选${e.basketballState?.getMoreSelectCount()}项"
                                          : "全部玩法",
                                      textAlign: TextAlign.center,
                                      style: e.basketballState!
                                                  .getMoreSelectCount() >
                                              0
                                          ? TextStyles.textWhite14
                                          : TextStyles.textSize14,
                                    ),
                                  ),
                                )),
                          ],
                        ),
                      ))
                    ],
                  ))
                ],
              ),
            ),
            e.canSingle()
                ? const Positioned(
                    top: 0,
                    child: LoadAssetImage(
                      "bet/single",
                      height: 30,
                    ))
                : const SizedBox.shrink(),
          ],
        ),
      );
    }).toList();
  }

  List<Widget> buildList() {
    list = [];
    widget.match.forEach((matchName, value) {
      Widget _item = Expanded(
          child: Theme(
              data: ThemeData().copyWith(dividerColor: Colors.transparent),
              child: FTCustomExpansionTile(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                title: Text(
                  '$matchName ${value.length}场比赛',
                  style: TextStyles.textSize12,
                ),
                children: buildMatchItem(value),
              )));

      list.add(_item);
    });

    return list;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BasketballModel>(builder: (context, model, child) {
      provider = model;
      return Container(
        decoration: const BoxDecoration(),
        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: buildList(),
        ),
      );
    });
  }
}

class FTMatchItem extends StatelessWidget {
  final String? team;
  final String? vef;
  final bool? isSelected;
  VoidCallback? onPressed;

  FTMatchItem(
      {this.team, this.vef, this.isSelected, this.onPressed, super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
        child: GestureDetector(
      onTap: onPressed,
      child: Container(
          color: isSelected == true ? Colors.red : Colors.white,
          margin: const EdgeInsets.only(right: 1),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "${team}",
                style: TextStyle(
                    fontSize: Dimens.font_sp14,
                    height: 1.5,
                    color: isSelected == true ? Colors.white : Colors.black,
                    fontWeight: FontWeight.bold),
              ),
              Text(
                "$vef",
                style: isSelected == true
                    ? TextStyles.textWhite12
                    : TextStyles.textGray12,
              ),
            ],
          )),
    ));
  }
}
