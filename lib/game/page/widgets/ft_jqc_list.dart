import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/page/entity/jczq_entity.dart';
import 'package:sport_mobile/game/page/model/ft_model.dart';
import 'package:sport_mobile/game/page/widgets/ft_button_item.dart';
import 'package:sport_mobile/game/page/widgets/ft_custom_expansion_tile.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/widgets/load_image.dart';

class FTJQSList extends StatefulWidget {
  final Map<String, List<JCZQEntity>> match;

  final String gameType;

  final bool isConfirmPage;

  const FTJQSList(this.match, this.gameType,
      {super.key, this.isConfirmPage = false});

  @override
  _FTJQSListState createState() => _FTJQSListState();
}

class _FTJQSListState extends State<FTJQSList> {
  late List<Widget> list;
  late FTModel provider;

  @override
  void initState() {
    super.initState();
  }

  List<Widget> buildMatchItem(List<JCZQEntity> list) {
    return list.map((e) {
      return Container(
          decoration: const BoxDecoration(
              border: Border(
            bottom: BorderSide(
              color: Color(0xFFd9d9d9),
              width: 1,
            ),
          )),
          child: Stack(
            children: [
              Container(
                padding: const EdgeInsets.only(
                    left: 15, right: 15, top: 5, bottom: 10),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "${e.league}",
                            style: TextStyles.textSize12,
                          ),
                          Expanded(
                              flex: 1,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text.rich(
                                    textAlign: TextAlign.center,
                                    overflow: TextOverflow.ellipsis,
                                    TextSpan(
                                      text: "${e.homeTeam}",
                                      style: TextStyles.textSize14,
                                      children: const <TextSpan>[
                                        TextSpan(
                                          text: "【主】",
                                          style: TextStyle(
                                            color: Color(0xFFa7a7a7),
                                            fontSize: Dimens.font_sp12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const LoadAssetImage(
                                    'games/vs',
                                    width: 17,
                                    height: 11,
                                    fit: BoxFit.contain,
                                  ),
                                  Text.rich(
                                    textAlign: TextAlign.center,
                                    overflow: TextOverflow.ellipsis,
                                    TextSpan(
                                      text: " ${e.awayTeam}",
                                      style: TextStyles.textSize14,
                                      children: <TextSpan>[],
                                    ),
                                  ),
                                ],
                              ))
                        ],
                      ),
                    ),
                    Container(
                        child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          margin: EdgeInsets.only(right: 10),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${e.weak}",
                                style: TextStyles.textGray12,
                              ),
                              Text(
                                "${e.stopSaleShortTime}",
                                style: e.stopSaleStatus == 0
                                    ? TextStyles.textGray12
                                    : TextStyles.textRed12,
                              ),
                            ],
                          ),
                        ),

                        //表格

                        Expanded(
                            child: Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Color(0xFFd9d9d9),
                                    width: 1, // 边框宽度为1像素
                                  ),
                                  borderRadius: BorderRadius.circular(10.0),
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                        flex: 1,
                                        child: Container(
                                          height: 71,
                                          width: double.infinity,
                                          alignment: Alignment.center,
                                          decoration: const BoxDecoration(
                                              color: Color(0xFF698ce8),
                                              borderRadius: BorderRadius.only(
                                                  topLeft: Radius.circular(10),
                                                  bottomLeft:
                                                      Radius.circular(10))),
                                          child: const Text(
                                            "进球数",
                                            textAlign: TextAlign.center,
                                            style: TextStyles.textWhite12,
                                          ),
                                        )),
                                    Expanded(
                                        flex: 18,
                                        child: Container(
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                  flex: 6,
                                                  child: Column(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Row(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          Expanded(
                                                              flex: 4,
                                                              child:
                                                                  FTButtonItem(
                                                                "0  ",
                                                                "${e.oddsData?.jqs[0]}",
                                                              
                                                                isActived: e
                                                                        .ftStateStore
                                                                        ?.jqs[0] ??
                                                                    false,
                                                                    
                                                                onPressed: () {
                                                                  provider
                                                                      .fillUpActiveMatchItem(
                                                                          e,
                                                                          "jqs",
                                                                          0);
                                                                },
                                                              isOverTime: e.stopSaleStatus == 1 ?  true  : false,

                                                              )),
                                                          Expanded(
                                                              flex: 4,
                                                              child:
                                                                  FTButtonItem(
                                                                "1  ",
                                                                "${e.oddsData?.jqs[1]}",
                                                                isOverTime:
                                                                    e.stopSaleStatus ==
                                                                            1
                                                                        ? true
                                                                        : false,
                                                                isActived: e
                                                                        .ftStateStore
                                                                        ?.jqs[1] ??
                                                                    false,
                                                                onPressed: () {
                                                                  provider
                                                                      .fillUpActiveMatchItem(
                                                                          e,
                                                                          "jqs",
                                                                          1);
                                                                },
                                                              )),
                                                          Expanded(
                                                              flex: 4,
                                                              child:
                                                                  FTButtonItem(
                                                                "2  ",
                                                                "${e.oddsData?.jqs[2]}",
                                                                isOverTime:
                                                                    e.stopSaleStatus ==
                                                                            1
                                                                        ? true
                                                                        : false,
                                                                isActived: e
                                                                        .ftStateStore
                                                                        ?.jqs[2] ??
                                                                    false,
                                                                onPressed: () {
                                                                  provider
                                                                      .fillUpActiveMatchItem(
                                                                          e,
                                                                          "jqs",
                                                                          2);
                                                                },
                                                              )),
                                                          Expanded(
                                                              flex: 4,
                                                              child: ClipRRect(
                                                                  borderRadius:
                                                                      const BorderRadius
                                                                          .only(
                                                                    topRight: Radius
                                                                        .circular(
                                                                            10),
                                                                  ),
                                                                  child:
                                                                      FTButtonItem(
                                                                    "3 ",
                                                                    "${e.oddsData?.jqs[3]}",
                                                                    isOverTime:
                                                                        e.stopSaleStatus ==
                                                                                1
                                                                            ? true
                                                                            : false,
                                                                    isActived: e
                                                                            .ftStateStore
                                                                            ?.jqs[3] ??
                                                                        false,
                                                                    FTBorder: Border.all(
                                                                        width:
                                                                            0,
                                                                        color: Colors
                                                                            .transparent),
                                                                    onPressed:
                                                                        () {
                                                                      provider.fillUpActiveMatchItem(
                                                                          e,
                                                                          "jqs",
                                                                          3);
                                                                    },
                                                                  ))),
                                                        ],
                                                      ),
                                                      Container(
                                                        height: 1,
                                                        width: double.infinity,
                                                        color:
                                                            Color(0xFFdcdcdc),
                                                      ),
                                                      Row(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Expanded(
                                                              flex: 4,
                                                              child:
                                                                  FTButtonItem(
                                                                "4  ",
                                                                "${e.oddsData?.jqs[4]}",
                                                                isOverTime:
                                                                    e.stopSaleStatus ==
                                                                            1
                                                                        ? true
                                                                        : false,
                                                                isActived: e
                                                                        .ftStateStore
                                                                        ?.jqs[4] ??
                                                                    false,
                                                                onPressed: () {
                                                                  provider
                                                                      .fillUpActiveMatchItem(
                                                                          e,
                                                                          "jqs",
                                                                          4);
                                                                },
                                                              )),
                                                          Expanded(
                                                              flex: 4,
                                                              child:
                                                                  FTButtonItem(
                                                                "5  ",
                                                                "${e.oddsData?.jqs[5]}",
                                                                isOverTime:
                                                                    e.stopSaleStatus ==
                                                                            1
                                                                        ? true
                                                                        : false,
                                                                isActived: e
                                                                        .ftStateStore
                                                                        ?.jqs[5] ??
                                                                    false,
                                                                onPressed: () {
                                                                  provider
                                                                      .fillUpActiveMatchItem(
                                                                          e,
                                                                          "jqs",
                                                                          5);
                                                                },
                                                              )),
                                                          Expanded(
                                                              flex: 4,
                                                              child:
                                                                  FTButtonItem(
                                                                "6  ",
                                                                "${e.oddsData?.jqs[6]}",
                                                                isOverTime:
                                                                    e.stopSaleStatus ==
                                                                            1
                                                                        ? true
                                                                        : false,
                                                                isActived: e
                                                                        .ftStateStore
                                                                        ?.jqs[6] ??
                                                                    false,
                                                                onPressed: () {
                                                                  provider
                                                                      .fillUpActiveMatchItem(
                                                                          e,
                                                                          "jqs",
                                                                          6);
                                                                },
                                                              )),
                                                          Expanded(
                                                              flex: 4,
                                                              child: ClipRRect(
                                                                borderRadius:
                                                                    const BorderRadius
                                                                        .only(
                                                                  bottomRight: Radius
                                                                      .circular(
                                                                          10),
                                                                ),
                                                                child: FTButtonItem(
                                                                    "7 + ",
                                                                    "${e.oddsData?.jqs[7]}",
                                                                    isOverTime:
                                                                        e.stopSaleStatus == 1
                                                                            ? true
                                                                            : false,
                                                                    isActived:
                                                                        e.ftStateStore?.jqs[7] ??
                                                                            false,
                                                                    FTBorder: Border.all(
                                                                        width:
                                                                            0,
                                                                        color: Colors
                                                                            .transparent),
                                                                    onPressed:
                                                                        () {
                                                                  provider
                                                                      .fillUpActiveMatchItem(
                                                                          e,
                                                                          "jqs",
                                                                          7);
                                                                }),
                                                              )),
                                                        ],
                                                      ),
                                                    ],
                                                  )),
                                            ],
                                          ),
                                        ))
                                  ],
                                )))
                      ],
                    ))
                  ],
                ),
              ),
              e.canSingle()
                  ? const Positioned(
                      top: 0,
                      child: LoadAssetImage(
                        "bet/single",
                        height: 30,
                      ))
                  : const SizedBox.shrink(),
            ],
          ));
    }).toList();
  }

  List<Widget> buildList() {
    list = [];
    widget.match.forEach((matchName, value) {
      Widget _item = Expanded(
          child: Theme(
              data: ThemeData().copyWith(dividerColor: Colors.transparent),
              child: FTCustomExpansionTile(
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                title: Text(
                  '$matchName ',
                  style: TextStyles.textSize12,
                ),
                children: buildMatchItem(value),
              )));

      list.add(_item);
    });

    return list;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<FTModel>(builder: (context, model, child) {
      provider = model;

      return Container(
        decoration: const BoxDecoration(),
        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: buildList(),
        ),
      );
    });
  }
}

class FTMatchItem extends StatelessWidget {
  final String? team;
  final String? vef;
  final bool? isSelected;
  VoidCallback? onPressed;

  FTMatchItem(
      {this.team, this.vef, this.isSelected, this.onPressed, super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
        child: GestureDetector(
      onTap: onPressed,
      child: Container(
          color: isSelected == true ? Colors.red : Colors.white,
          margin: const EdgeInsets.only(right: 1),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "${team}",
                style: TextStyle(
                    fontSize: Dimens.font_sp14,
                    height: 1.5,
                    color: isSelected == true ? Colors.white : Colors.black,
                    fontWeight: FontWeight.bold),
              ),
              Text(
                "$vef",
                style: isSelected == true
                    ? TextStyles.textWhite12
                    : TextStyles.textGray12,
              ),
            ],
          )),
    ));
  }
}
