import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/page/entity/bjdc_entity.dart';
import 'package:sport_mobile/game/page/model/bjdc_model.dart';
import 'package:sport_mobile/game/page/widgets/bjdc_bf_bottom_sheet.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/widgets/load_image.dart';

class BJDCBFConfirm extends StatefulWidget {
  @override
  _BJDCBFConfirmState createState() {
    return _BJDCBFConfirmState();
  }

  const BJDCBFConfirm({
    super.key,
  });
}

class _BJDCBFConfirmState extends State<BJDCBFConfirm> {
  late BJDCModel provider;

  late List<Widget> list;

  

  hasSelect(BJDCEntity match) {
    int count = match.bjdcState!.cbf.length;
    for (int i = 0; i < count; i++) {
      if (match.bjdcState!.cbf[i]) {
        return false;
      }
    }
    return true;
  }

  buildSelectText(BJDCEntity match) {
    List<String> selectList = [];
    int count = match.bjdcState!.cbf.length;
    for (int i = 0; i < count; i++) {
      if (match.bjdcState!.cbf[i]) {
        selectList.add(BJDCMatchTip.cbf[i]);
      }
    }

    return selectList.join(' ');
  }
 

  void _showBottomSheet(BuildContext context, BJDCEntity e) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      builder: (BuildContext context) {
        return BJDCBFBottomSheet(e);
      },
    ).then((value) {
        if (value == null || value == 1) {
        provider.updateMatchList();
        provider.DGItemAdd();
        provider.notifyListeners();
      }

    });
  }

  Widget buildMatchItem() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: provider.confirmMatchList.length,
        itemBuilder: (BuildContext context, index) {
          BJDCEntity? e = provider.confirmMatchList[index];

          return Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                    border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFdcdcdc),
                    width: 1,
                  ),
                )),
                padding: const EdgeInsets.only(
                    left: 10, right: 10, top: 5, bottom: 10),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 1,
                              child: Row(
                                children: [
                                  Text(
                                    "${e.weak}",
                                    style: TextStyles.textGray12,
                                  ),
                                  Text(
                                    "${e.league}",
                                    style: TextStyles.textGray12,
                                  ),
                                ],
                              )),
                          Expanded(
                              flex: 3,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text.rich(
                                    overflow: TextOverflow.ellipsis,
                                    TextSpan(
                                      text: "${e.homeTeam}",
                                      style: TextStyles.textSize14,
                                      children: const <TextSpan>[
                                        TextSpan(
                                          text: "【主】",
                                          style: TextStyle(
                                            color: Color(0xFFa7a7a7),
                                            fontSize: Dimens.font_sp12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const LoadAssetImage(
                                    'games/vs',
                                    width: 17,
                                    height: 11,
                                    fit: BoxFit.contain,
                                  ),
                                  Text.rich(
                                    //    textAlign: TextAlign.center,
                                    overflow: TextOverflow.ellipsis,
                                    TextSpan(
                                      text: " ${e.awayTeam}",
                                      style: TextStyles.textSize14,
                                    ),
                                  ),
                                ],
                              ))
                        ],
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            flex: 1,
                            child: InkWell(
                              onTap: () {
                                e.resetCheckState();
                                provider.itemMatchUpdate(e);
                              },
                              child: Container(
                                alignment: Alignment.centerLeft,
                                child: const Icon(
                                  Icons.cancel,
                                  color: Color(0xFFcccccc),
                                  size: 30,
                                ),
                              ),
                            )),

                         Expanded(

                          flex: 5,
                            child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: const Color(0xFFd9d9d9),
                              width: 1, // 边框宽度为1像素
                            ),
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          child: InkWell(
                            onTap: () {
                            _showBottomSheet(context, e);
                            },
                            child: Container(
                                width: double.infinity,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: hasSelect(e) == false
                                      ? const Color(0xFFfd4627)
                                      : Colors.white,
                                  borderRadius: BorderRadius.circular(10.0),
                                ),
                                height: 35,
                                //    padding: EdgeInsets.symmetric(vertical: 10),
                                   child: Text(
                                  hasSelect(e)
                                      ? "点击展开比分投注"
                                      : buildSelectText(e),
                                  overflow: TextOverflow.ellipsis,
                                  style: hasSelect(e) == false
                                      ? TextStyles.textWhite14
                                      : TextStyles.textSize14,
                                )
                                ),
                          ),
                        ))
                      ],
                    )
                  ],
                ),
              ),
              e.canSingle()
                  ? const Positioned(
                      top: 0,
                      child: LoadAssetImage(
                        "bet/single",
                        height: 30,
                      ))
                  : const SizedBox.shrink(),
            ],
          );
        });
  }


  @override
  Widget build(BuildContext context) {
    return Consumer<BJDCModel>(builder: (context, model, child) {
      provider = model;

      return SingleChildScrollView(
        child: Container(
          decoration: const BoxDecoration(),
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
          child: Column(
            children: [
              buildMatchItem(),
              Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(color: Colors.white),
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop(true);
                    },
                    child: Container(
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.add,
                              color: Color(0xFFa0a0a0),
                              size: Dimens.font_sp20,
                            ),
                            Text.rich(
                              TextSpan(
                                text: "继续添加比赛",
                                style:
                                    const TextStyle(fontSize: Dimens.font_sp14),
                                children: <TextSpan>[
                                  TextSpan(
                                    text:
                                        " (已选${model.summary.selectMatchCount}场) ",
                                    style: const TextStyle(
                                      color: Color(0xFFa0a0a0),
                                      fontSize: Dimens.font_sp12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        )),
                  ))
            ],
          ),
        ),
      );
    });
  }
}
