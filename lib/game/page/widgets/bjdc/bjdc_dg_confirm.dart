

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/data/bet_commit.dart';
import 'package:sport_mobile/game/page/entity/bjdc_entity.dart';
import 'package:sport_mobile/game/page/model/bjdc_model.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/util/toast_utils.dart';
import 'package:sport_mobile/widgets/load_image.dart';

class BJDCDGConfirm extends StatefulWidget {
  @override
  _BJDCConfirmState createState() {
    return _BJDCConfirmState();
  }

  const BJDCDGConfirm({
    super.key,
  });
}

class _BJDCConfirmState extends State<BJDCDGConfirm> {
  late BJDCModel provider;

  List<BetTouzhuItem> touzhuItemList = [];


  Widget buildMatchItem() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: provider.confirmMatchList.length,
        itemBuilder: (BuildContext context, index) {
          BJDCEntity? e = provider.confirmMatchList[index];

          return Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                    border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFdcdcdc),
                    width: 1,
                  ),
                )),
                padding: const EdgeInsets.only(
                    left: 15, right: 15, top: 5, bottom: 10),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 1,
                              child: Row(
                                children: [
                                  Text(
                                    "${e.weak}",
                                    style: TextStyles.textGray12,
                                  ),
                                  Text(
                                    "${e.league}",
                                    style: TextStyles.textGray12,
                                  ),
                                ],
                              )),
                          Expanded(
                              flex: 3,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text.rich(
                                    overflow: TextOverflow.ellipsis,
                                    TextSpan(
                                      text: "${e.homeTeam}",
                                      style: TextStyles.textSize14,
                                      children: const <TextSpan>[
                                        TextSpan(
                                          text: "【主】",
                                          style: TextStyle(
                                            color: Color(0xFFa7a7a7),
                                            fontSize: Dimens.font_sp12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const LoadAssetImage(
                                    'games/vs',
                                    width: 17,
                                    height: 11,
                                    fit: BoxFit.contain,
                                  ),
                                  Text.rich(
                                    //    textAlign: TextAlign.center,
                                    overflow: TextOverflow.ellipsis,
                                    TextSpan(
                                      text: " ${e.awayTeam}",
                                      style: TextStyles.textSize14,
                                    ),
                                  ),
                                ],
                              ))
                        ],
                      ),
                    ),

                    BeishuItem(e,provider,provider.getItemList(e),key:Key('touzhuitem${index}'))
                    //...generateMatchItem(e)
                  ],
                ),
              ),
              e.canSingle()
                  ? const Positioned(
                      top: 0,
                      child: LoadAssetImage(
                        "bet/single",
                        height: 30,
                      ))
                  : const SizedBox.shrink(),
            ],
          );
        });
  }



  @override
  Widget build(BuildContext context) {
    return Consumer<BJDCModel>(builder: (context, model, child) {
      provider = model;

      return SingleChildScrollView(
        child: Container(
          decoration: const BoxDecoration(),
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
          child: Column(
            children: [
              buildMatchItem(),
              Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(color: Colors.white),
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop(true);
                    },
                    child: Container(
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.add,
                              color: Color(0xFFa0a0a0),
                              size: Dimens.font_sp20,
                            ),
                            Text.rich(
                              TextSpan(
                                text: "继续添加比赛",
                                style:
                                    const TextStyle(fontSize: Dimens.font_sp14),
                                children: <TextSpan>[
                                  TextSpan(
                                    text:
                                        " (已选${model.summary.selectMatchCount}场) ",
                                    style: const TextStyle(
                                      color: Color(0xFFa0a0a0),
                                      fontSize: Dimens.font_sp12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        )),
                  ))
            ],
          ),
        ),
      );
    });
  }
}

class DGBeisuItem extends StatefulWidget {
 final BJDCEntity match;
 final BetTouzhuItem betTouzhuItem;
 final BJDCModel provider;
 final List<BetTouzhuItem> touzhuItemList;

  DGBeisuItem(
      this.match, this.betTouzhuItem, this.touzhuItemList, this.provider,
      {super.key});

  @override
  _DGBeisuItemState createState() {
    return _DGBeisuItemState();
  }
}

class _DGBeisuItemState extends State<DGBeisuItem> {
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    
    _controller.text = widget.betTouzhuItem.beisu.toStringAsFixed(0);
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  int beisuIncrement(String beisu) {
    int count = int.tryParse(beisu) == null ? 0 : int.parse(beisu);

    count += 1;

    widget.betTouzhuItem.beisu = count;

    widget.provider.noticeBeisuUpdate(widget.match, widget.touzhuItemList);
    return count;
  }

  int beisuDecrement(String beisu) {
    int count = int.tryParse(beisu) == null ? 0 : int.parse(beisu);

    if (count == 0) {
      count = 1;
    }

    count -= 1;

    widget.betTouzhuItem.beisu = count;

    widget.provider.noticeBeisuUpdate(widget.match, widget.touzhuItemList);

    return count;
  }

  beisuChange(int newTimes) {
    widget.betTouzhuItem.beisu = newTimes;
    widget.provider.noticeBeisuUpdate(widget.match, widget.touzhuItemList);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        GestureDetector(
          onTap: () {
            int _times = beisuDecrement(_controller.text);
            _controller.text = _times.toString();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 2),
            child: const Icon(
              Icons.do_not_disturb_on_outlined,
              color: Color(0xFFee0a24),
              size: 20,
            ),
          ),
        ),
        Expanded(

            flex: 1,
            child: Container(
              alignment: Alignment.center,
              height: 20,
              child: TextField(
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                controller: _controller,
                onChanged: (newTimes) {
                  if (newTimes == "") {
                    Toast.show("倍数不能为空");
                    return;
                  }
                  int times = int.parse(newTimes);
                  beisuChange(times);
                  return;
                },
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp('[0-9]'))
                ],
                decoration: InputDecoration(
                  //   hintText: "倍数",
                  hintStyle: TextStyles.textGray14,
                  focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(
                      color: Color(0xFFEEEEEE),
                      width: 1.0, // focused state border width
                    ),
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: const BorderSide(
                      color: Color(0xFFEEEEEE),
                      width: 1.0, // focused state border width
                    ),
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 5, vertical: 0),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                ),
              ),
            )),
        GestureDetector(
          onTap: () {
            int _times = beisuIncrement(_controller.text);
            _controller.text = _times.toString();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 2),
            child: const Icon(
              Icons.add_circle,
              color: Color(0xFFee0a24),
              size: 20,
            ),
          ),
        ),
        const Text(
          "倍",
          style: TextStyles.textSize12,
        )
      ],
    );
  }
}



class BeishuItem extends StatefulWidget{

  final BJDCEntity match;

 final BJDCModel provider;
  List<BetTouzhuItem> touzhuItemList = [];


  BeishuItem(this.match,this.provider,this.touzhuItemList, {super.key});
  

    @override
  BeishuItemState createState() {
    return BeishuItemState();
  }
}


class BeishuItemState extends State<BeishuItem>{


  List<Widget> list = [];


  itemClose(index, BetTouzhuItem item, BJDCEntity match) {

    widget.touzhuItemList.removeRange(index,index +1);

    switch (item.itemType) {
      case 'spf':
        match.bjdcState!.spf[item.itemTypeIndex] = false;
        break;

      case 'jqs':
        match.bjdcState!.jqs[item.itemTypeIndex] = false;
        break;

      case 'cbf':
        match.bjdcState!.cbf[item.itemTypeIndex] = false;
        break;

      case 'sfgg':
        match.bjdcState!.sfgg[item.itemTypeIndex] = false;
        break;

      case 'bqc':
        match.bjdcState!.bqc[item.itemTypeIndex] = false;
        break;

      case 'sxp':
        match.bjdcState!.sxp[item.itemTypeIndex] = false;
        break;

      default:
    }

    if (widget.touzhuItemList.isEmpty) {
      match.resetCheckState();
    }
    
    widget.provider.noticeBeisuUpdate(match, widget.touzhuItemList);
  }


  @override
  void initState() {
  //  touzhuItemList = widget.provider.bjdcBetCommit.getTouzhuItem(widget.match);

    //  WidgetsBinding.instance.addPostFrameCallback((_) async {
    //       widget.provider.noticeBeisuUpdate(widget.match, touzhuItemList);

    // });

    
    super.initState();
  }



  


 List<Widget> generateItem(){

    List<Widget> list = [];
      widget.touzhuItemList.asMap().forEach((int index, BetTouzhuItem item) {
      Widget _widget = Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                  flex: 1,
                  child: InkWell(
                    onTap: () {
                      itemClose(index, item,  widget.match);
                    },
                    child: Container(
                      alignment: Alignment.centerLeft,
                      child: const Icon(
                        Icons.cancel,
                        color: Color(0xFFcccccc),
                        size: 30,
                      ),
                    ),
                  )),

              //表格
              Expanded(
                  flex: 5,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(
                        color: const Color(0xFFd9d9d9), // 边框颜色为红色
                        width: 1, // 边框宽度为1像素
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text.rich(
                              TextSpan(
                                text: item.name,
                                style: TextStyles.textSize14,
                                children: <TextSpan>[
                                  TextSpan(
                                    text: " ${item.sp}",
                                    style: TextStyles.textGray14,
                                  ),
                                ],
                              ),
                            ),
                            Text.rich(
                              TextSpan(
                                text: "预计奖金",
                                style: TextStyles.textSize12,
                                children: <TextSpan>[
                                  TextSpan(
                                    text: " ${(item.sp  * item.beisu * 2).toStringAsFixed(2)}",
                                    style: const TextStyle(
                                        fontSize: Dimens.font_sp14,
                                        color: Color(0xFFfb4627)),
                                  ),
                                  const TextSpan(
                                    text: "元",
                                    style: TextStyles.textSize12,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        )),
                        Expanded(
                            flex: 1,
                            child: DGBeisuItem(
                              widget.match,
                              item,
                              widget.touzhuItemList,
                              widget.provider,
                              key: Key('BetTouzhuItem_${index}'),
                            ))
                      ],
                    ),
                  )),
            ],
          ),
         const SizedBox(
            height: 15,
          ),
        ],
      );

      list.add(_widget);
    }
    
    
    );

    return list;
  }


  @override
  Widget build(BuildContext context) {
      return Column(children: generateItem(),);
  }

}