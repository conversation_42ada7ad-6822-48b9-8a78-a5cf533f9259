import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/page/entity/oyzq_entity.dart';
import 'package:sport_mobile/game/page/model/oy_model.dart';
import 'package:sport_mobile/game/page/widgets/ft_button_item.dart';
import 'package:sport_mobile/game/page/widgets/ft_custom_expansion_tile.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/widgets/load_image.dart';

//胜平负
class OYBCSPFList extends StatefulWidget {
  final Map<String, List<OYZQEntity>> match;

  final String gameType;

  const OYBCSPFList(this.match, this.gameType, {super.key});

  @override
  _OYBCSPFListState createState() => _OYBCSPFListState();
}

class _OYBCSPFListState extends State<OYBCSPFList> {
  late List<Widget> list;
  late OYModel provider;

  @override
  void initState() {
    super.initState();
  }

  List<Widget> buildMatchItem(List<OYZQEntity> list) {
    return list.map((e) {
      return Container(
          decoration: const BoxDecoration(
              border: Border(
            bottom: BorderSide(
              color: Color(0xFFd9d9d9),
              width: 1,
            ),
          )),
          child: Stack(
            children: [
              Container(
                padding: const EdgeInsets.only(
                    left: 15, right: 15, top: 5, bottom: 10),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "${e.league}",
                            style: TextStyles.textSize12,
                          ),
                          Expanded(
                              flex: 1,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text.rich(
                                    overflow: TextOverflow.ellipsis,
                                    TextSpan(
                                      text: "${e.homeTeam}",
                                      style: TextStyles.textSize14,
                                      children: const <TextSpan>[
                                        TextSpan(
                                          text: "【主】",
                                          style: TextStyle(
                                            color: Color(0xFFa7a7a7),
                                            fontSize: Dimens.font_sp12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const LoadAssetImage(
                                    'games/vs',
                                    width: 17,
                                    height: 11,
                                    fit: BoxFit.contain,
                                  ),
                                  Text.rich(
                                    //    textAlign: TextAlign.center,
                                    overflow: TextOverflow.ellipsis,
                                    TextSpan(
                                      text: " ${e.awayTeam}",
                                      style: TextStyles.textSize14,
                                    ),
                                  ),
                                ],
                              ))
                        ],
                      ),
                    ),
                    Container(
                        child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          margin: EdgeInsets.only(right: 10),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${e.weak}",
                                style: TextStyles.textGray12,
                              ),
                              Text(
                               "${e.stopSaleShortTime}",
                                style: e.stopSaleStatus == 0 ? TextStyles.textGray12 : TextStyles.textRed12,
                              ),
                            ],
                          ),
                        ),

                        //表格
                        Expanded(
                            child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Color(0xFFd9d9d9),
                              width: 1, // 边框宽度为1像素
                            ),
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                  flex: 6,
                                  child: Column(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                              flex: 4,
                                              child: ClipRRect(
                                                  borderRadius:
                                                      const BorderRadius.only(
                                                    topLeft:
                                                        Radius.circular(10),
                                                    bottomLeft:
                                                        Radius.circular(10),
                                                  ),
                                                  child: FTButtonItem(
                                                    "胜 ",
                                                    "${e.oddsData?.bcspf[0]}",
                                                    isActived: e.oyStateStore
                                                            ?.bcspf[0] ??
                                                        false,

                                                   isAvailable:   e.canBCSPF(),
                                                    onPressed: () {
                                                      provider
                                                          .fillUpActiveMatchItem(
                                                              e, "bcspf", 0);
                                                    },
                                                isOverTime: e.stopSaleStatus == 1 ?  true  : false,

                                                  ))),
                                          Expanded(
                                              flex: 4,
                                              child: FTButtonItem(
                                                "平 ",
                                                "${e.oddsData?.bcspf[1]}",
                                                isActived:
                                                    e.oyStateStore?.bcspf[1] ??
                                                        false,
                                                   isAvailable:   e.canBCSPF(),
                                                onPressed: () {
                                                  provider
                                                      .fillUpActiveMatchItem(
                                                          e, "bcspf", 1);
                                                },
                                                isOverTime: e.stopSaleStatus == 1 ?  true  : false,

                                              )),
                                          Expanded(
                                              flex: 4,
                                              child: ClipRRect(
                                                borderRadius:
                                                    const BorderRadius.only(
                                                  topRight: Radius.circular(10),
                                                  bottomRight:
                                                      Radius.circular(10),
                                                ),
                                                child: FTButtonItem(
                                                  "负 ",
                                                  "${e.oddsData?.bcspf[2]}",
                                                  isActived:
                                                      e.oyStateStore?.bcspf[2] ??
                                                          false,
                                                   isAvailable:   e.canBCSPF(),
                                                  onPressed: () {
                                                    provider
                                                        .fillUpActiveMatchItem(
                                                            e, "bcspf", 2);
                                                  },
                                                isOverTime: e.stopSaleStatus == 1 ?  true  : false,

                                                  FTBorder: Border.all(
                                                      width: 0,
                                                      color:
                                                          Colors.transparent),
                                                ),
                                              )),
                                        ],
                                      ),
                                    ],
                                  )),
                            ],
                          ),
                        ))
                      ],
                    ))
                  ],
                ),
              ),
              e.canSingle()
                  ? const Positioned(
                      top: 0,
                      child: LoadAssetImage(
                        "bet/single",
                        height: 30,
                      ))
                  : const SizedBox.shrink(),
            ],
          ));
    }).toList();
  }

  List<Widget> buildList() {
    list = [];
    widget.match.forEach((matchName, value) {
      Widget _item = Expanded(
          child: Theme(
              data: ThemeData().copyWith(dividerColor: Colors.transparent),
              child: FTCustomExpansionTile(
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                title: Text(
                  '$matchName ${value.length}场比赛',
                  style: TextStyles.textSize12,
                ),
                children: buildMatchItem(value),
              )));

      list.add(_item);
    });

    return list;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<OYModel>(builder: (context, model, child) {
      provider = model;
      return Container(
        decoration: const BoxDecoration(),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: buildList(),
        ),
      );
    });
  }
}
