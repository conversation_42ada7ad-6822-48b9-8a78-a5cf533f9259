import 'package:flutter/material.dart';

import 'package:sport_mobile/res/gaps.dart';
import 'package:sport_mobile/widgets/shimmer_struct.dart';


class MatchItemShimmer extends StatelessWidget {
  const MatchItemShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: const BoxDecoration(
            border: Border(
          bottom: BorderSide(
            color: Color(0xFFd9d9d9),
            width: 1,
          ),
        )),
        child: <PERSON>ack(
          children: [
            Container(
              padding:
                  const EdgeInsets.only(left: 5, right: 5, top: 5, bottom: 10),
              child: Column(
                children: [
                  Container(
                    width: 60,
                    padding: const EdgeInsets.only(bottom: 5),
                    child: ShimmerStruct.rectangular(height: 10),
                  ),
                  Row(
                    children: [
                      Column(
                        children: [
                          Container(
                            width: 20,
                            padding: const EdgeInsets.only(bottom: 5),
                            child: ShimmerStruct.rectangular(height: 5),
                          ),
                          Container(
                            width: 20,
                            padding: const EdgeInsets.only(bottom: 5),
                            child: ShimmerStruct.rectangular(height: 5),
                          ),
                          Container(
                            width: 20,
                            padding: const EdgeInsets.only(bottom: 5),
                            child: ShimmerStruct.rectangular(height: 5),
                          ),
                          Container(
                            width: 20,
                            padding: const EdgeInsets.only(bottom: 5),
                            child: ShimmerStruct.rectangular(height: 5),
                          ),
                        ],
                      ),
                      Gaps.hGap10,
                      Expanded(
                          child: Container(
                        decoration: BoxDecoration(),
                        child: Column(
                          children: [
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.only(bottom: 5),
                              child: ShimmerStruct.rectangular(height: 20),
                            ),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.only(bottom: 5),
                              child: ShimmerStruct.rectangular(height: 20),
                            ),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.only(bottom: 5),
                              child: ShimmerStruct.rectangular(height: 20),
                            ),
                          ],
                        ),
                      ))
                    ],
                  ),
                ],
              ),
            ),
          ],
        ));
  }
}