import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/data/basketball_state.dart';
import 'package:sport_mobile/game/page/entity/jclq_entity.dart';
import 'package:sport_mobile/game/page/model/basketball_model.dart';
import 'package:sport_mobile/game/page/widgets/ft_button_item.dart';
import 'package:sport_mobile/res/dimens.dart';
import 'package:sport_mobile/res/gaps.dart';
import 'package:sport_mobile/res/styles.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/widgets/load_image.dart';

class BasketballSFCBottomSheet extends StatefulWidget {
  final JCLQEntity match;

  const BasketballSFCBottomSheet(this.match, {super.key});

  @override
  _BJDCBFBottomSheetState createState() {
    return _BJDCBFBottomSheetState();
  }
}

class _BJDCBFBottomSheetState extends State<BasketballSFCBottomSheet> {

  late BasketballState basketballState;

     @override
  void initState() {
    super.initState();
    basketballState = BasketballState.fromJson(widget.match.basketballState!.toJson());
  }


  @override
  Widget build(BuildContext context) {

     return Consumer<BasketballModel>(builder: (context, model, child) {
          return Container(
            height: 500,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Container(
                  padding: EdgeInsets.symmetric(vertical: 15),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.0),
                      color: Color(0xFFf5f5f5)),
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text.rich(
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        TextSpan(
                          text: "${widget.match.awayTeam} ",
                          style: TextStyles.textSize14,
                        ),
                      ),
                      const LoadAssetImage(
                        'games/vs',
                        width: 17,
                        height: 11,
                        fit: BoxFit.contain,
                      ),


                         Text.rich(
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        TextSpan(
                          text: " ${widget.match.homeTeam}",
                          style: TextStyles.textSize14,
                          children: const <TextSpan>[
                            TextSpan(
                              text: "【主】",
                              style: TextStyle(
                                color: Color(0xFFa7a7a7),
                                fontSize: Dimens.font_sp12,
                              ),
                            ),
                          ],
                        ),
                      ),
                   
                    ],
                  ),
                ),
                Expanded(
                    child: ListView(
                  children: [
                    Container(
                        padding: EdgeInsets.symmetric(horizontal: 15),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: 60,
                              alignment: Alignment.center,
                              margin: const EdgeInsets.symmetric(vertical: 15),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: const Color(0xFFff664c),
                              ),
                              child: const Text(
                                "客胜",
                                style: TextStyle(
                                    fontSize: Dimens.font_sp14,
                                    color: Colors.white),
                              ),
                            ),
                            Row(
                              children: [

                                 Expanded(
                                  child: FTButtonIndivalItem(
                                    "1-5",
                                    "${widget.match.oddsData?.sfc[6] ?? ""}",
                                    isActived: widget.match.basketballState?.sfc[6] ?? false,
                                    onPressed: () {
                                      model.fillUpActiveMatchItem(widget.match, "sfc", 6);
                                    },
                                  ),
                                ),

                                Gaps.hGap10,
                                 Expanded(
                                  child: FTButtonIndivalItem(
                                    "6-10",
                                    "${widget.match.oddsData?.sfc[7] ?? ""}",
                                    isActived: widget.match.basketballState?.sfc[7] ?? false,
                                    onPressed: () {
                                      model.fillUpActiveMatchItem(widget.match, "sfc", 7);
                                    },
                                  ),
                                ),
                                 Gaps.hGap10,
                                 Expanded(
                                  child: FTButtonIndivalItem(
                                    "11-15",
                                    "${widget.match.oddsData?.sfc[8] ?? ""}",
                                    isActived: widget.match.basketballState?.sfc[8] ?? false,
                                    onPressed: () {
                                      model.fillUpActiveMatchItem(widget.match, "sfc", 8);
                                    },
                                  ),
                                ),
                              ],
                            ),
                            Gaps.vGap12,
                            Row(
                              children: [
                                Expanded(
                                  child: FTButtonIndivalItem(
                                    "16-20",
                                    "${widget.match.oddsData?.sfc[9] ?? ""}",
                                    isActived: widget.match.basketballState?.sfc[9] ?? false,
                                    onPressed: () {
                                      model.fillUpActiveMatchItem(widget.match, "sfc", 9);
                                    },
                                  ),
                                ),
                                Gaps.hGap10,
                                Expanded(
                                  child: FTButtonIndivalItem(
                                    "21-25",
                                    "${widget.match.oddsData?.sfc[10] ?? ""}",
                                    isActived: widget.match.basketballState?.sfc[10] ?? false,
                                    onPressed: () {
                                      model.fillUpActiveMatchItem(widget.match, "sfc", 10);
                                    },
                                  ),
                                ),
                                Gaps.hGap10,
                                Expanded(
                                  child: FTButtonIndivalItem(
                                    "26+",
                                    "${widget.match.oddsData?.sfc[11] ?? ""}",
                                    isActived: widget.match.basketballState?.sfc[11] ?? false,
                                    onPressed: () {
                                      model.fillUpActiveMatchItem(widget.match, "sfc", 11);
                                    },
                                  ),
                                ),
                                Gaps.hGap10,
                               
                              ],
                            ),
                            Gaps.vGap12,
                          
                            Container(
                              margin: EdgeInsets.only(top: 15),
                              height: 1,
                              width: double.infinity,
                              color: Color(0xFFdcdcdc),
                            ),

                            Container(
                              width: 60,
                              alignment: Alignment.center,
                              margin: EdgeInsets.symmetric(vertical: 15),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: Color(0xFF80c269),
                              ),
                              child: const Text(
                                "主胜",
                                style: TextStyle(
                                    fontSize: Dimens.font_sp14,
                                    color: Colors.white),
                              ),
                            ),
                             Row(
                              children: [

                                Expanded(
                                  child: FTButtonIndivalItem(
                                    "1-5",
                                    "${widget.match.oddsData?.sfc[0] ?? ""}",
                                    isActived: widget.match.basketballState?.sfc[0] ?? false,
                                    onPressed: () {
                                      model.fillUpActiveMatchItem(widget.match, "sfc", 0);
                                    },
                                  ),
                                ),

                                Gaps.hGap10,
                                 Expanded(
                                  child: FTButtonIndivalItem(
                                    "6-10",
                                    "${widget.match.oddsData?.sfc[1] ?? ""}",
                                    isActived: widget.match.basketballState?.sfc[1] ?? false,
                                    onPressed: () {
                                      model.fillUpActiveMatchItem(widget.match, "sfc", 1);
                                    },
                                  ),
                                ),
                                 Gaps.hGap10,
                                 Expanded(
                                  child: FTButtonIndivalItem(
                                    "11-15",
                                    "${widget.match.oddsData?.sfc[2] ?? ""}",
                                    isActived: widget.match.basketballState?.sfc[2] ?? false,
                                    onPressed: () {
                                      model.fillUpActiveMatchItem(widget.match, "sfc", 2);
                                    },
                                  ),
                                ),
                              ],
                            ),

                            Gaps.vGap12,


                             Row(
                              children: [
                                Expanded(
                                  child: FTButtonIndivalItem(
                                    "16-20",
                                    "${widget.match.oddsData?.sfc[3] ?? ""}",
                                    isActived: widget.match.basketballState?.sfc[3] ?? false,
                                    onPressed: () {
                                      model.fillUpActiveMatchItem(widget.match, "sfc", 3);
                                    },
                                  ),
                                ),
                                Gaps.hGap10,
                                Expanded(
                                  child: FTButtonIndivalItem(
                                    "21-25",
                                    "${widget.match.oddsData?.sfc[4] ?? ""}",
                                    isActived: widget.match.basketballState?.sfc[4] ?? false,
                                    onPressed: () {
                                      model.fillUpActiveMatchItem(widget.match, "sfc", 4);
                                    },
                                  ),
                                ),
                                Gaps.hGap10,
                                Expanded(
                                  child: FTButtonIndivalItem(
                                    "26+",
                                    "${widget.match.oddsData?.sfc[5] ?? ""}",
                                    isActived: widget.match.basketballState?.sfc[5] ?? false,
                                    onPressed: () {
                                      model.fillUpActiveMatchItem(widget.match, "sfc", 5);
                                    },
                                  ),
                                ),
                                Gaps.hGap10,
                               
                              ],
                            ),


                            Gaps.vGap12,
                           
                            

                          ],
                        ))
                  ],
                )),
                 Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Expanded(
                        child: InkWell(
                           onTap: (){
                             widget.match.basketballState = basketballState;
                    model.notifyListeners();



                    NavigatorUtils.goBackWithParams(context,2);
                          },
                      child: Container(
                        height: 45,
                        alignment: Alignment.center,
                        color: const Color(0xFFc7c7c7),
                        child: const Text("取消"),
                      ),
                    )),
                    Expanded(
                        child: InkWell(
                      onTap: (){
                          NavigatorUtils.goBackWithParams(context,1);
                      },

                      child: Container(
                        height: 45,
                        alignment: Alignment.center,
                        color: const Color(0xFFfd4627),
                        child: const Text(
                          "确定",
                          style: TextStyles.textWhite14,
                        ),
                      ),
                    )),
                  ],
                ),
              ],
            ),
          );
        });
  }
}
