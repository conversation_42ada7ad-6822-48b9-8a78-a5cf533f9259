import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/page/widgets/match_table_cell.dart';
import 'package:sport_mobile/game/page/widgets/shimmer/match_item_jc_shimmer.dart';
import 'package:sport_mobile/game/page/widgets/shimmer/match_item_shimmer.dart';
import 'package:sport_mobile/net/network_state.dart';
import 'package:sport_mobile/widgets/state_layout.dart';
import 'package:sport_mobile/game/page/model/jc_model.dart';

class JCListRefresh extends StatefulWidget {
  final String title;

  const JCListRefresh(this.title, {super.key});

  @override
  _JCListRefreshState createState() => _JCListRefreshState();
}

class _JCListRefreshState extends State<JCListRefresh> {
  @override
  void initState() {
    super.initState();

     JCModel model = Provider.of<JCModel>(context, listen: false);

      model.gameName = widget.title;

      if (widget.title == '冠亚军') {
        model.gameID = MatchDataType.Gyj;
      } else {
        model.gameID = MatchDataType.Gj;
      }

      model.refreshData();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<JCModel>(builder: (context, model, child) {
      if ((model.isLoad == false &&
              model.networkStatus == NetworkStatus.networkFailed) ||
          (model.isLoad == false &&
              model.networkStatus == NetworkStatus.networkTimeout)) {
        return NetworkFailedWidget(
          onPressed: () {
            setState(() {
              model.refreshData();
            });
          },
        );
      }

      if (model.isLoad) {
        return SingleChildScrollView(
          child: Column(
            children: [
              ...List.generate(10, (index) {
                return Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  alignment: Alignment.center,
                  padding: const EdgeInsets.only(top: 15, left: 20, right: 20),
                  child: const MatchItemShimmer(),
                  // child: Text("122112"),
                );
              })
            ],
          ),
        );
      }

      if (model.jcList.isEmpty && model.isLoad == false) {
        //加载完毕，没有数据
        return const StateLayout(
          type: StateType.empty,
        );
      }

      return RefreshIndicator(
        onRefresh: () {
          return model.refreshData();
        },
        child: ListView.builder(
            itemCount: model.jcList.length,
            itemBuilder: (context, index) {
              return MatchTableCell(
                model.jcList[index],
                widget.title,
                key: Key("$index"),
              );
            }),
      );
    });
  }
}
