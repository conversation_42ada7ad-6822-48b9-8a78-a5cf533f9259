import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/page/entity/gjjc_entity.dart';
import 'package:sport_mobile/game/page/widgets/match_table_cell.dart';
import 'package:sport_mobile/game/page/model/jc_model.dart';

class GJJCConfirmList extends StatefulWidget {
  const GJJCConfirmList({super.key});

  @override
  _GJJCConfirmListState createState() => _GJJCConfirmListState();
}

class _GJJCConfirmListState extends State<GJJCConfirmList> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<JCModel>(builder: (context, model, child) {
      return ListView.builder(
          itemCount: model.jcList.length,
          itemBuilder: (context, index) {
            JCMatch temp = model.jcList[index];

            if (model.selectedMatchList.containsKey("${temp.boutIndex}")) {
              return MatchTableCell(
                model.jcList[index],
                "${model.gameName}竞猜",
                key: Key("$index"),
                isConfirmPage: true,
              );
            } else {
              return const SizedBox();
            }
          });
    });
  }
}
