import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/data/league_filter.dart';
import 'package:sport_mobile/game/page/model/oy_model.dart';
import 'package:sport_mobile/game/page/widgets/ft_chuan_bottom_sheet.dart';
import 'package:sport_mobile/res/dimens.dart';
import 'package:sport_mobile/res/styles.dart';

class OYLeaguesFilter extends StatefulWidget {
  const OYLeaguesFilter({super.key});

  @override
  _OYLeaguesFilterState createState() {
    return _OYLeaguesFilterState();
  }
}

class _OYLeaguesFilterState extends State<OYLeaguesFilter> {
  late OYModel provider;
  List<LeagueFilter> leagueList = [];

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {

      leagueList = provider.getLeagueList();
      setState(() {});
    });
  }

  List<Widget> generateLeagueList(List<LeagueFilter> leagueFilter) {
    List<Widget> leagueList = [];
    leagueFilter.asMap().forEach((index, item) {
      Widget _button = FTButtonChuanIndivalItem(
        item.name,
        isActived: item.select,
        key: Key(item.name),
        onPressed: () {
          if (index == 0) {
            for (int i = 1; i < leagueFilter.length; i++) {
              leagueFilter[i].select = false;
            }
            leagueFilter[0].select = true;
          } else {
            item.select = !item.select;
            leagueFilter[0].select = false;
          }

          provider.selectMatchMap(provider.cate.typeKey!);
        },
      );

      leagueList.add(_button);
    });

    return leagueList;
  }

  flush() {
    provider.leagueFilters.asMap().forEach((index, item) {
      if (index > 0) {
        item.select = false;
      } else {
        item.select = true;
      }
    });

    provider.selectMatchMap(provider.cate.typeKey!);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<OYModel>(builder: (context, model, child) {
      provider = model;

      return Container(
        height: 500,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Container(
              padding: EdgeInsets.symmetric(vertical: 15),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.0),
                  color: Color(0xFFf5f5f5)),
              alignment: Alignment.center,
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text.rich(
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    TextSpan(
                      text: "",
                      style: TextStyles.textSize14,
                      children: <TextSpan>[
                        TextSpan(
                          text: "赛事筛选",
                          style: TextStyle(
                            //  color: Color(0xFFa7a7a7),
                            fontSize: Dimens.font_sp14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
                child: Container(
              // margin: EdgeInsets.symmetric(horizontal: 20,vertical: 25),
              child: ListView(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 15, vertical: 15),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          "选择联赛",
                          style: TextStyle(
                              color: Color(0xFF807d7d),
                              fontSize: Dimens.font_sp14),
                        ),
                        InkWell(
                          onTap: () {
                            flush();
                          },
                          child: const Text(
                            "清空",
                            style: TextStyle(
                                color: Color(0xFFfd4627),
                                fontSize: Dimens.font_sp14),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: GridView.count(
                      crossAxisCount: 3, // 每行显示的列数
                      childAspectRatio: 3.2, // 宽高比例
                      crossAxisSpacing: 10,
                      mainAxisSpacing: 10,
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(), // 禁止滚动

                      children: generateLeagueList(leagueList),
                    ),
                  ),
                ],
              ),
            )),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Expanded(
                    child: InkWell(
                  onTap: () {
                    provider.leagueFilters = provider.cloneLeagueList;
                    provider.selectMatchMap(provider.cate.typeKey!);
                    Navigator.pop(context);
                  },
                  child: Container(
                    height: 45,
                    alignment: Alignment.center,
                    color: const Color(0xFFc7c7c7),
                    child: const Text("取消"),
                  ),
                )),
                Expanded(
                    child: InkWell(
                  onTap: () {
                    //    provider.leagueFilters =  provider.cloneLeagueList;
                    provider.selectMatchMap(provider.cate.typeKey!);
                    Navigator.pop(context);
                  },
                  child: Container(
                    height: 45,
                    alignment: Alignment.center,
                    color: const Color(0xFFfd4627),
                    child: const Text(
                      "确定",
                      style: TextStyles.textWhite14,
                    ),
                  ),
                )),
              ],
            ),
          ],
        ),
      );
    });
  }
}
