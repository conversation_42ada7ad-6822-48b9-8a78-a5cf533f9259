import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/page/model/chuan_select.dart';
import 'package:sport_mobile/game/page/model/ft_model.dart';
import 'package:sport_mobile/res/styles.dart';

class FTChuanBottomSheet extends StatefulWidget {
  const FTChuanBottomSheet({super.key});

  @override
  _FTChuanBottomSheetState createState() {
    return _FTChuanBottomSheetState();
  }
}

class _FTChuanBottomSheetState extends State<FTChuanBottomSheet> {
  List<ChuanSelect> cloneChuanSelect = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      FTModel provider = Provider.of<FTModel>(context, listen: false);

      provider.chuanSelectList.forEach((ChuanSelect item) {
        ChuanSelect _item;
        _item = ChuanSelect.from<PERSON>son(item.toJson());
        cloneChuanSelect.add(_item);
      });

    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<FTModel>(builder: (context, model, child) {
      return Container(
        height: 300,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Container(
                padding: const EdgeInsets.symmetric(vertical: 15),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.0),
                    color: const Color(0xFFf5f5f5)),
                alignment: Alignment.center,
                child: Container(
                  child: const Text(
                    "过关方式",
                    style: TextStyles.textSize14,
                  ),
                )),
            Expanded(
                child: Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
              child: GridView.count(
                crossAxisCount: 3,
                childAspectRatio: 3,
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
                children: model.chuanSelectList.map((e) {
                  return FTButtonChuanIndivalItem(
                    e.name,
                    isActived: e.select,
                    onPressed: () {
                      e.select = !e.select;
                      model.updateChuanItem();
                    },
                  );
                }).toList(),
              ),
            )),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Expanded(
                    child: InkWell(
                  onTap: () {
                    model.chuanSelectList = cloneChuanSelect;
                    Navigator.pop(context);
                    model.updateChuanItem();
                  },
                  child: Container(
                    height: 45,
                    alignment: Alignment.center,
                    color: const Color(0xFFc7c7c7),
                    child: const Text("取消"),
                  ),
                )),
                Expanded(
                    child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    height: 45,
                    alignment: Alignment.center,
                    color: const Color(0xFFfd4627),
                    child: const Text(
                      "确定",
                      style: TextStyles.textWhite14,
                    ),
                  ),
                )),
              ],
            ),
          ],
        ),
      );
    });
  }
}

class FTButtonChuanIndivalItem extends StatelessWidget {
  final String name;

  final bool isActived;
  final VoidCallback? onPressed;

  const FTButtonChuanIndivalItem(this.name,
      {this.onPressed, this.isActived = false, super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(
              //   vertical: 10,

              ),
          decoration: BoxDecoration(
            border: Border.all(
              color: const Color(0xFFd9d9d9),
              width: 1,
            ),
            color: isActived == true ? const Color(0xFFfd4627) : Colors.white,
            borderRadius: BorderRadius.circular(5),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(name,
                  style: isActived == true
                      ? TextStyles.textWhite12
                      : TextStyles.textSize12),
            ],
          )),
    );
  }
}
