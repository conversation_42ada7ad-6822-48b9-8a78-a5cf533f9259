import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/page/entity/jclq_entity.dart';
import 'package:sport_mobile/game/page/model/basketball_model.dart';
import 'package:sport_mobile/game/page/widgets/basketball_sfc_bottom_sheet.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/widgets/load_image.dart';

class BasketballSFCConfirm extends StatefulWidget {
  @override
  _BasketballSFCConfirmState createState() {
    return _BasketballSFCConfirmState();
  }

  const BasketballSFCConfirm({
    super.key,
  });
}

class _BasketballSFCConfirmState extends State<BasketballSFCConfirm> {
  late BasketballModel provider;

  late List<Widget> list;

  void _showBottomSheet(BuildContext context, JCLQEntity e) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      builder: (BuildContext context) {
        return BasketballSFCBottomSheet(e);
      },
    ).then((value) {
        if (value == null || value == 1) {
        provider.updateMatchList();
        provider.DGItemAdd();
        provider.notifyListeners();
      }

    });
  }

  bool noSelect(JCLQEntity match) {
    return !match.basketballState!.hasSelect();
  }

  buildSelectText(JCLQEntity match) {
    List<String> selectList = [];
    List itemList = [];
    int count;


    count = match.basketballState!.sfc.length;
    for (int i = 0; i < count; i++) {
      if (match.basketballState!.sfc[i]) {
        itemList.add(BasketballMatchTip.sfc[i]);
      }
    }
    if (itemList.isNotEmpty) {
      selectList.add('胜分差: ${itemList.join(' ')}');
    }


    return selectList.join(' ');
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BasketballModel>(builder: (context, model, child) {
      provider = model;

      return SingleChildScrollView(
        child: Container(
          decoration: const BoxDecoration(),
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
          child: Column(
            children: [
              ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: model.confirmMatchList.length,
                  itemBuilder: (BuildContext context, index) {
                    JCLQEntity? match = model.confirmMatchList[index];

                    return Stack(
                      children: [
                        Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 20, horizontal: 15),
                            decoration: const BoxDecoration(
                                border: Border(
                              bottom: BorderSide(
                                color: Color(0xFFdcdcdc),
                                width: 1,
                              ),
                            )),
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                        flex: 2,
                                        child: Row(
                                          children: [
                                            Text(
                                              "${match.weak}",
                                              style: TextStyles.textGray12,
                                            ),
                                            Text(
                                              "${match.league}",
                                              style: TextStyles.textGray12,
                                            ),
                                          ],
                                        )),
                                    Expanded(
                                        flex: 3,
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            Text.rich(
                                              overflow: TextOverflow.ellipsis,
                                              TextSpan(
                                                text: "${match.homeTeam}",
                                                style: TextStyles.textSize14,
                                                children: const <TextSpan>[
                                                  TextSpan(
                                                    text: "【主】",
                                                    style: TextStyle(
                                                      color: Color(0xFFa7a7a7),
                                                      fontSize:
                                                          Dimens.font_sp12,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const LoadAssetImage(
                                              'games/vs',
                                              width: 17,
                                              height: 11,
                                              fit: BoxFit.contain,
                                            ),
                                            Text.rich(
                                              //    textAlign: TextAlign.center,
                                              overflow: TextOverflow.ellipsis,
                                              TextSpan(
                                                text: " ${match.awayTeam}",
                                                style: TextStyles.textSize14,
                                              ),
                                            ),
                                          ],
                                        ))
                                  ],
                                ),
                                Gaps.vGap12,
                                Row(
                                  children: [
                                    Expanded(
                                        flex: 1,
                                        child: InkWell(
                                          onTap: () {
                                            match.resetCheckState();
                                            model.itemMatchUpdate(match);
                                          },
                                          child: Container(
                                            alignment: Alignment.centerLeft,
                                            child: const Icon(
                                              Icons.cancel,
                                              color: Color(0xFFcccccc),
                                              size: 30,
                                            ),
                                          ),
                                        )),
                                    Expanded(
                                        flex: 5,
                                        child: InkWell(
                                          onTap: () {
                                            _showBottomSheet(context, match);
                                          },
                                          child: Container(
                                            alignment: Alignment.center,
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 10, vertical: 8),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              color: noSelect(match)
                                                  ? Colors.white
                                                  : const Color(0xFFfd4627),
                                            ),
                                            child: Text(
                                              noSelect(match)
                                                  ? "点击展开比分投注"
                                                  : buildSelectText(match),
                                              style: noSelect(match)
                                                  ? const TextStyle(
                                                      color: Color(0xFF807d7d),
                                                      fontSize:
                                                          Dimens.font_sp14)
                                                  : TextStyles.textWhite14,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ))
                                  ],
                                )
                              ],
                            )),
                        match.canSingle()
                            ? const Positioned(
                                top: 0,
                                child: LoadAssetImage(
                                  "bet/single",
                                  height: 30,
                                ))
                            : const SizedBox(),
                      ],
                    );
                  }),
              Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(color: Colors.white),
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop(true);
                    },
                    child: Container(
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.add,
                              color: Color(0xFFa0a0a0),
                              size: Dimens.font_sp20,
                            ),
                            Text.rich(
                              TextSpan(
                                text: "继续添加比赛",
                                style:
                                    const TextStyle(fontSize: Dimens.font_sp14),
                                children: <TextSpan>[
                                  TextSpan(
                                    text:
                                        " (已选${model.summary.selectMatchCount}场) ",
                                    style: const TextStyle(
                                      color: Color(0xFFa0a0a0),
                                      fontSize: Dimens.font_sp12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        )),
                  ))
            ],
          ),
        ),
      );
    });
  }
}
