import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/page/entity/jclq_entity.dart';
import 'package:sport_mobile/game/page/model/basketball_model.dart';
import 'package:sport_mobile/game/page/widgets/ft_button_item.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/widgets/load_image.dart';

class BasketballSFConfirm extends StatefulWidget {
  @override
  _BasketballSFConfirmState createState() {
    return _BasketballSFConfirmState();
  }

  const BasketballSFConfirm({
    super.key,
  });
}

class _BasketballSFConfirmState extends State<BasketballSFConfirm> {
  late BasketballModel provider;

  late List<Widget> list;

  bool isSingle = false;
 
  Widget buildMatchItem() {
    return   ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: provider.confirmMatchList.length,
                  itemBuilder: (BuildContext context, index) {
                    JCLQEntity? e = provider.confirmMatchList[index];

                    return Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                                border: Border(
                              bottom: BorderSide(
                                color: Color(0xFFdcdcdc),
                                width: 1,
                              ),
                            )),
                padding: const EdgeInsets.only(
                    left: 15, right: 15, top: 5, bottom: 10),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                         Expanded(
                                        flex: 2,
                                        child: Row(
                                          children: [
                                            Text(
                                              "${e.weak}",
                                              style: TextStyles.textGray12,
                                            ),
                                            Text(
                                              "${e.league}",
                                              style: TextStyles.textGray12,
                                            ),
                                          ],
                                        )),
                          Expanded(
                              flex: 3,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text.rich(
                                    overflow: TextOverflow.ellipsis,
                                    TextSpan(
                                      text: "${e.homeTeam}",
                                      style: TextStyles.textSize14,
                                      children: const <TextSpan>[
                                        TextSpan(
                                          text: "【主】",
                                          style: TextStyle(
                                            color: Color(0xFFa7a7a7),
                                            fontSize: Dimens.font_sp12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const LoadAssetImage(
                                    'games/vs',
                                    width: 17,
                                    height: 11,
                                    fit: BoxFit.contain,
                                  ),
                                  Text.rich(
                                    //    textAlign: TextAlign.center,
                                    overflow: TextOverflow.ellipsis,
                                    TextSpan(
                                      text: " ${e.awayTeam}",
                                      style: TextStyles.textSize14,
                                    ),
                                  ),
                                ],
                              ))
                        ],
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                    Expanded(
                                        flex: 1,
                                        child: InkWell(
                                          onTap: () {
                                            e.resetCheckState();
                                            provider.itemMatchUpdate(e);
                                          },
                                          child: Container(
                                            alignment: Alignment.centerLeft,
                                            child: const Icon(
                                              Icons.cancel,
                                              color: Color(0xFFcccccc),
                                              size: 30,
                                            ),
                                          ),
                                        )),

                    //表格
                    Expanded(
                      flex: 5,
                        child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Color(0xFFd9d9d9),
                          width: 1, // 边框宽度为1像素
                        ),
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 6,
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                     
                                      Expanded(
                                          flex: 5,
                                          child: 
                                          
                                          ClipRRect(
                                            borderRadius:
                                                const BorderRadius.only(
                                              topLeft: Radius.circular(10),
                                              bottomLeft:
                                                  Radius.circular(10),
                                            ),
                                            child:

                                          FTButtonItem(
                                            "负",
                                            "${e.oddsData?.sf[1]}",
                                            isActived:
                                                e.basketballState?.sf[1] ??
                                                    false,
                                            isAvailable: e.canSF(isSingle),
                                            onPressed: () {
                                              provider
                                                  .fillUpActiveMatchItem(
                                                      e, "sf", 1);

                                               provider.updateMatchList();
                                              provider.DGItemAdd();
                                            },
                                          ))
                                          ),
                                      const Expanded(
                                          flex: 5,
                                          child: FTButtonItem(
                                            "-",
                                            "",
                                            isActived:
                                              
                                                    false,
                                            isAvailable: false,

                                          
                                          )),
                                      Expanded(
                                          flex: 5,
                                          child: ClipRRect(
                                            borderRadius:
                                                const BorderRadius.only(
                                              topRight: Radius.circular(10),
                                              bottomRight:
                                                  Radius.circular(10),
                                            ),
                                            child: FTButtonItem(
                                              "胜",
                                              "${e.oddsData?.sf[0]}",
                                              isActived: e.basketballState
                                                      ?.sf[0] ??
                                                  false,
                                              isAvailable: e.canSF(isSingle),
                                              onPressed: () {
                                                provider
                                                    .fillUpActiveMatchItem(
                                                        e, "sf", 0);
                                                 provider.updateMatchList();
                                              provider.DGItemAdd();
                                              },
                                              FTBorder: Border.all(
                                                  width: 0,
                                                  color:
                                                      Colors.transparent),
                                            ),
                                          )),
                                    ],
                                  ),
                                ],
                              )),
                        ],
                      ),
                    ))
                      ],
                    )
                  ],
                ),
              ),
              e.canSingle()
                  ? const Positioned(
                      top: 0,
                      child: LoadAssetImage(
                        "bet/single",
                        height: 30,
                      ))
                  : const SizedBox.shrink(),
            ],
          );
                  });
  }

  bool noSelect(JCLQEntity match) {
    
    return !match.basketballState!.hasSelect();
  }

  @override
  Widget build(BuildContext context) {
    
    return Consumer<BasketballModel>(builder: (context, model, child) {
      provider = model;

      return SingleChildScrollView(
        child: Container(
          decoration: const BoxDecoration(),
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
          child: Column(
            children: [
              buildMatchItem(),
              Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  decoration: const BoxDecoration(color: Colors.white),
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop(true);
                    },
                    child: Container(
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.add,
                              color: Color(0xFFa0a0a0),
                              size: Dimens.font_sp20,
                            ),
                            Text.rich(
                              TextSpan(
                                text: "继续添加比赛",
                                style:
                                    const TextStyle(fontSize: Dimens.font_sp14),
                                children: <TextSpan>[
                                  TextSpan(
                                    text:
                                        " (已选${model.summary.selectMatchCount}场) ",
                                    style: const TextStyle(
                                      color: Color(0xFFa0a0a0),
                                      fontSize: Dimens.font_sp12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        )),
                  ))
            ],
          ),
        ),
      );
    });
  }
}
