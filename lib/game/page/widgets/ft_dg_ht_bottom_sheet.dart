import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/data/ft_state_store.dart';
import 'package:sport_mobile/game/page/entity/jczq_entity.dart';
import 'package:sport_mobile/game/page/model/ft_model.dart';
import 'package:sport_mobile/game/page/widgets/ft_match_row_item.dart';
import 'package:sport_mobile/res/dimens.dart';
import 'package:sport_mobile/res/gaps.dart';
import 'package:sport_mobile/res/styles.dart';
import 'package:sport_mobile/widgets/load_image.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';

class FTDGHTBottomSheet extends StatefulWidget {
  final JCZQEntity match;

  const FTDGHTBottomSheet(this.match, {super.key});

  @override
  _FTDGHTBottomSheetState createState() {
    return _FTDGHTBottomSheetState();
  }
}

class _FTDGHTBottomSheetState extends State<FTDGHTBottomSheet> {


  late FTStateStore ftStateStore;


  @override
  void initState() {
    super.initState();
    ftStateStore = FTStateStore.fromJson(widget.match.ftStateStore!.toJson());
  }

  @override
  Widget build(BuildContext context) {
     return Consumer<FTModel>(builder: (context, model, child) {
          return Container(
            height: 500,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Container(
                  padding: EdgeInsets.symmetric(vertical: 15),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.0),
                      color: const Color(0xFFf5f5f5)),
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text.rich(
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        TextSpan(
                          text: "${widget.match.homeTeam}",
                          style: TextStyles.textSize14,
                          children: const <TextSpan>[
                            TextSpan(
                              text: "【主】",
                              style: TextStyle(
                                color: Color(0xFFa7a7a7),
                                fontSize: Dimens.font_sp12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const LoadAssetImage(
                        'games/vs',
                        width: 17,
                        height: 11,
                        fit: BoxFit.contain,
                      ),
                      Text.rich(
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        TextSpan(
                          text: " ${widget.match.awayTeam}",
                          style: TextStyles.textSize14,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView(
                    children: [
                      Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 15, vertical: 15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  "胜平负",
                                  style: TextStyle(
                                      fontSize: Dimens.font_sp14,
                                      color: Color(0xFF807d7d)),
                                ),
                                Gaps.vGap12,
                                SPFLayout(
                                  widget.match,
                                  true,
                                  key: const Key("SPFLayout"),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            width: double.infinity,
                            height: 1,
                            color: const Color(0xFFededed),
                          ),
                          Container(
                            alignment: Alignment.centerLeft,
                            margin: const EdgeInsets.only(
                                top: 15, left: 15, right: 15),
                            child: const Text(
                              "比分",
                              style: TextStyle(
                                  fontSize: Dimens.font_sp14,
                                  color: Color(0xFF807d7d)),
                            ),
                          ),
                          BFLayout(
                            widget.match,
                            key: const Key("CBFLayout"),
                          ),
                          Container(
                            alignment: Alignment.centerLeft,
                            margin: EdgeInsets.only(left: 15, right: 15),
                            child: const Text(
                              "进球数",
                              style: TextStyle(
                                  fontSize: Dimens.font_sp14,
                                  color: Color(0xFF807d7d)),
                            ),
                          ),
                          JQSLayout(
                            widget.match,
                            true,
                            key: const Key("JQSLayoutdg"),
                          ),
                          Container(
                            alignment: Alignment.centerLeft,
                            margin: const EdgeInsets.only(left: 15, right: 15),
                            child: const Text(
                              "半全场",
                              style: TextStyle(
                                  fontSize: Dimens.font_sp14,
                                  color: Color(0xFF807d7d)),
                            ),
                          ),
                          BQCLayout(
                            widget.match,
                            true,
                            key: const Key("BQCLayout"),
                          )
                        ],
                      )
                    ],
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Expanded(
                        child: InkWell(
                      onTap: () {

                         widget.match.ftStateStore = ftStateStore;
                    model.notifyListeners();

                    NavigatorUtils.goBack(context);

                      },
                      child: Container(
                        height: 45,
                        alignment: Alignment.center,
                        color: const Color(0xFFc7c7c7),
                        child: const Text("取消"),
                      ),
                    )),
                    Expanded(
                        child: InkWell(
                      onTap: () {
                    NavigatorUtils.goBack(context);


                      },
                      child: Container(
                        height: 45,
                        alignment: Alignment.center,
                        color: const Color(0xFFfd4627),
                        child: const Text(
                          "确定",
                          style: TextStyles.textWhite14,
                        ),
                      ),
                    )),
                  ],
                ),
              ],
            ),
          );
        });
  }
}
