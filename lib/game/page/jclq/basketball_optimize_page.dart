import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/data/bet_commit.dart';
import 'package:sport_mobile/game/page/entity/game_summary.dart';
import 'package:sport_mobile/game/page/entity/jclq_entity.dart';
import 'package:sport_mobile/game/page/model/basketball_model.dart';
import 'package:sport_mobile/game/page/model/basketball_optimize_model.dart';
import 'package:sport_mobile/game/page/model/checkout_model.dart';
import 'package:sport_mobile/game/page/widgets/optimize_button.dart';
import 'package:sport_mobile/game/page/widgets/optimize_dialog.dart';
import 'package:sport_mobile/game/routers/game_router.dart';
import 'package:sport_mobile/login/model/user_model.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/widgets/load_image.dart';
import 'package:sport_mobile/widgets/optimize_app_bar.dart';

class BasketballOptimizePage extends StatefulWidget {
  const BasketballOptimizePage({super.key});

  @override
  BasketballOptimizeState createState() {
    return BasketballOptimizeState();
  }
}

class BasketballOptimizeState extends State<BasketballOptimizePage> {
  BasketballOptimizeModel provider = BasketballOptimizeModel();

  List<Widget> generateBetList(List<BetTouzhuItem> list) {
    List<Widget> _list = [];

    _list = list.map((e) {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                  child: Container(
                child: Text(
                  "${e.homeTeam}",
                  softWrap: true,
                  maxLines: 2,
                  style: TextStyles.textSize12,
                ),
              )),
              const Text(
                "vs",
                style: TextStyle(
                    color: Color(0xFF919191), fontSize: Dimens.font_sp10),
              ),
              Expanded(
                  child: Container(
                      child: Column(
                children: [
                  Text(
                    e.awayTeam,
                    softWrap: true,
                    style: TextStyles.textSize12,
                  ),
                ],
              ))),
            ],
          ),
          Row(
            children: [
              Container(
                child: Text(
                  "${e.name}",
                  softWrap: true,
                  style: TextStyles.textSize12,
                ),
              ),
              Container(
                child: Text(
                  "${e.sp}",
                  softWrap: true,
                  style: TextStyles.textGray12,
                ),
              ),
            ],
          )
        ],
      );
    }).toList();

    return _list;
  }

  numFilter(double value) {
    String realVal = '';
    realVal = value.toStringAsFixed(2);
    return realVal;
  }

  moneyAdd() {
    double money = provider.summary.totalAmount;
    money += 2;

    provider.summary.totalAmount = money;

    provider.calculateOptimize();
  }

  moneyMinus() {
    double money = provider.summary.totalAmount;

    if (money == 0 || money <= provider.minMoney) {
      money = provider.minMoney;
    } else {
      money -= 2;
    }

    provider.summary.totalAmount = money;

    provider.calculateOptimize();
  }

  List<Widget> generateMatchList() {
    List<Widget> _list = [];

    int _index = 0;
    _list = provider.optimizeMatchList.map((item) {
      _index++;

      return Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 15,
          vertical: 10,
        ),
        decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFe3e3e3),
              width: 1,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2, vertical: 1),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Color(0xFF688de8),
                          width: 1.0,
                        ),
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: Text(
                        item.chuan,
                        style: const TextStyle(
                            color: Color(0xFF688de8),
                            fontSize: Dimens.font_sp12),
                      ),
                    ),
                    ...generateBetList(item.list),
                  ],
                )),
            Expanded(
                flex: 1,
                child: Container(
                  //    color: Colors.red,
                  alignment: Alignment.center,
                  child: Text(
                    "${numFilter(item.price)}",
                    softWrap: true,
                    style: TextStyles.textSize12,
                  ),
                )),
            Expanded(
                flex: 2,
                child: Container(
                    alignment: Alignment.center,
                    child: DGBeisuItem(
                      item,
                      provider,
                      key: Key("beisu${_index}"),
                    ))),
            Expanded(
                flex: 1,
                child: Container(
                  alignment: Alignment.centerRight,
                  child: Text(
                    "${numFilter(item.total)}",
                    softWrap: true,
                    style: TextStyles.textSize12,
                  ),
                )),
          ],
        ),
      );
    }).toList();

    return _list;
  }

  bool optimizationType() {
    double count = provider.summary.totalAmount / 2;
    double bonus = 0;
    double thetotalBonus = 0;
    bool stated = true;
    double stprice = 0;

    provider.optimizeMatchList.forEach((BasketballOptimizeTouzhuItem item) {
      bonus += item.price;
      thetotalBonus += item.total;
    });

    if (count >= provider.optimizeMatchList.length * 2) {
      provider.optimizeMatchList.forEach((_item) {
        if (_item.total - (thetotalBonus / provider.optimizeMatchList.length) >
            (thetotalBonus / provider.optimizeMatchList.length) * 0.2) {
          stated = false;
          stprice = _item.price;
        }
      });
    }

    bool addsted = true;

    switch (provider.optimizeID) {
      case 0:
        if (stated) {
          addsted = true;
        } else {
          EasyLoading.showToast('当前有单注投注项中奖金额超过平均中奖金额的20%,该方案不适合平均优化。');
          addsted = false;
        }
        break;

      case 1:
        if (stated) {
          EasyLoading.showToast('投注项中奖金额相近(没有超过平均中奖金额的20%),该方案不适合博热优化');
          addsted = false;
        } else {
          if (stprice < (bonus / provider.optimizeMatchList.length)) {
            addsted = true;
          } else {
            EasyLoading.showToast('单注中奖金额最大的投注项赔率较高,该方案不适合博热优化,适用于搏冷优化');
            addsted = false;
          }
        }
        break;
      case 2:
        if (stated) {
          EasyLoading.showToast('投注项中奖金额相近(没有超过平均中奖金额的20%),该方案不适合博冷优化');
          addsted = false;
        } else {
          if (stprice >= (bonus / provider.optimizeMatchList.length)) {
            addsted = true;
          } else {
            EasyLoading.showToast('单注中奖金额最大的投注项赔率较低,该方案不适合博冷优化,适用于搏热优化');
            addsted = false;
          }
        }
        break;
      default:
    }

    return addsted;
  }

  void _showOptimizeDialog() {
    showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (_) => OptimizeDialog(
              () {},
            ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFf2f2f2),
        appBar: OptimizeMyAppBar(
          centerTitle: '奖金优化',
          backImgColor: Colors.white,
          isMore: true,
          icon: Icons.help_outline_sharp,
          onPressed: () {
            _showOptimizeDialog();
          },
        ),
        body: ChangeNotifierProvider<BasketballOptimizeModel>(create: (_) {
          BasketballModel basketballModel =
              Provider.of<BasketballModel>(context, listen: false);

          Map<String, JCLQEntity> cloneSelectedMatches = {};
          List<JCLQEntity> cloneConfirmMatches = [];

          basketballModel.selectedMatches.forEach((key, JCLQEntity _match) {
            cloneSelectedMatches[key] =
                JCLQEntity.fromObject(_match.toObject());
          });

          cloneConfirmMatches = basketballModel.confirmMatchList.map((_match) {
            return JCLQEntity.fromObject(_match.toObject());
          }).toList();

          provider.selectedMatches = cloneSelectedMatches;

          provider.confirmMatchList = cloneConfirmMatches;

          provider.chuanSelectTip = basketballModel.chuanSelectTip;

          provider.summary = GameSummary.fromJson(basketballModel.summary.toJson());

          provider.chuanSelectList = basketballModel.chuanSelectList;

          provider.isChuan = basketballModel.isChuan;

          provider.gameID = basketballModel.gameID;

          provider.updateMatchList();

          return provider;
        }, child:
            Consumer<BasketballOptimizeModel>(builder: (context, model, child) {
          return Column(
            children: [
              Container(
                width: double.infinity,
                height: 0.2,
                color: Colors.white,
              ),
              Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFF4268cb), Color(0xFF8c7eec)], // 渐变色数组
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                ),
                child: Container(
                  alignment: Alignment.center,
                  child: Column(
                    children: [
                      Gaps.vGap16,
                      Text.rich(
                        textAlign: TextAlign.start,
                        overflow: TextOverflow.ellipsis,
                        TextSpan(
                          text: "最小优化金额",
                          style: TextStyles.textWhite14,
                          children: <TextSpan>[
                            TextSpan(
                              text: "${model.minMoney.toStringAsFixed(0)}",
                              style: const TextStyle(
                                  color: Color(0xFFffb520),
                                  fontSize: Dimens.font_sp14,
                                  fontWeight: FontWeight.bold),
                            ),
                            const TextSpan(
                              text: "元",
                              style: TextStyles.textWhite14,
                            ),
                          ],
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          GestureDetector(
                            onTap: () {
                              moneyMinus();
                            },
                            child: LoadAssetImage(
                              "bet/optimize-",
                              height: 55,
                            ),
                          ),
                          Gaps.hGap16,
                          Text.rich(
                            textAlign: TextAlign.start,
                            overflow: TextOverflow.ellipsis,
                            TextSpan(
                              text: "购买 ",
                              style: TextStyles.textWhite14,
                              children: <TextSpan>[
                                TextSpan(
                                  text:
                                      "${provider.summary.totalAmount.toStringAsFixed(0)}",
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: Dimens.font_sp40,
                                      fontWeight: FontWeight.bold),
                                ),
                                TextSpan(
                                  text: " 元",
                                  style: TextStyles.textWhite14,
                                ),
                              ],
                            ),
                          ),
                          Gaps.hGap16,
                          GestureDetector(
                              onTap: () {
                                moneyAdd();
                              },
                              child: LoadAssetImage(
                                "bet/optimize+",
                                height: 55,
                              )),
                          Gaps.vGap16,
                        ],
                      ),
                      Gaps.vGap16,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          OptimizeButton(
                            provider.optimizeTypes.first,
                            "平均优化",
                            onPressed: () {
                              provider.chooseOptimizeTypes(0);
                            },
                            key: const Key("average"),
                          ),
                          Gaps.hGap10,
                          OptimizeButton(
                            provider.optimizeTypes[1],
                            "博热优化",
                            onPressed: () {
                              provider.chooseOptimizeTypes(1);
                            },
                            key: Key("hot"),
                          ),
                          Gaps.hGap10,
                          OptimizeButton(
                            provider.optimizeTypes[2],
                            "博冷优化",
                            onPressed: () {
                              provider.chooseOptimizeTypes(2);
                            },
                            key: Key("cold"),
                          ),
                        ],
                      ),
                      Gaps.vGap16,
                    ],
                  ),
                ),
              ),
              Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFF4268cb), Color(0xFF8c7eec)], // 渐变色数组
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                  ),
                  child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(15),
                        topRight: Radius.circular(15),
                      ),
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 15, vertical: 20),
                            alignment: Alignment.centerLeft,
                            color: Colors.white,
                            child: Text.rich(
                              textAlign: TextAlign.start,
                              overflow: TextOverflow.ellipsis,
                              TextSpan(
                                text: "已选",
                                style: TextStyles.textSize12,
                                children: <TextSpan>[
                                  TextSpan(
                                    text: "${model.confirmMatchList.length}",
                                    style: const TextStyle(
                                      color: Color(0xFFfe4727),
                                      fontSize: Dimens.font_sp12,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: "场比赛",
                                    style: TextStyles.textSize12,
                                  ),
                                  const TextSpan(
                                    text: "      过关方式：",
                                    style: TextStyles.textSize12,
                                  ),
                                  TextSpan(
                                    text: "${model.chuanSelectTip}",
                                    style: TextStyles.textSize12,
                                  ),
                                ],
                              ),
                            ),
                          )
                        ],
                      ))),
              Expanded(
                  child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                alignment: Alignment.center,
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 15, vertical: 2),
                      decoration: const BoxDecoration(
                        color: Color(0xFFf2f3f5),
                        border: Border(
                          bottom: BorderSide(
                            color: Color(0xFFe3e3e3),
                            width: 1,
                          ),
                          top: BorderSide(
                            color: Color(0xFFe3e3e3),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 2,
                              child: Container(
                                child: const Text(
                                  "投注选项",
                                  style: TextStyles.textSize12,
                                ),
                              )),
                          Expanded(
                              flex: 1,
                              child: Container(
                                ///  color: Colors.red,
                                alignment: Alignment.center,

                                child: Text(
                                  "单注奖金",
                                  style: TextStyles.textSize12,
                                ),
                              )),
                          Expanded(
                              flex: 2,
                              child: Container(
                                alignment: Alignment.center,
                                child: Text(
                                  "倍数",
                                  style: TextStyles.textSize12,
                                ),
                              )),
                          Expanded(
                              flex: 1,
                              child: Container(
                                alignment: Alignment.centerRight,
                                child: const Text(
                                  "奖金",
                                  style: TextStyles.textSize12,
                                ),
                              )),
                        ],
                      ),
                    ),
                    Expanded(
                        child: ListView(
                      children: [
                        ...generateMatchList(),
                      ],
                    ))
                  ],
                ),
              )),
              Container(
                color: Colors.white,
                height: 65,
                padding: EdgeInsets.only(left: 20),
                width: double.infinity,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                        flex: 2,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text.rich(
                              overflow: TextOverflow.ellipsis,
                              TextSpan(
                                text: "共",
                                style: TextStyles.textSize14,
                                children: <TextSpan>[
                                  TextSpan(
                                    text: "${model.summary.totalCount}",
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: Dimens.font_sp14,
                                    ),
                                  ),
                                  const TextSpan(
                                      text: "注 ", style: TextStyles.textSize14),
                                  TextSpan(
                                    text: (model.summary.totalAmount)
                                        .toStringAsFixed(2),
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: Dimens.font_sp14,
                                    ),
                                  ),
                                  const TextSpan(
                                      text: "元", style: TextStyles.textSize14),
                                ],
                              ),
                            ),
                            Text.rich(
                              overflow: TextOverflow.ellipsis,
                              TextSpan(
                                text: model.isChuan ? "奖金范围:" : "最高奖金:",
                                style: TextStyles.textSize12,
                                children: <TextSpan>[
                                  TextSpan(
                                    text: model.isChuan
                                        ? "${model.summary.totalMinMoney.toStringAsFixed(2)} ~ ${model.summary.totalMaxMoney.toStringAsFixed(2)}"
                                        : (model.summary.totalMaxMoney)
                                            .toStringAsFixed(2),
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: Dimens.font_sp12,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: " 元",
                                  ),
                                ],
                              ),
                            ),
                          ],
                        )),
                    Expanded(
                        flex: 1,
                        child: GestureDetector(
                          onTap: () {
                            if (model.optimizeMatchList.isEmpty) {
                              EasyLoading.showToast('没有选择正确的场次,无法投注');
                              return;
                            }
                            //limit maximum prize for DG
                            if (model.isChuan == false) {
                              UserModel userModel = Provider.of<UserModel>(
                                  context,
                                  listen: false);
                              double maximumAmount =
                                  userModel.getDGLimit().toDouble();
                              if (model.summary.totalAmount > maximumAmount) {
                                EasyLoading.showToast(
                                    '单关投注金额不能超过${maximumAmount}');
                                return;
                              }
                            }

                            bool optinizat = optimizationType();
                            if (!optinizat) {
                              provider.calculateOptimize();
                              return;
                            }

                            List<int> chuanList = [];
                            model.chuanSelectList.forEach((item) {
                              if (item.select) {
                                chuanList.add(item.value);
                              }
                            });

                            model.chuanList = chuanList;

                            model.readyCommit();
                            CheckoutModel checkoutModel =
                                Provider.of<CheckoutModel>(context,
                                    listen: false);
                            checkoutModel.fillupInfo(model.summary);

                            NavigatorUtils.push(
                                context, GameRouter.checkoutPage);
                          },
                          child: Container(
                            color: Colors.red,
                            alignment: Alignment.center,
                            child: const Text(
                              "确认下单",
                              style: TextStyle(
                                  fontSize: Dimens.font_sp18,
                                  color: Colors.white),
                            ),
                          ),
                        )),
                  ],
                ),
              )
            ],
          );
        })));
  }
}

class DGBeisuItem extends StatefulWidget {
  final BasketballOptimizeTouzhuItem touzhu;
  final BasketballOptimizeModel provider;

  DGBeisuItem(this.touzhu, this.provider, {super.key});

  @override
  _DGBeisuItemState createState() {
    return _DGBeisuItemState();
  }
}

class _DGBeisuItemState extends State<DGBeisuItem> {
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  int beisuIncrement(String beisu) {
    int count = int.tryParse(beisu) == null ? 0 : int.parse(beisu);

    count += 1;

    widget.touzhu.beisu = count.toDouble();

    widget.touzhu.total = widget.touzhu.beisu * widget.touzhu.price;

    widget.provider.noticeBeisuUpdate(widget.touzhu);

    return count;
    //  this.noticeBeisuUpdate()
  }

  int beisuDecrement(String beisu) {
    int count = widget.touzhu.beisu.toInt();
    if (count == 0) {
      count = 1;
    }
    count -= 1;

    widget.touzhu.beisu = count.toDouble();

    widget.touzhu.total = widget.touzhu.beisu * widget.touzhu.price;

    widget.provider.noticeBeisuUpdate(widget.touzhu);

    return count;
  }

  beisuChange(int newTimes) {
    widget.touzhu.beisu = newTimes.toDouble();

    widget.touzhu.total = widget.touzhu.beisu * widget.touzhu.price;

    widget.provider.noticeBeisuUpdate(widget.touzhu);
  }

  @override
  Widget build(BuildContext context) {
    _controller.text = widget.touzhu.beisu.toStringAsFixed(0);

    return Row(
      children: [
        Expanded(
            flex: 1,
            child: IconButton(
                iconSize: 20,
                padding: EdgeInsets.only(right: 2),
                constraints: BoxConstraints(),
                onPressed: () {
                  int _times = beisuDecrement(_controller.text);
                  _controller.text = _times.toString();
                },
                icon: const Icon(
                  Icons.do_not_disturb_on_outlined,
                  color: Color(0xFFee0a24),
                ))),
        Expanded(
            flex: 3,
            child: Container(
              alignment: Alignment.center,
              height: 20,
              child: TextField(
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                controller: _controller,
                onChanged: (newTimes) {
                  if (newTimes == "") {
                    EasyLoading.showToast("倍数不能为空");
                    return;
                  }
                  int times = int.parse(newTimes);
                  beisuChange(times);
                  return;
                },
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp('[0-9]'))
                ],
                decoration: InputDecoration(
                  //   hintText: "倍数",
                  hintStyle: TextStyles.textGray14,
                  focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(
                      color: Color(0xFFEEEEEE),
                      width: 1.0, // focused state border width
                    ),
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: const BorderSide(
                      color: Color(0xFFEEEEEE),
                      width: 1.0, // focused state border width
                    ),
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 5, vertical: 0),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                ),
              ),
            )),
        Expanded(
            flex: 1,
            child: IconButton(
                iconSize: 20,
                padding: EdgeInsets.only(left: 0),
                constraints: BoxConstraints(),
                onPressed: () {
                  int _times = beisuIncrement(_controller.text);
                  _controller.text = _times.toString();
                },
                icon: const Icon(
                  Icons.add_circle,
                  color: Color(0xFFee0a24),
                )))
      ],
    );

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        IconButton(
            padding: EdgeInsets.only(right: 2),
            constraints: BoxConstraints(),
            onPressed: () {
              int _times = beisuDecrement(_controller.text);
              _controller.text = _times.toString();
            },
            icon: const Icon(
              Icons.do_not_disturb_on_outlined,
              color: Color(0xFFee0a24),
              size: 22,
            )),
        SizedBox(
            width: 50,

            //  flex: 1,
            child: Container(
              alignment: Alignment.center,
              height: 20,
              child: TextField(
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                controller: _controller,
                onChanged: (newTimes) {
                  if (newTimes == "") {
                    EasyLoading.showToast("倍数不能为空");
                    return;
                  }
                  int times = int.parse(newTimes);
                  beisuChange(times);
                  return;
                },
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp('[0-9]'))
                ],
                decoration: InputDecoration(
                  //   hintText: "倍数",
                  hintStyle: TextStyles.textGray14,
                  focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(
                      color: Color(0xFFEEEEEE),
                      width: 1.0, // focused state border width
                    ),
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: const BorderSide(
                      color: Color(0xFFEEEEEE),
                      width: 1.0, // focused state border width
                    ),
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 5, vertical: 0),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                ),
              ),
            )),
        IconButton(
            padding: EdgeInsets.only(left: 0),
            constraints: BoxConstraints(),
            onPressed: () {
              int _times = beisuIncrement(_controller.text);
              _controller.text = _times.toString();
            },
            icon: const Icon(
              Icons.add_circle,
              color: Color(0xFFee0a24),
              size: 22,
            )),
      ],
    );
  }
}
