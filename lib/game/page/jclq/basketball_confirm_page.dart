import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/page/model/basketball_model.dart';
import 'package:sport_mobile/game/page/model/checkout_model.dart';
import 'package:sport_mobile/game/page/widgets/back_dialog.dart';
import 'package:sport_mobile/game/page/widgets/basketball/basketball_dg_confirm.dart';
import 'package:sport_mobile/game/page/widgets/basketball/basketball_dxf_confirm.dart';
import 'package:sport_mobile/game/page/widgets/basketball/basketball_ht_confirm.dart';
import 'package:sport_mobile/game/page/widgets/basketball/basketball_rfsf_confirm.dart';
import 'package:sport_mobile/game/page/widgets/basketball/basketball_sf_confirm.dart';
import 'package:sport_mobile/game/page/widgets/basketball/basketball_sfc_confirm.dart';
import 'package:sport_mobile/game/page/widgets/basketball_chuan_bottom_sheet.dart';
import 'package:sport_mobile/game/routers/game_router.dart';
import 'package:sport_mobile/login/model/user_model.dart';
import 'package:sport_mobile/res/dimens.dart';
import 'package:sport_mobile/res/gaps.dart';
import 'package:sport_mobile/res/styles.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/util/toast_utils.dart';
import 'package:sport_mobile/widgets/my_app_bar.dart';

import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

class BasketballConfirmPage extends StatefulWidget {
  const BasketballConfirmPage({super.key});

  @override
  BasketballConfirmPageState createState() {
    return BasketballConfirmPageState();
  }
}

class BasketballConfirmPageState extends State<BasketballConfirmPage> {
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();

    BasketballModel model =
        Provider.of<BasketballModel>(context, listen: false);
    _controller.text = model.summary.times.toStringAsFixed(0);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _showChuanBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      builder: (BuildContext context) {
        return const BasketballChuanBottomSheet(
          key: Key('basketballChuanBottomSheet'),
        );
      },
    );
  }

  Widget generateBasketballConfirmLayout(String typeKey) {
    Widget _widget;
    switch (typeKey) {
      case 'ht':
        _widget = const BasketballHTConfirm();
        break;

      case 'dxf':
        _widget = const BasketballDXFConfirm();
        break;

      case 'sf':
        _widget = const BasketballSFConfirm();
        break;

      case 'sfc':
        _widget = const BasketballSFCConfirm();
        break;

      case 'rfsf':
        _widget = const BasketballRFSFConfirm();
        break;

      case 'dg':
        _widget = const BasketballDGConfirm();
        break;

      default:
        _widget = const BasketballDGConfirm();
    }
    return _widget;
  }

  void _showExitDialog() {
    BasketballModel provider =
        Provider.of<BasketballModel>(context, listen: false);

    showDialog<void>(
        context: context,
        builder: (_) => BackDialog(
              onBackPressed: () {
                provider.flushExit();
                Navigator.of(context).pop(true);
              },
            ));
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BasketballModel>(builder: (context, model, child) {
      return Scaffold(
        backgroundColor: const Color(0xFFf2f2f2),
        appBar: MyAppBar(
          title: "${BasketballMatchTypes.values[model.cate.typeKey]!.name}投注单",
          backgroundColor: Colors.red,
          backImgColor: Colors.white,
          actionName: "奖金优化",
          isShowDialog: true,
          onPressed: () {
            NavigatorUtils.push(context, GameRouter.basketballOptimizePage);
          },
          onBackPressed: () {
            _showExitDialog();
          },
        ),
        body: Column(children: [
          Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF4268cb), Color(0xFF8c7eec)],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(10),
                  topRight: Radius.circular(10),
                ),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
                  color: Colors.white,
                  //alignment: Alignment.center,
                  child: Text.rich(
                    TextSpan(
                      text: "截止时间: ",
                      style: const TextStyle(fontSize: Dimens.font_sp14),
                      children: <TextSpan>[
                        TextSpan(
                          text: model.stopSaleTime,
                          style: const TextStyle(
                            color: Color(0xFFfe4727),
                            fontSize: Dimens.font_sp12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              )),
          Expanded(child: generateBasketballConfirmLayout(model.cate.typeKey!)),
          Container(
              color: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              width: double.infinity,
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                        flex: 1,
                        child: model.isChuan == true
                            ? InkWell(
                                onTap: () {
                                  _showChuanBottomSheet();
                                },
                                child: Stack(
                                  children: [
                                    Container(
                                      alignment: Alignment.center,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 5),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: const Color(
                                              0xFFdcdcdc), // 边框颜色为红色
                                          width: 1, // 边框宽度为1像素
                                        ),
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(
                                            10.0), // 设置圆角大小
                                      ),
                                      child: Text(model.chuanSelectTip),
                                    ),
                                    const Positioned(
                                        right: 0,
                                        top: 0,
                                        bottom: 0,
                                        child: Icon(
                                          Icons.keyboard_arrow_down_sharp,
                                          color: Color(0xFFCCCCCC),
                                        ))
                                  ],
                                ),
                              )
                            : const Text(
                                "每项都买",
                                style: TextStyles.textSize14,
                              )),
                    Gaps.hGap10,
                    Expanded(
                        flex: 1,
                        child: Row(
                          children: [
                            IconButton(
                                onPressed: () {
                                  model.timesDecrement(_controller.text);
                                  _controller.text =
                                      model.beisuTotalInput.toString();
                                },
                                icon: const Icon(
                                  Icons.do_not_disturb_on_outlined,
                                  color: Color(0xFFee0a24),
                                )),
                            Expanded(
                                child: Container(
                              alignment: Alignment.center,
                              height: 30,
                              child: TextField(
                                keyboardType: TextInputType.number,
                                textAlign: TextAlign.center,
                                controller: _controller,
                                onChanged: (newTimes) {
                                  if (newTimes == "") {
                                    Toast.show("倍数不能为空");
                                    return;
                                  }
                                  int times = int.parse(newTimes);
                                  model.changeBetTimes(times);
                                  return;
                                },
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                      RegExp('[0-9]'))
                                ],
                                decoration: InputDecoration(
                                  hintText: "倍数",
                                  hintStyle: TextStyles.textGray14,
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: const BorderSide(
                                      color: Color(0xFFEEEEEE),
                                      width: 1.0, // focused state border width
                                    ),
                                    borderRadius: BorderRadius.circular(30.0),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 5, vertical: 0),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(30.0),
                                  ),
                                ),
                              ),
                            )),
                            IconButton(
                                onPressed: () {
                                  model.timesIncrement(_controller.text);
                                  _controller.text =
                                      model.beisuTotalInput.toString();
                                },
                                icon: const Icon(
                                  Icons.add_circle,
                                  color: Color(0xFFee0a24),
                                )),
                            const Text(
                              "倍",
                              style: TextStyles.textSize14,
                            )
                          ],
                        ))
                  ])),
          Container(
            height: 65,
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(
                top: BorderSide(
                  color: Color(0xFFe3e3e3),
                  width: 0.5,
                ),
              ),
            ),
            width: double.infinity,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: GestureDetector(
                      onTap: () {
                        model.flushAllSelected();
                      },
                      child: Container(
                        width: double.infinity,
                        decoration: const BoxDecoration(
                          //    color: Colors.red,
                          border: Border(
                            right: BorderSide(
                              color: Color(0xFFe3e3e3),
                              width: 0.5,
                            ),
                          ),
                        ),
                        alignment: Alignment.center,
                        child: const Text(
                          "清空",
                          style: TextStyles.textSize14,
                        ),
                      )),
                ),
                Gaps.hGap10,
                Expanded(
                    flex: 5,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text.rich(
                          overflow: TextOverflow.ellipsis,
                          TextSpan(
                            text: "共",
                            style: TextStyles.textSize14,
                            children: <TextSpan>[
                              TextSpan(
                                text: "${model.summary.totalCount}",
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: Dimens.font_sp14,
                                ),
                              ),
                              const TextSpan(
                                  text: "注 ", style: TextStyles.textSize14),
                              TextSpan(
                                text: (model.summary.totalAmount)
                                    .toStringAsFixed(2),
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: Dimens.font_sp14,
                                ),
                              ),
                              const TextSpan(
                                  text: "元", style: TextStyles.textSize14),
                            ],
                          ),
                        ),
                        Text.rich(
                          overflow: TextOverflow.ellipsis,
                          TextSpan(
                            text: model.isChuan ? "奖金范围:" : "最高奖金:",
                            style: TextStyles.textSize12,
                            children: <TextSpan>[
                              TextSpan(
                                text: model.isChuan
                                    ? "${model.summary.totalMinMoney.toStringAsFixed(2)} ~ ${model.summary.totalMaxMoney.toStringAsFixed(2)}"
                                    : (model.summary.totalMaxMoney)
                                        .toStringAsFixed(2),
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: Dimens.font_sp12,
                                ),
                              ),
                              const TextSpan(
                                  text: " 元", style: TextStyles.textSize12),
                            ],
                          ),
                        ),
                      ],
                    )),
                Expanded(
                    flex: 4,
                    child: GestureDetector(
                      onTap: () {
                        //limit maximum prize for DG
                        if (model.isChuan == false) {
                          UserModel userModel =
                              Provider.of<UserModel>(context, listen: false);
                          double maximumAmount =
                              userModel.getDGLimit().toDouble();
                          if (model.summary.totalAmount > maximumAmount) {
                            EasyLoading.showToast('单关投注金额不能超过${maximumAmount}');
                            return;
                          }
                        }

                        if (model.summary.totalCount > 5000) {
                          EasyLoading.showToast('投注数量不能超过5000注');
                          return;
                        }

                        List<int> chuanList = [];

                        model.chuanSelectList.forEach((item) {
                          if (item.select) {
                            chuanList.add(item.value);
                          }
                        });

                        if (chuanList.isEmpty) {
                          if (model.confirmMatchList.isEmpty) {
                            EasyLoading.showToast('请选择比赛');
                            return;
                          } else if (model.isChuan &&
                              model.confirmMatchList.length < 2) {
                            EasyLoading.showToast('请选择至少2场比赛');
                            return;
                          }

                          EasyLoading.showToast('请选择投注方式');
                          return;
                        }

                        model.chuanList = chuanList;

                        model.readyCommit();
                        CheckoutModel checkoutModel =
                            Provider.of<CheckoutModel>(context, listen: false);
                        checkoutModel.fillupInfo(model.summary);
                        NavigatorUtils.push(context, GameRouter.checkoutPage);
                      },
                      child: Container(
                        color: Colors.red,
                        alignment: Alignment.center,
                        child: const Text(
                          "确认下单",
                          style: TextStyle(
                              fontSize: Dimens.font_sp18, color: Colors.white),
                        ),
                      ),
                    )),
              ],
            ),
          )
        ]),
      );
    });
  }
}
