import 'package:flutter/material.dart';

import 'package:flutter/services.dart';

import 'package:provider/provider.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/page/model/checkout_model.dart';
import 'package:sport_mobile/game/page/model/jqc_model.dart';
import 'package:sport_mobile/game/page/widgets/back_dialog.dart';
import 'package:sport_mobile/game/page/widgets/jqc_confirm_list.dart';
import 'package:sport_mobile/game/routers/game_router.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/util/toast_utils.dart';
import 'package:sport_mobile/widgets/Custom_Button.dart';
import 'package:sport_mobile/widgets/my_app_bar.dart';

class JQCConfirmPage extends StatefulWidget {
  const JQCConfirmPage({super.key});

  @override
  _JQCConfirmPageState createState() {
    return _JQCConfirmPageState();
  }
}

class _JQCConfirmPageState extends State<JQCConfirmPage> {

  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();


    JQCModel model = Provider.of<JQCModel>(context,listen: false);

    _controller.text = model.summary.times.toStringAsFixed(0);

    // 添加监听器
    _controller.addListener(() {
      int? _times = 0;
      String text = _controller.text;

      if (text.isNotEmpty) {
        _times = int.tryParse(text);
        if (_times == null) {
          _controller.text = '1';
          _controller.selection = TextSelection.fromPosition(
              TextPosition(offset: _controller.text.length));
        }
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }


  void _showExitDialog() {
    JQCModel model = Provider.of<JQCModel>(context,listen: false);


      showDialog<void>(
          context: context,
          builder: (_) => BackDialog(
                onBackPressed: () {
                  model.flushExit();
                  Navigator.of(context).pop(true);
                },
              ));
    }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFf2f2f2),
        appBar: MyAppBar(
          centerTitle: '四场进球',
          backImgColor: Colors.white,
          isMore: false,
          isShowDialog: true,
          onBackPressed: (){
            _showExitDialog();
          },
        ),
        body: Stack(
          children: [
            Container(
              child: Column(
                children: [
                  Consumer<JQCModel>(builder: (context, model, child) {
                    return Container(
                      color: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "【第${model.termNo}期】",
                            style: TextStyles.textSize12,
                          ),
                          Text(
                            "截止时间${model.stopTime}",
                            style: TextStyles.textSize12,
                          ),
                        ],
                      ),
                    );
                  }),
                  const Expanded(
                    child: JQCConfirmList(
                      MatchDataType.Jqc,
                      key: Key("jqc"),
                    ),
                  ),
                  Consumer<JQCModel>(builder: (context, model, child) {
                    return Column(
                      children: [
                        Container(
                            color: Colors.white,
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 5),
                            width: double.infinity,
                            child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Row(
                                    children: [
                                      Gaps.hGap12,
                                      CustomButton(
                                        onPressed: () {
                                          model.flushAllSelected();
                                          Navigator.pop(context);
                                        },
                                        text: "清空重选",
                                        iconData: Icons.delete_outline,
                                        radius: 4,
                                        textColor: Color(0xFFee0a24),
                                        bgColor: Colors.white,
                                        padding: EdgeInsets.all(4),
                                        side: BorderSide(
                                            color: Color(0xFFee0a24)),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          model
                                              .timesDecrement(_controller.text);
                                          _controller.text =
                                              model.summary.times.toString();
                                        },
                                        child: Container(
                                          child: const Icon(
                                            Icons.do_not_disturb_on_outlined,
                                            color: Color(0xFFee0a24),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        alignment: Alignment.center,
                                        width: 100,
                                        height: 30,
                                        child: TextField(
                                          keyboardType: TextInputType.number,
                                          textAlign: TextAlign.center,
                                          controller: _controller,
                                          onChanged: (newTimes) {
                                            if (newTimes == "") {
                                              Toast.show("倍数不能为空");
                                              return;
                                            }
                                            int times = int.parse(newTimes);
                                            model.changeBetTimes(times);
                                            return;
                                          },
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(
                                                RegExp('[0-9]'))
                                          ],
                                          decoration: InputDecoration(
                                            hintText: "倍数",
                                            hintStyle: TextStyles.textGray14,
                                            focusedBorder: OutlineInputBorder(
                                              borderSide: const BorderSide(
                                                color: Color(0xFFEEEEEE),
                                                width:
                                                    1.0, // focused state border width
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(30.0),
                                            ),
                                            contentPadding:
                                                const EdgeInsets.symmetric(
                                                    horizontal: 5, vertical: 0),
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(30.0),
                                            ),
                                          ),
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          model
                                              .timesIncrement(_controller.text);

                                          _controller.text =
                                              model.summary.times.toString();
                                        },
                                        child: Container(
                                          child: const Icon(
                                            Icons.add_circle,
                                            color: Color(0xFFee0a24),
                                          ),
                                        ),
                                      ),
                                      Text("倍")
                                    ],
                                  )
                                ])),
                        Container(
                          color: Colors.white,
                          height: 65,
                          //     padding: EdgeInsets.symmetric(horizontal: 20,vertical: 10),
                          width: double.infinity,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                  flex: 3,
                                  child: Container(
                                    padding: EdgeInsets.only(left: 20),
                                    child: Text.rich(
                                      overflow: TextOverflow.ellipsis,
                                      TextSpan(
                                        text: "共 ",
                                        style: TextStyles.textBold14,
                                        children: <TextSpan>[
                                          TextSpan(
                                            text: "${model.summary.totalCount}",
                                            style: const TextStyle(
                                              color: Colors.red,
                                              fontSize: Dimens.font_sp16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const TextSpan(
                                            text: " 注",
                                          ),
                                          TextSpan(
                                              text: " ${model.summary.times}",
                                              style: const TextStyle(
                                                color: Colors.red,
                                                fontSize: Dimens.font_sp16,
                                                fontWeight: FontWeight.bold,
                                              )),
                                          TextSpan(
                                            text: " 倍",
                                          ),
                                          TextSpan(
                                              text:
                                                  " ${model.summary.totalAmount}",
                                              style: const TextStyle(
                                                color: Colors.red,
                                                fontSize: Dimens.font_sp16,
                                                fontWeight: FontWeight.bold,
                                              )),
                                          TextSpan(
                                            text: " 元",
                                          ),
                                        ],
                                      ),
                                    ),
                                  )),
                              Expanded(
                                  flex: 2,
                                  child: GestureDetector(
                                    onTap: () {
                                      if (model.totalMatchCount < 4) {
                                        Toast.show('至少选择4场比赛',
                                            position: "center");
                                        return;
                                      }
                                    

                                      model.readyCommit(MatchDataType.Jqc);
                                      CheckoutModel checkoutModel =
                                          Provider.of<CheckoutModel>(context,
                                              listen: false);
                                      checkoutModel.fillupInfo(model.summary);

                                      NavigatorUtils.push(
                                          context, GameRouter.checkoutPage);
                                    },
                                    child: Container(
                                      color: Colors.red,
                                      alignment: Alignment.center,
                                      child: const Text(
                                        "确认下单",
                                        style: TextStyle(
                                            fontSize: Dimens.font_sp18,
                                            color: Colors.white),
                                      ),
                                    ),
                                  )),
                            ],
                          ),
                        ),
                      ],
                    );
                  })
                ],
              ),
            ),
            // ...JCToogleMenu.getMenuWidgets(isMenuVisible, context, () {
            //   setState(() {
            //     isMenuVisible = !isMenuVisible;
            //   });
            // }),
          ],
        ));
  }
}
