import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sport_mobile/game/data/match_data.dart';
import 'package:sport_mobile/game/page/model/jqc_model.dart';
import 'package:sport_mobile/game/page/widgets/ft_chuan_bottom_sheet.dart';
import 'package:sport_mobile/game/page/widgets/jqc_list_refresh.dart';
import 'package:sport_mobile/game/page/widgets/slider_down_menu.dart';
import 'package:sport_mobile/game/routers/game_router.dart';
import 'package:sport_mobile/order/order_router.dart';
import 'package:sport_mobile/res/resources.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/util/toast_utils.dart';
import 'package:sport_mobile/widgets/multi_actions_app_bar.dart';
import 'package:sport_mobile/widgets/my_app_bar.dart';

class JQCPage extends StatefulWidget {
  const JQCPage({super.key});

  @override
  _JQCPageState createState() {
    return _JQCPageState();
  }
}

class _JQCPageState extends State<JQCPage> {
  SliderController filterController = SliderController();
  SliderController sliderController = SliderController();

  @override
  void initState() {
    super.initState();

    JQCModel model = Provider.of<JQCModel>(context, listen: false);
    model.gameID = MatchDataType.Jqc;
  }

  @override
  void dispose() {
    sliderController.dispose();
    filterController.dispose();
    super.dispose();
  }


  List<Widget> generateLeagueList(JQCModel model, List<String> boutList) {
    List<Widget> leagueList = [];
    boutList.asMap().forEach((index, item) {


      Widget _button = FTButtonChuanIndivalItem(
        item,
        isActived: model.termNo == item ? true : false,
        key: Key(item),
        onPressed: () {
          model.changeBoutData(item);
        },
      );

      leagueList.add(_button);
    });

    return leagueList;
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
        backgroundColor: const Color(0xFFf2f2f2),
        appBar: MultiActionsAppBar(
          title: '四场进球',
          backImgColor: Colors.white,
          actions: [
            GestureDetector(
              onTap: () {
                if (filterController.isShow) {
                  filterController.hide();
                } else {
                  filterController.show();
                }
              },
              child: Container(
                  alignment: Alignment.centerRight,
                  child: const Icon(
                    Icons.calendar_today,
                    color: Colors.white,
                  )),
            ),
            Gaps.hGap10,
            GestureDetector(
                onTap: () {
                  if (sliderController.isShow) {
                    sliderController.hide();
                  } else {
                    sliderController.show();
                  }
                },
                child: Container(
                    alignment: Alignment.centerRight,
                    child: const Icon(
                      Icons.more_horiz,
                      color: Colors.white,
                    ))),
          ],
          onBackPressed: () {
            JQCModel model = Provider.of<JQCModel>(context, listen: false);
            model.flushExit();
          },
        ),
        body: Stack(
          children: [
            Container(
              child: Column(
                children: [
                  Consumer<JQCModel>(builder: (context, model, child) {
                    return Container(
                      color: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "第${model.termNo}期",
                            style: TextStyles.textGray12,
                          ),
                          Text(
                            "截止时间${model.stopTime}",
                            style: TextStyles.textGray12,
                          ),
                        ],
                      ),
                    );
                  }),
                  const Expanded(
                    child: JQCListRefresh(
                      MatchDataType.Jqc,
                      key: Key("jqc"),
                    ),
                  ),
                  Consumer<JQCModel>(builder: (context, model, child) {
                    return Container(
                      color: Colors.white,
                      height: 65,
                      //     padding: EdgeInsets.symmetric(horizontal: 20,vertical: 10),
                      width: double.infinity,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            flex: 1,
                            child: GestureDetector(
                                onTap: () {
                                  model.flushAllSelected();
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    "清空",
                                    style: TextStyles.textSize14,
                                  ),
                                )),
                          ),
                          Container(
                            height: 40,
                            width: 1,
                            color: Color(0xFFeeeeee),
                          ),
                          Expanded(
                              flex: 3,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text.rich(
                                    TextSpan(
                                      text: "已经选择 ",
                                      style: TextStyles.textBold14,
                                      children: <TextSpan>[
                                        TextSpan(
                                          text: "${model.totalMatchCount}",
                                          style: const TextStyle(
                                            color: Colors.red,
                                            fontSize: Dimens.font_sp16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        TextSpan(
                                          text: " 场比赛",
                                        ),
                                      ],
                                    ),
                                  ),
                                  const Text(
                                    "主客队至少各选1个进球数",
                                    style: TextStyles.textGray12,
                                  ),
                                ],
                              )),
                          Expanded(
                              flex: 2,
                              child: GestureDetector(
                                onTap: () {
                                  if (model.totalMatchCount < 4) {
                                    Toast.show('至少选择4场比赛', position: "center");
                                    return;
                                  }

                                  NavigatorUtils.pushResult(
                                        context, GameRouter.jqcConfirmPage,
                                        (obj) {
                                      model.refreshData();
                                    });
                                  
                                },
                                child: Container(
                                  color: Colors.red,
                                  alignment: Alignment.center,
                                  child: const Text(
                                    "选好了",
                                    style: TextStyle(
                                        fontSize: Dimens.font_sp18,
                                        color: Colors.white),
                                  ),
                                ),
                              )),
                        ],
                      ),
                    );
                  })
                ],
              ),
            ),
            SliderDownMenu(
              sliderController: sliderController,
              child: Container(
                color: Colors.white,
                child: Column(mainAxisSize: MainAxisSize.min, children: [
                  GestureDetector(
                    onTap: () {
                      NavigatorUtils.push(
                          context, '${OrderRouter.orderPage}?action=all');
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 10, horizontal: 10),
                      child: const Row(
                        children: [
                          Icon(
                            Icons.description,
                            color: Color(0xFFfd4627),
                          ),
                          Gaps.hGap8,
                          Text(
                            "投注记录",
                            style: TextStyles.textSize16,
                          )
                        ],
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      NavigatorUtils.push(context,
                          "${GameRouter.gameHelperPage}/${MatchDataType.Jqc}");
                    },
                    child: Container(
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(
                            color: Color(0xFFe3e3e3),
                            width: 1,
                          ),
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(
                          vertical: 10, horizontal: 10),
                      child: const Row(
                        children: [
                          Icon(
                            Icons.error,
                            color: Color(0xFFfd4627),
                          ),
                          Gaps.hGap8,
                          Text(
                            "玩法介绍",
                            style: TextStyles.textSize16,
                          )
                        ],
                      ),
                    ),
                  ),
                ]),
              ),
            ),

            Consumer<JQCModel>(builder: (context, model, child) {
              return SliderDownMenu(
                sliderController: filterController,
                key: const Key("filter"),
                child: Container(
                  color: Colors.white,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 15, vertical: 10),
                    child: GridView.count(
                      crossAxisCount: 3, // 每行显示的列数
                      childAspectRatio: 3.2, // 宽高比例
                      crossAxisSpacing: 10,
                      mainAxisSpacing: 10,
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(), // 禁止滚动

                      children: generateLeagueList(model, model.boutList),
                    ),
                  ),
                ),
              );
            })

          ],
        ));
  }
}
