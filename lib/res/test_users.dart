import 'package:flutter/cupertino.dart';

class TestAccount {
  final String name;
  final String password;

  TestAccount(this.name, this.password);
}

class AccountManager {
  final List<TestAccount> accounts = [
    TestAccount("***********", "a123456"),
    TestAccount("***********", "aa112233"),
    TestAccount("***********", "a12345678"),
    TestAccount("***********", "a123456"),
    TestAccount("***********", "a123456"),
    TestAccount("***********", "a123456"),
    TestAccount("***********", "zhy119"),
    TestAccount("***********", "a123456"),
  ];

  void setAccount(int index, TextEditingController nameController, TextEditingController passwordController) {
    if (index >= 0 && index < accounts.length) {
      nameController.text = accounts[index].name;
      passwordController.text = accounts[index].password;
    }
  }
}
