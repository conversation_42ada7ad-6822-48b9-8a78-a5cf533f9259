import 'dart:async';
import 'package:flutter/material.dart';

import 'package:sport_mobile/res/resources.dart';



//向上滚动插件

class Marquee extends StatefulWidget {
  final List<String>? prizeList;

  final Color? color;

  final double? fontSize;

  const Marquee({this.prizeList, this.color, this.fontSize, Key? key}) : super(key: key);

  @override
  _MarqueeState createState() => _MarqueeState();
}

class _MarqueeState extends State<Marquee> {
   List<String> textList = [
    '恭喜用户188****635竞彩篮球中奖57800.00元',
    '恭喜用户188****6353竞彩篮球中奖57800.00元',
    '这是第恭喜用户12288****635竞彩篮球中奖57800',
    '恭喜用户188****6315竞彩篮球中奖57800.00元',
    '恭喜用户188****6325竞彩篮球中奖57800.00元',
  ];

  final ScrollController _scrollController = ScrollController();
  Timer? _timer;
  int _currentIndex = 0;

  @override
  void initState() {
    if (widget.prizeList  != null  && widget.prizeList!.isNotEmpty) {
        textList = widget.prizeList!;
    }

    super.initState();
    startAutoScroll();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void startAutoScroll() {
    _timer = Timer.periodic(Duration(seconds: 2), (timer) {
      setState(() {
        _currentIndex = (_currentIndex + 1) % textList.length;
        _scrollToIndex(_currentIndex);
      });
    });
  }

  void _scrollToIndex(int index) {
    if (index >= 0 && index < textList.length) {
      _scrollController.animateTo(
        index * 25.0, // 假设每行文字的高度为48
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );

      double index2 = index * 25.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 25,
      child: ListView.builder(
        padding: EdgeInsets.zero,
        controller: _scrollController,
        scrollDirection: Axis.vertical,
        itemCount: textList.length,
        itemBuilder: (context, index) {
          final itemText = textList[index];
          return GestureDetector(
              onTap: () {
                // 处理点击事件
                print('点击了文字: $itemText');
              },
              child: Container(
                alignment: Alignment.centerLeft,
                height: 25,
                child: Text(
                  itemText,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      fontSize: widget.fontSize ?? Dimens.font_sp10,
                      color: widget.color ?? Color(0xFFaa2921)),
                  textAlign: TextAlign.start,
                ),
              ));
        },
      ),
    );
  }
}



/// 跑马灯 Builder 类型
/// 跑马灯 Builder 类型
typedef MarqueeWidgetBuilder = Widget Function(BuildContext context, int index, BoxConstraints constraints);

/// 跑马灯
class MarqueeWidget extends StatefulWidget {
  /// 跑马灯
  MarqueeWidget({
    Key? key,
    this.title,
    this.controller,
    this.duration = const Duration(milliseconds: 350),
    this.durationOffset = 30,
    required this.itemCount,
    required this.itemBuilder,
    required this.separatorBuilder,
    required this.edgeBuilder,
  }) : super(key: key);

  String? title;

  int itemCount;

  /// item builder
  MarqueeWidgetBuilder itemBuilder;

  /// 边界(前后新增) builder
  MarqueeWidgetBuilder edgeBuilder;

  /// item 间距 builder
  MarqueeWidgetBuilder separatorBuilder;

  /// 控制器
  ScrollController? controller;

  /// 定时器运行间隔
  Duration? duration;

  /// 定时器运行间隔移动偏移量
  double? durationOffset;

  @override
  _MarqueeWidgetState createState() => _MarqueeWidgetState();
}

class _MarqueeWidgetState extends State<MarqueeWidget> {
  ScrollController? _scrollController;

  final _globalKey = GlobalKey();
  Timer? _timer;

  @override
  void initState() {
    _scrollController = widget.controller ?? ScrollController();
    _initTimer();
    super.initState();
  }

  @override
  void dispose() {
    _cancelTimer();
    _scrollController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.itemCount == 0) {
      return Container();
    }

    final totalCount = widget.itemCount + 2;

    return LayoutBuilder(
      builder: (context, constraints) {
        return ListView.separated(
          key: _globalKey,
          controller: _scrollController,
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.all(0),
          itemCount: totalCount,
          itemBuilder: (context, index) {
            final isEdge = (index == 0 || index == totalCount - 1);
            if (isEdge) {
              return widget.edgeBuilder(context, index, constraints);
            }
            return widget.itemBuilder(context, index - 1, constraints);
          },
          separatorBuilder: (context, index) {
            if (index == 0 || index == totalCount - 2) {
              return Container();
            }
            return widget.separatorBuilder(context, index, constraints);
          },
        );
      },
    );
  }

  /// 取消定时器
  _cancelTimer({bool isContinue = false}) {
    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
      if (isContinue) {
        _initTimer();
      }
    }
  }

  /// 初始化定时任务
  _initTimer() {
    if (_timer == null) {
      final duration = widget.duration ?? Duration(milliseconds: 350);
      _timer = Timer.periodic(duration, (t) {
        if (_scrollController == null) {
          return;
        }

        // 滚动距离
        final val = _scrollController!.offset + (widget.durationOffset ?? 30);

        // 检查是否滚动到边界（右侧）
        if (_scrollController!.position.pixels >= _scrollController!.position.maxScrollExtent) {
          // 滚动到最后一个 item 时无缝跳转到头部
          _scrollController!.jumpTo(0);
        } else {
          // 平滑滚动
          _scrollController!.animateTo(val, duration: duration, curve: Curves.linear);
        }
      });
    }
  }
}
