import 'package:flutter/material.dart';
import 'package:sport_mobile/res/dimens.dart';
import 'package:sport_mobile/res/gaps.dart';
import 'package:sport_mobile/widgets/load_image.dart';

class  MyCustomButton extends StatelessWidget {
  final String text;
  final double fontSize;
  final Color? textColor;
  final Color? iconColor;
  final Color? disabledTextColor;
  final Color? bgColor;
  final Color? disabledBackgroundColor;
  final double? minHeight;
  final double? minWidth;
  final EdgeInsetsGeometry padding;
  final double radius;
  final Border? border;
  final bool gradient;
  final LoadAssetImage? iconImg;
  final Icon? customIcon;
  final VoidCallback? onPressed;

  const MyCustomButton({
    super.key,
    this.text = '',
    this.fontSize = Dimens.font_sp12,
    this.textColor,
    this.iconColor,
    this.disabledTextColor,
    this.bgColor = const Color(0xFFfe8320),
    this.disabledBackgroundColor,
    this.minHeight = 30.0,
    //this.minWidth = double.infinity,
    this.minWidth = 30,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0),
    this.radius = 10.0,
    this.gradient = false,
    this.iconImg, 
    this.customIcon,
    this.border,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        alignment: Alignment.center,
       // width: minWidth,
          padding: padding,
          decoration: BoxDecoration(
              color: bgColor ?? bgColor,
              border: border ,
              borderRadius: BorderRadius.circular(radius)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (iconImg != null)
                  iconImg!,
              if (customIcon != null) customIcon!,
              Gaps.hGap4,
              Text(
                text,
                style: TextStyle(fontSize: fontSize,color: textColor),
              ),
            ],
          )),
    );
  }
}



class  GGLDialogButton extends StatelessWidget {
  final String text;
  final double fontSize;
  final Color? textColor;
  final Color? iconColor;
  final Color? disabledTextColor;
  final Color? bgColor;
  final Color? disabledBackgroundColor;
  final double? minHeight;
  final double? minWidth;
  final EdgeInsetsGeometry padding;
  final double radius;
  final Border? border;
  final bool gradient;
  final LoadAssetImage? iconImg;
  final Icon? customIcon;
  final VoidCallback? onPressed;

  const GGLDialogButton({
    super.key,
    this.text = '',
    this.fontSize = Dimens.font_sp12,
    this.textColor,
    this.iconColor,
    this.disabledTextColor,
    this.bgColor = const Color(0xFFfed7b3),
    this.disabledBackgroundColor,
    this.minHeight = 30.0,
    //this.minWidth = double.infinity,
    this.minWidth = 30,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0),
    this.radius = 10.0,
    this.gradient = false,
    this.iconImg, 
    this.customIcon,
    this.border,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        alignment: Alignment.center,
        width: minWidth,
          padding: padding,
          decoration: BoxDecoration(
              color: bgColor ?? bgColor,
              border: border ,
              borderRadius: BorderRadius.circular(radius)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (iconImg != null)
                  iconImg!,
              if (customIcon != null) customIcon!,
              Gaps.hGap4,
              Text(
                text,
                style: TextStyle(fontSize: fontSize,color: textColor),
              ),
            ],
          )),
    );
  }
}