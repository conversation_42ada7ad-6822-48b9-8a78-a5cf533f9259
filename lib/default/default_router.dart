import 'package:fluro/fluro.dart';
import 'package:sport_mobile/default/page/default_page.dart';
import 'package:sport_mobile/routers/i_router.dart';

class DefaultRouter implements IRouterProvider{

 // static String defaultPage = '/default';
  
  @override
  void initRouter(FluroRouter router) {
//    router.define(defaultPage, handler: Handler(handlerFunc: (_, __) => const DefaultPage()));
  //  router.define(auditResultPage, handler: Handler(handlerFunc: (_, __) => const StoreAuditResultPage()));
  }
  
}
