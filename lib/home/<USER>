import 'dart:async';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sport_mobile/config/api_config.dart';
import 'package:sport_mobile/http/http_utils.dart';
import 'package:sport_mobile/login/login_router.dart';
import 'package:sport_mobile/login/model/user_model.dart';
import 'package:sport_mobile/net/api.dart';
import 'package:sport_mobile/net/ws_utils.dart';
import 'package:sport_mobile/res/constant.dart';
import 'package:sport_mobile/routers/fluro_navigator.dart';
import 'package:sport_mobile/routers/routers.dart';
import 'package:sport_mobile/util/device_utils.dart';
import 'package:sport_mobile/util/log_utils.dart';
import 'package:sport_mobile/util/theme_utils.dart';
import 'package:sport_mobile/widgets/fractionally_aligned_sized_box.dart';
import 'package:sport_mobile/widgets/load_image.dart';
import 'package:quick_actions/quick_actions.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sp_util/sp_util.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  _SplashPageState createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  int _status = 0;
  StreamSubscription<dynamic>? _subscription;
  Timer? _triggerHealthCheckTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      bool hasValidURL = await initWebsocket();

      if (!hasValidURL) {
        return;
      }

      _connectToWebSocket();
      //等待链接成功
      bool state = await getWSStatus();

      if (state) {
        _initSplash();
      }
      
    });

    if (Device.isAndroid) {
      const QuickActions quickActions = QuickActions();
      quickActions.initialize((String shortcutType) async {});
    }
  }

  Future<bool> getWSStatus() async {
    bool whetherConnected = false;



    while (true) {
      if (WsUtils.getInternalIns() == SocketStatus.socketStatusConnected) {
        whetherConnected = true;

        if (whetherConnected == true && _triggerHealthCheckTimer != null) {
          _triggerHealthCheckTimer?.cancel();
        }

        break;
      }


     await Future.delayed(const Duration(milliseconds: 300));


      print("WsUtils.getInternalIns() WsUtils.getInternalIns() ${WsUtils.getInternalIns()}");

      if (WsUtils.getInternalIns() != SocketStatus.socketStatusConnected && WsUtils.getInternalIns() != SocketStatus.socketStatusConnecting  ) {
        debugPrint("开始执行延迟检测");

        await Future.delayed(const Duration(seconds: 2)).then((_) {
          if (WsUtils.getInternalIns() != SocketStatus.socketStatusConnected) {
            WsUtils.instance.triggerHealthCheck();
          }
        });
      }
    }

    return whetherConnected;
  }

  Future<bool> initWebsocket() async {
    List<String> wsURLList;
    bool? isInit = false;
    await LogUtils.initializeLogFile();

    await PackageInfo.fromPlatform().then((PackageInfo packageInfo) {
      String initRefreshVersionKey =
          "${Constant.initRefreshVersion}-${packageInfo.appName}-${packageInfo.version}";
      isInit = SpUtil.getBool(initRefreshVersionKey, defValue: true);
      if (isInit == null || isInit == true) {
        //说明首次加载
        SpUtil.putBool(initRefreshVersionKey, false);
      }
    });

    //先从本地获取一份文件，防止部分地区用户
    //await ApiConfig.getLocalAPIData();

    //已经改为直接从线上获取配置内容
    isInit = true;
    await ApiConfig.getLinkableXMLAPI(init: isInit!);

    if (Constant.inProduction) {
      wsURLList = await WSSpeedTest.getWSURLList(init: true);
    } else {
      wsURLList = await WSSpeedTest.getWSURLList(init: true);
      //wsURLList.first = "ws://127.0.0.1:7072/ws";
    }

    if (wsURLList.isEmpty) {
      LogUtils.logger.i("没有可用的API");
      LogUtils.uploadLogToServer();
      EasyLoading.showToast("没有可用的API");
      return false;
    }

    String fastURL = await findFastestWebSocket(wsURLList);

  
    HttpUtils.initDio("http://127.0.0.1:7072");

    configWS(
        baseUrl: fastURL,
        urlList: wsURLList,
        connectTimeout: const Duration(seconds: 3),
        platformName: ApiConfig.latestGameName);
    return true;
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  void _initGuide() {
    setState(() {
      _status = 1;
    });
  }

  void _connectToWebSocket() {
    WsUtils.instance;
  }

  void _initSplash() {
    if (SpUtil.getString(Constant.token) != "") {
      Future.delayed(Duration(milliseconds: 300)).then((_) {
        NavigatorUtils.push(context, Routes.home);
      });
    } else {
      _goLogin();
    }
  }

  void _goLogin() {
    NavigatorUtils.push(context, LoginRouter.loginPage, replace: true);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        color: Colors.white,
        child: const FractionallyAlignedSizedBox(
            heightFactor: 0.3,
            widthFactor: 0.33,
            leftFactor: 0.33,
            bottomFactor: 0,
            child: LoadAssetImage('logo')));
  }
}
