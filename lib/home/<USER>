import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:sport_mobile/res/gaps.dart';
import 'package:sport_mobile/widgets/my_app_bar.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart'
    as webview_flutter_android;
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';


import 'dart:async';
import 'package:flutter/foundation.dart';

class WebViewPage extends StatefulWidget {
  const WebViewPage({
    super.key,
    required this.title,
    required this.url,
  });

  final String title;
  final String url;

  @override
  _WebViewPageState createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  late final WebViewController _controller;
  int _progressValue = 0;

  Future<List<String>> _androidFilePicker(
      webview_flutter_android.FileSelectorParams params) async {
    final result = await FilePicker.platform.pickFiles();

    if (result != null && result.files.single.path != null) {
      final file = File(result.files.single.path!);
      return [file.uri.toString()];
    }
    return [];
  }

  void addFileSelectionListener() async {
    if (Platform.isAndroid) {
      final androidController = _controller.platform
          as webview_flutter_android.AndroidWebViewController;
      await androidController.setOnShowFileSelector(_androidFilePicker);
    }
  }

  @override
  void initState() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            if (!mounted) {
              return;
            }
            debugPrint('WebView is loading (progress : $progress%)');
            setState(() {
              _progressValue = progress;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
    addFileSelectionListener();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        appBar: MyAppBar(
          backImgColor: Colors.white,
          centerTitle: widget.title,
        ),
        body: Stack(
          children: [
            WebViewWidget(
              controller: _controller,
            ),
            if (_progressValue != 100)
              LinearProgressIndicator(
                value: _progressValue / 100,
                backgroundColor: Colors.transparent,
                minHeight: 2,
              )
            else
              Gaps.empty,
          ],
        ),
      ),
    );
  }
}



// class InWebViewPage extends StatefulWidget {
//   const InWebViewPage({
//     super.key,
//     required this.title,
//     required this.url,
//   });

//   final String title;
//   final String url;

//   @override
//   _InWebViewPageState createState() => _InWebViewPageState();
// }

// class _InWebViewPageState extends State<InWebViewPage> {
//   //late final WebViewController _controller;
//   InAppWebViewController? webViewController;
//   final GlobalKey webViewKey = GlobalKey();

//     InAppWebViewSettings settings = InAppWebViewSettings(
//       isInspectable: kDebugMode,
//       mediaPlaybackRequiresUserGesture: false,
//       allowsInlineMediaPlayback: true,
//       iframeAllow: "camera; microphone",
//       iframeAllowFullscreen: true);

//   int _progressValue = 0;

//   @override
//   void initState() {
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return PopScope(
//       canPop: true,
//       child: Scaffold(
//         appBar: MyAppBar(
//           backImgColor: Colors.white,
//           centerTitle: widget.title,
//         ),
//         body: Stack(
//           children: [
//             InAppWebView(
//                   key: webViewKey,
//                   initialUrlRequest:
//                       URLRequest(url: WebUri("${widget.url}")),
//                   initialSettings: settings,
//                   onWebViewCreated: (controller) {
//                     webViewController = controller;
//                   },
//                   // onLoadStart: (controller, url) {
//                   //   setState(() {
//                   //     this.url = url.toString();
//                   //     urlController.text = this.url;
//                   //   });
//                   // },
//                   onPermissionRequest: (controller, request) async {
//                     return PermissionResponse(
//                         resources: request.resources,
//                         action: PermissionResponseAction.GRANT);
//                   },
//                   // shouldOverrideUrlLoading:
//                   //     (controller, navigationAction) async {
//                   //   var uri = navigationAction.request.url!;

//                   //   if (![
//                   //     "http",
//                   //     "https",
//                   //     "file",
//                   //     "chrome",
//                   //     "data",
//                   //     "javascript",
//                   //     "about"
//                   //   ].contains(uri.scheme)) {
//                   //     if (await canLaunchUrl(uri)) {
//                   //       // Launch the App
//                   //       await launchUrl(
//                   //         uri,
//                   //       );
//                   //       // and cancel the request
//                   //       return NavigationActionPolicy.CANCEL;
//                   //     }
//                   //   }

//                   //   return NavigationActionPolicy.ALLOW;
//                   // },
//                   // onLoadStop: (controller, url) async {
//                   //   pullToRefreshController?.endRefreshing();
//                   //   setState(() {
//                   //     this.url = url.toString();
//                   //     urlController.text = this.url;
//                   //   });
//                   // },
//                   // onReceivedError: (controller, request, error) {
//                   //   pullToRefreshController?.endRefreshing();
//                   // },
//                   onProgressChanged: (controller, progress) {

//                       if (!mounted) {
//                         return;
//                       }
//                       debugPrint('WebView is loading (progress : $progress%)');
//                       setState(() {
//                         _progressValue = progress;
//                       });


//                   },
//                   // onUpdateVisitedHistory: (controller, url, androidIsReload) {
//                   //   setState(() {
//                   //     this.url = url.toString();
//                   //     urlController.text = this.url;
//                   //   });
//                   // },
//                   // onConsoleMessage: (controller, consoleMessage) {
//                   //   if (kDebugMode) {
//                   //     print(consoleMessage);
//                   //   }
//                   // },
//                 ),
//             if (_progressValue != 100)
//               LinearProgressIndicator(
//                 value: _progressValue / 100,
//                 backgroundColor: Colors.transparent,
//                 minHeight: 2,
//               )
//             else
//               Gaps.empty,
//           ],
//         ),
//       ),
//     );
//   }
// }
