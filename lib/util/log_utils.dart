import 'dart:io';

import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class LogUtils {
  static late File logFile;
  static late Logger logger;
  static DateTime? _lastSent;
  static Duration throttleDuration = const Duration(minutes: 5);

  static Future<void> getDirectoryForLogRecord() async {
    DateTime date = DateTime.now();

    final Directory directory = await getApplicationDocumentsDirectory();

    final String fileName = "${DateFormat("yyyy-MM-dd").format(date)}.txt";
    logFile = File('${directory.path}/${fileName}');

    if (!logFile.existsSync()) {
      logFile.createSync();
    }

    await logFile.writeAsString('开始初始化 \n');

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;

    await logFile.writeAsString('当前版本号为 $version \n', mode: FileMode.append);
  }

  static Future<void> initializeLogFile() async {
    await getDirectoryForLogRecord();
    logger = Logger(
        filter: ProductionFilter(),
        printer: MyPrinter(),
        output: FileOutput(file: File(logFile.path)));
  }

  static Future<void> writeToFile(String message) async {
    try {
      await logFile.writeAsString('$message\n', mode: FileMode.append);
    } catch (e) {
      print('Error writing to file: $e');
    }
  }

  static uploadLogToServer() {
    //将网络日志传送到sentry
    LogUtils.logFile.readAsString().then((content) {
      final DateTime now = DateTime.now();
      if (_lastSent == null || now.difference(_lastSent!) > throttleDuration) {
        Sentry.captureMessage(
          content,
          level: SentryLevel.warning,
          withScope: (scope) {
              scope.setTag('event_category', 'network_error'); // 设置自定义标签
          },
        );
       _lastSent = now;
      }
    });
  }


  static captureMessage(String content){
       Sentry.captureMessage(
          content,
          level: SentryLevel.warning,
          withScope: (scope) {
              scope.setTag('event_category', 'network_error_testing_domain'); // 设置自定义标签
          },
        );
  }


  static Future<void> deleteAndCreateFile() async {
    DateTime date = DateTime.now();
    final Directory directory = await getApplicationDocumentsDirectory();
    final String fileName = "${DateFormat("yyyy-MM-dd").format(date)}.txt";
    logFile = File('${directory.path}/${fileName}');
    await logFile.delete();
    await initializeLogFile();
  }

}

class MyPrinter extends LogPrinter {
  @override
  List<String> log(LogEvent event) {
    return ["${event.time.toString()}: ${event.message}"];
  }
}
