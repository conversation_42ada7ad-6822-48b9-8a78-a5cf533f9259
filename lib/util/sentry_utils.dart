import 'dart:convert';

import 'package:sentry_flutter/sentry_flutter.dart';

class SentryUtils {
  static DateTime? _lastSent;

  //五分钟
  static Duration throttleDuration = const Duration(minutes: 5);

  static List<String> combineLogs = [];

  //频率限制日志,比如频繁的网络检测日志需要控制上报频率
  void logEventRateLimit(String message) {
    final DateTime now = DateTime.now();

    if (_lastSent == null || now.difference(_lastSent!) > throttleDuration) {
      Sentry.captureMessage(
        message,
        level: SentryLevel.warning,
      );
      _lastSent = now;
    }
  }

  static void logEventWithoutInstantly(String message, {bool report = false}) {
    final DateTime now = DateTime.now();



     combineLogs.add("$message \n");
    // if ((_lastSent == null || now.difference(_lastSent!) > throttleDuration) || report == true) {

    //   String jsonMessage =  jsonEncode(combineLogs);

    //   Sentry.captureMessage(
    //     jsonMessage,
    //     level: SentryLevel.warning,
    //    // withScope:
    //   );
    //   _lastSent = now;
    // }

    if (report) {
      String jsonMessage = jsonEncode(combineLogs);

      Sentry.captureMessage(
        jsonMessage,
        level: SentryLevel.warning,
        // withScope:
      );
      _lastSent = now;

      combineLogs = [];
    }
  }
}
