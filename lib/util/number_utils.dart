class NumberUtils {


double processValue(dynamic value) {
  if (value is int) {
    return value.toDouble();  // 将整数转换为浮点数
  } else if (value is double) {
    return value;             // 保持原始浮点数
  } else {
    throw ArgumentError('Invalid value');
  }
}

 static String filterPercent(String? rateProfit) {
    if (rateProfit == null) {
      return '';
    }

    double rateProfitWithDouble = double.parse(rateProfit) * 100;
    String rateProfitWithString = "${rateProfitWithDouble.toStringAsFixed(2)}%";

    return rateProfitWithString;
  }


}
