import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class CustomCachedNetworkImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Duration cacheDuration;

  CustomCachedNetworkImage({
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.cacheDuration = const Duration(days:365), // 默认缓存365天
  });

  @override
  Widget build(BuildContext context) {
    // 自定义缓存管理器
    final customCacheManager = CacheManager(
      Config(
        'customCacheKey', 
        stalePeriod: cacheDuration, // 设置缓存时间
        maxNrOfCacheObjects: 10000,   // 设置最大缓存数量
      ),
    );

    return CachedNetworkImage(
      cacheManager: customCacheManager, // 使用自定义缓存管理器
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) => CircularProgressIndicator(), 
      errorWidget: (context, url, error) => Icon(Icons.error), 
    );
  }
}
