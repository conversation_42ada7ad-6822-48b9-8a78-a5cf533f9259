<mxfile host="65bd71144e">
    <diagram id="havf-pR9Jqj6DYWvbPL7" name="第 1 页">
        <mxGraphModel dx="1888" dy="606" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="3" style="edgeStyle=none;html=1;" parent="1" source="2" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="45" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="2" value="打开APP" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-50" y="60" width="190" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="13" style="edgeStyle=none;html=1;" parent="1" source="4" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="16" style="edgeStyle=none;html=1;" parent="1" source="4" target="30" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="45" y="360" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="4" value="获取配置文件xml地址" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-50" y="200" width="190" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="手机内存" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="310" y="110" width="60" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="14" style="edgeStyle=none;html=1;entryX=1;entryY=0;entryDx=0;entryDy=15;entryPerimeter=0;" parent="1" source="11" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="打包时，将一份文件地址存入手机内存" style="shape=ext;double=1;whiteSpace=wrap;html=1;aspect=fixed;" parent="1" vertex="1">
                    <mxGeometry x="480" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="45" style="edgeStyle=none;html=1;entryX=0.467;entryY=0.111;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="19" target="38" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="从返回的健康的XML地址进行连接" style="shape=ext;double=1;rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="450" y="370" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="32" style="edgeStyle=none;html=1;" parent="1" source="30" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="45" y="590" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="30" value="&lt;meta charset=&quot;utf-8&quot;&gt;&lt;span style=&quot;color: rgb(240, 240, 240); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(42, 37, 47); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;开启对xml配置&lt;/span&gt;&lt;br style=&quot;border-color: rgb(0, 0, 0); color: rgb(240, 240, 240); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(42, 37, 47); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;&lt;span style=&quot;color: rgb(240, 240, 240); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(42, 37, 47); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;地址网络检测&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-32.5" y="370" width="155" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="34" style="edgeStyle=none;html=1;" parent="1" source="33" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="440" y="430" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="35" value="成功" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="34" vertex="1" connectable="0">
                    <mxGeometry x="-0.0011" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="33" value="网络是否&lt;br&gt;检查成功" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-37.5" y="585" width="165" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="失败，5秒后开始重新对xml地址检测" style="edgeStyle=segmentEdgeStyle;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="33" target="30" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="-100" y="620" as="sourcePoint"/>
                        <mxPoint x="-60" y="400" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="-37" y="620"/>
                            <mxPoint x="-142" y="620"/>
                            <mxPoint x="-142" y="400"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="40" style="edgeStyle=none;html=1;entryX=1;entryY=0.75;entryDx=0;entryDy=0;exitX=0.998;exitY=0.496;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="38" target="19" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="740" y="585" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="770" y="580"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="41" value="链接失败，5秒开始重新获取" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="40" vertex="1" connectable="0">
                    <mxGeometry x="-0.596" y="-4" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="46" style="edgeStyle=none;html=1;entryX=0.5;entryY=0.167;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="38" target="43" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="47" value="链接成功" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="46" vertex="1" connectable="0">
                    <mxGeometry x="-0.1761" y="-2" relative="1" as="geometry">
                        <mxPoint y="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="38" value="对websocke地址&lt;br&gt;进行链接" style="html=1;whiteSpace=wrap;aspect=fixed;shape=isoRectangle;" parent="1" vertex="1">
                    <mxGeometry x="440" y="540" width="150" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="链接完毕，进行通信" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="450" y="710" width="120" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>